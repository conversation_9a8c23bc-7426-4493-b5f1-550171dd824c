DB_NAME='local'
DB_USER='root'
DB_PASSWORD='root'
DB_HOST='localhost:{DB_SOCKET}'
DB_PREFIX='{DB_PREFIX}'

DRUPAL_DB_NAME='drupal'
DRUPAL_DB_USER='root'
DRUPAL_DB_PASSWORD='root'
DRUPAL_DB_HOST='localhost:/Users/<USER>/Library/Application Support/Local/run/xxxxx/mysql/mysqld.sock'

# Optionally, you can use a data source name (DSN)
# When using a DSN, you can remove the DB_NAME, DB_USER, DB_PASSWORD, and DB_HOST variables
# DATABASE_URL='mysql://database_user:database_password@database_host:database_port/database_name'

WP_ENV='development'
WP_HOME='{SITE_URL}'
WP_SITEURL="${WP_HOME}/wp"

SSL_PATH='{SSL_PATH}'
SSL_KEY='local.lesanimals.digital.key'
SSL_CERT='local.lesanimals.digital.crt'

DEPLOY_HOST_PREPROD='ssh.example.com'
DEPLOY_PATH_PREPROD='/preprod/web/app/themes/lesanimals/'
DEPLOY_USER_PREPROD='username'

DEPLOY_HOST_PROD='ssh.example.com'
DEPLOY_PATH_PROD='/prod/web/app/themes/lesanimals/'
DEPLOY_USER_PROD='username'

# Elastipress
ES_SHIELD='login:password'

# Vimeo
VIMEO_CLIENT_ID='vimeo_client_id'
VIMEO_CLIENT_SECRET='vimeo_client_secret'
VIMEO_ACCESS_TOKEN='vimeo_access_token'

# Specify optional debug.log path
# WP_DEBUG_LOG='/path/to/debug.log'

# Generated from a dedicated tool
{SALT_KEYS}

# Plugins licences
WPMDB_LICENCE='{WPMDB_LICENCE}'
ACF_PRO_LICENSE='{ACF_PRO_LICENSE}'
WP_ROCKET_EMAIL='{WP_ROCKET_EMAIL}'
WP_ROCKET_KEY='{WP_ROCKET_KEY}'
SEOPRESS_LICENSE_KEY='{SEOPRESS_LICENSE_KEY}'
POLYLANG_PRO_KEY="{POLYLANG_PRO_KEY}"
POLYLANG_PRO_URL="{POLYLANG_PRO_URL}"
ACFE_PRO_KEY="{ACFE_PRO_KEY}"
ACFE_PRO_URL="{ACFE_PRO_URL}"
