# Various
.DS_Store
/.idea/**/*.*
!/.idea/.name
!/.idea/codeStyles/codeStyleConfig.xml
!/.idea/codeStyles/Project.xml
!/.idea/inspectionProfiles/Project_Default.xml
!/.idea/jsLinters/eslint.xml
!/.idea/modules.xml
!/.idea/*.iml
!/.idea/php.xml
!/.idea/scopes/Theme.xml
!/.idea/stylesheetLinters/stylelint.xml
!/.idea/symfony2.xml
!/.idea/watcherTasks.xml
!/.idea/webResources.xml

/wp-config.php
/wp-includes/

# Application
web/app/cache/*
web/app/languages/*
web/app/plugins/*
!web/app/plugins/.gitkeep
web/app/mu-plugins/*/
web/app/themes/*
web/app/upgrade
web/app/uploads/*
web/app/uploads/**/*-default.*
web/app/uploads/**/*-center.*
web/app/uploads/**/*-*x*.*
!web/app/uploads/
!web/app/uploads/.gitkeep

# WP Rocket
web/app/wp-rocket-config/
web/app/advanced-cache.php
web/app/wflogs/
web/.user.ini

# WordPress
web/wp
web/.htaccess

# Logs
*.log

# Dotenv
auth.json
.env
.env.*
!.env.example

# Composer
/vendor

# WP-CLI
wp-cli.local.yml

# Theme Les Animals
!web/app/themes/lesanimals
web/app/themes/lesanimals/app/assets/dist/
web/app/themes/lesanimals/vendor/
web/app/themes/lesanimals/node_modules/
ide-twig.json
/web/app/wflogs
