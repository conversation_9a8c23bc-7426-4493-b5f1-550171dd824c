# Full installation instructions with MacOS*, local* and PhpStorm**

## Table of contents

* [Dependencies](INSTALLATION.md#-dependencies)
* [One-time setup](INSTALLATION.md#-one-time-setup-)
* [Install the Local Add-on for <PERSON><PERSON><PERSON>](INSTALLATION.md#-install-the-local-add-on-for-grahou)
* [Generate the SSL](INSTALLATION.md#-generate-the-ssl)
* [Plugins](INSTALLATION.md#-plugins)
* [Configure PhpStorm](INSTALLATION.md#-configure-phpstorm)
* [Known issues](INSTALLATION.md#-known-issues)

> *installation process should work on Windows but might need more testing  
> **will work with Mamp or any other local server app, with a manual installation not detailed there  
> ***will work with other code editors, not detailed there

## 📀 Dependencies

* local • [website](https://localwp.com/) ↗
* Node • [brew](https://formulae.brew.sh/formula/node#default) ↗
* Composer • [brew](https://formulae.brew.sh/formula/composer#default) ↗
* WP-CLI • [brew](https://formulae.brew.sh/formula/wp-cli#default) ↗
* Bash 5 • [brew](https://formulae.brew.sh/formula/bash) ↗
* PHP 8.1 • [brew](https://formulae.brew.sh/formula/php@8.1) ↗
* Github CLI • [brew](https://formulae.brew.sh/formula/gh) ↗

### Installation snippets for macOS

```bash
brew install node
brew install composer
brew install wp-cli
brew install bash
brew install php@8.1
brew install gh
brew install gnu-sed
```

## 🚨 One-time setup 

Make sure to have this constants in your .profile env file:

```
# Grahou variables
export SSL_PATH="/Users/<USER>/www/ssl/"
export WP_ADMIN_USERNAME="you"
export WP_ADMIN_EMAIL="<EMAIL>"
export WPMDB_LICENCE=""
export ACF_PRO_LICENSE=""
export WP_ROCKET_EMAIL=""
export WP_ROCKET_KEY=""
export SEOPRESS_LICENSE_KEY=""
```

Execute this command in your terminal to setup github credentials

```bash
gh auth login
```

For Windows, take a look at [Chocolatey](https://chocolatey.org/) ↗

## ⬇️ Install the [Local Add-on for Grahou](https://github.com/lesanimals/local-addon-grahou)

### Create a new site

![Create a local site](img/installation-01.png)

### Create from a blueprint

![Create from a blueprint](img/installation-02.png)

### Then the rest is pretty straightforward

![Next steps](img/installation-03.png)

## 🔐 Generate the SSL

### In your own SSL for browser sync

Open your SSL folder (ex: `/Users/<USER>/www/ssl`)

Open the file `local.lesanimals.digital.conf`

Add the local site URL at the end, increment the DNS number by one (ex: `DNS.92 = exemple-project.local`)

Launch the script `generate-certificate` press enter as much as needed

Open the file `local.lesanimals.digital.crt` with the `Trousseaux.app`, trust the latest `Les Animals` certificate.
Delete the previous one if needed and close.

### In local

Click the `Trust` button next to the `SSL` label in your site configuration.

Open the `Trousseaux.app` and trust the latest certificate (marked as red)

## 💫 Plugins

ACF Extended Pro can't be installed with composer yet, you need to download it and install it manually

↗ [ACF Extended Pro website](https://www.acf-extended.com/account)

## 🛠️ Configure PhpStorm

Follow this instructions:

[PHPSTORM.md](PHPSTORM.md)

## ☠️ Known issues

Your `.profile` variables could override some Bedrock `.env` variables config<br>
Be sure not to have any of this that could be overide:
* `DB_NAME`
* `DB_USER`
* `DB_PASSWORD`
* `DB_HOST`
* `DB_PREFIX`
* `WP_ENV`
* `WP_HOME`
* `WP_SITEURL`
