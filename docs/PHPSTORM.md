# 🧑‍💻 PhpStorm

## 📖 Table of content

* [Migrating the configuration from a project to another](#settings-migration-from-one-project-to-another)
* [Screenshot of some panel configurations](#-some-panels-configuration)

## ♻️ Migrate settings from one project to another

Copy and paste the .idea file from your previous project to the freshly created one

`{project_name_old}` refers to the name of the previous project.  
`{project_name_new}` refers to the name of the new project.

> We assume that `{project_name_old}` and `{project_name_new}` are the same as their root folders name.

* Edit the file `{project_name_old}.iml`, rename it to `{project_name_new}.iml`
* Open the file `name`, and inside you should find `{project_name_old}`. Change it to `{project_name_new}`
* Open the file `workspace.xml`.
	* Find and replace every instance of `{project_name_old}` to `{project_name_new}`.
	* Find the line starting with `<component name="ProjectId" id="`. Change the id value to `{project_name_new}`.
* Open the file `modules.xml`.
	* Find and replace every instance of `{project_name_old}` to `{project_name_new}`.

## ⚙️ Some panel configurations

### Enable EditorConfig support

![.editorconfig](./img/0.0-editorconfig.png)

### Scope

Pattern : `file[grahou]:web/app/themes/lesanimals//*||file:.env||file:web/app/debug.log`

![Scope](./img/1.0-scopes.png)

### Directories

![Directories](./img/2.0-directories.png)

### PHP

> 💡 To get the path of your PHP version, open the terminal and type the following command :
>
> - bash : `type -a php`
> - zsh : `where php`
>
> Then, paste it in the `CLI Interpreter` input

> It is fine to have multiple php versions<br>
> Just set the right one for your project

![PHP 1](./img/3.0-php.png)

![PHP 2](./img/3.1-php.png)

### PHP - Quality Tools

![PHP - Quality Tools](./img/4.0-php-quality_tools.png)

### PHP - CodeSniffer

![PHP CodeSniffer 1](./img/5.0-php-codesniffer.png)

![PHP CodeSniffer 2](./img/5.1-php-codesniffer.png)

### ESLint

![ESLint](./img/6.0-eslint.png)

### Stylelint

![Stylelint](./img/7.0-stylelint.png)

### Actions on save

> Take care of the `Rearrange code` functionality<br>
> You might want to remove it for PHP and keep it for scss and javascript only

![Actions on save](./img/8.0-actions-on-save.png)

### Rearrange code - Javascript

![Rearrange code - Javascript](./img/9.1-rearrange-js.png)

### Rearrange code - SCSS

> [The properties order are set in the .editorconfig file](../.editorconfig)

![Rearrange code - SCSS](./img/9.2-rearrange-scss.png)

### Rearrange code - PHP

![Rearrange code - PHP](./img/9.3-rearrange-php.png)
