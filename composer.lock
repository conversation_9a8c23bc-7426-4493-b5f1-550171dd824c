{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "c789ec865c9162f12219a9d5b1cc16d9", "packages": [{"name": "berlindb/core", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/berlindb/core.git", "reference": "7dcddaddcffb69c58800d2fb3f6f169791cab1f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/berlindb/core/zipball/7dcddaddcffb69c58800d2fb3f6f169791cab1f7", "reference": "7dcddaddcffb69c58800d2fb3f6f169791cab1f7", "shasum": ""}, "type": "library", "autoload": {"psr-4": {"BerlinDB\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A collection of PHP classes and functions that aims to provide an ORM-like experience and interface to WordPress database tables.", "support": {"issues": "https://github.com/berlindb/core/issues", "source": "https://github.com/berlindb/core/tree/2.0.1"}, "time": "2022-03-10T21:12:11+00:00"}, {"name": "cloudflare/cf-ip-rewrite", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/cloudflare/cf-ip-rewrite.git", "reference": "****************************************"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cloudflare/cf-ip-rewrite/zipball/****************************************", "reference": "****************************************", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8", "squizlabs/php_codesniffer": "2.*"}, "type": "library", "autoload": {"psr-0": {"CloudFlare\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CloudFlare", "homepage": "http://www.cloudflare.com/"}], "description": "Library to rewrite CloudFlare IP Addresses to the end-user IP address", "homepage": "https://github.com/cloudflare/cf-ip-rewrite", "keywords": ["cloudflare", "ip rewrite"], "support": {"issues": "https://github.com/cloudflare/cf-ip-rewrite/issues", "source": "https://github.com/cloudflare/cf-ip-rewrite/tree/master"}, "time": "2017-10-10T15:44:33+00:00"}, {"name": "composer/installers", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "12fb2dfe5e16183de69e784a7b84046c43d97e8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/12fb2dfe5e16183de69e784a7b84046c43d97e8e", "reference": "12fb2dfe5e16183de69e784a7b84046c43d97e8e", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": "^7.2 || ^8.0"}, "require-dev": {"composer/composer": "^1.10.27 || ^2.7", "composer/semver": "^1.7.2 || ^3.4.0", "phpstan/phpstan": "^1.11", "phpstan/phpstan-phpunit": "^1", "symfony/phpunit-bridge": "^7.1.1", "symfony/process": "^5 || ^6 || ^7"}, "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "2.x-dev"}, "plugin-modifies-install-path": true}, "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "concreteCMS", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "matomo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "tastyigniter", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v2.3.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-06-24T20:46:46+00:00"}, {"name": "composer/semver", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-09-19T14:15:21+00:00"}, {"name": "deliciousbrains-plugin/wp-migrate-db-pro", "version": "2.7.2", "dist": {"type": "zip", "url": "https://composer.deliciousbrains.com/?wc-api=delicious-brains&request=composer_download&package=wp-migrate-db-pro&version=2.7.2&composer_key=2631864A9B3BEF79B036345C6A87E763"}, "require": {"composer/installers": "~1.0 || ~2.0"}, "type": "wordpress-plugin"}, {"name": "giggsey/libphonenumber-for-php", "version": "8.13.55", "source": {"type": "git", "url": "https://github.com/giggsey/libphonenumber-for-php.git", "reference": "6e28b3d53cf96d7f41c83d9b80b6021ecbd00537"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/libphonenumber-for-php/zipball/6e28b3d53cf96d7f41c83d9b80b6021ecbd00537", "reference": "6e28b3d53cf96d7f41c83d9b80b6021ecbd00537", "shasum": ""}, "require": {"giggsey/locale": "^2.0", "php": "^7.4|^8.0", "symfony/polyfill-mbstring": "^1.17"}, "replace": {"giggsey/libphonenumber-for-php-lite": "self.version"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.64", "pear/pear-core-minimal": "^1.10", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.7", "phing/phing": "^3.0", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^9.6", "symfony/console": "^v5.2", "symfony/var-exporter": "^5.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"libphonenumber\\": "src/"}, "exclude-from-classmap": ["/src/data/", "/src/carrier/data/", "/src/geocoding/data/", "/src/timezone/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "PHP Port of Google's libphonenumber", "homepage": "https://github.com/giggsey/libphonenumber-for-php", "keywords": ["geocoding", "geolocation", "libphonenumber", "mobile", "phonenumber", "validation"], "support": {"issues": "https://github.com/giggsey/libphonenumber-for-php/issues", "source": "https://github.com/giggsey/libphonenumber-for-php"}, "time": "2025-02-14T08:14:08+00:00"}, {"name": "giggsey/locale", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/giggsey/Locale.git", "reference": "a5c65ea3c2630f27ccb78977990eefbee6dd8f97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/Locale/zipball/a5c65ea3c2630f27ccb78977990eefbee6dd8f97", "reference": "a5c65ea3c2630f27ccb78977990eefbee6dd8f97", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "require-dev": {"ext-json": "*", "friendsofphp/php-cs-fixer": "^3.64", "pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.7", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^8.5|^9.5", "symfony/console": "^5.0|^6.0", "symfony/filesystem": "^5.0|^6.0", "symfony/finder": "^5.0|^6.0", "symfony/process": "^5.0|^6.0", "symfony/var-exporter": "^5.2|^6.0"}, "type": "library", "autoload": {"psr-4": {"Giggsey\\Locale\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "Locale functions required by libphonenumber-for-php", "support": {"issues": "https://github.com/giggsey/Locale/issues", "source": "https://github.com/giggsey/Locale/tree/2.7.0"}, "time": "2024-11-04T11:18:07+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/3ba905c11371512af9d9bdd27d99b782216b6945", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2024-07-20T21:45:45+00:00"}, {"name": "ircmaxell/security-lib", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/ircmaxell/SecurityLib.git", "reference": "f3db6de12c20c9bcd1aa3db4353a1bbe0e44e1b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ircmaxell/SecurityLib/zipball/f3db6de12c20c9bcd1aa3db4353a1bbe0e44e1b5", "reference": "f3db6de12c20c9bcd1aa3db4353a1bbe0e44e1b5", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"mikey179/vfsstream": "1.1.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"SecurityLib": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://blog.ircmaxell.com"}], "description": "A Base Security Library", "homepage": "https://github.com/ircmaxell/SecurityLib", "support": {"issues": "https://github.com/ircmaxell/SecurityLib/issues", "source": "https://github.com/ircmaxell/SecurityLib/tree/master"}, "time": "2015-03-20T14:31:23+00:00"}, {"name": "junaidbhura/acf-extended-pro", "version": "0.9.1", "dist": {"type": "zip", "url": "https://www.acf-extended.com/"}, "require": {"junaidbhura/composer-wp-pro-plugins": "*"}, "type": "wordpress-plugin"}, {"name": "juna<PERSON><PERSON><PERSON>a/composer-wp-pro-plugins", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/junaidbhura/composer-wp-pro-plugins.git", "reference": "4ec0a5892076b763a2d283e28c180877cff943e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/junaidbhura/composer-wp-pro-plugins/zipball/4ec0a5892076b763a2d283e28c180877cff943e1", "reference": "4ec0a5892076b763a2d283e28c180877cff943e1", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "composer/semver": "^1.0 || ^2.0 || ^3.0", "vlucas/phpdotenv": "^3.0 || ^4.0 || ^5.0"}, "type": "composer-plugin", "extra": {"class": "Junaidbhura\\Composer\\WPProPlugins\\Installer", "plugin-modifies-downloads": true}, "autoload": {"psr-4": {"Junaidbhura\\Composer\\WPProPlugins\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/junaidbhura/composer-wp-pro-plugins/issues", "source": "https://github.com/junaidbhura/composer-wp-pro-plugins/tree/1.8.0"}, "time": "2023-03-14T20:05:41+00:00"}, {"name": "junaidbhura/polylang-pro", "version": "3.6.6", "dist": {"type": "zip", "url": "https://www.polylang.pro/"}, "require": {"junaidbhura/composer-wp-pro-plugins": "*"}, "type": "wordpress-plugin"}, {"name": "oscarotero/env", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/oscarotero/env.git", "reference": "9f7d85cc6890f06a65bad4fe0077c070d596e4a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/oscarotero/env/zipball/9f7d85cc6890f06a65bad4fe0077c070d596e4a4", "reference": "9f7d85cc6890f06a65bad4fe0077c070d596e4a4", "shasum": ""}, "require": {"ext-ctype": "*", "php": ">=7.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "phpunit/phpunit": ">=7.0"}, "type": "library", "autoload": {"files": ["src/env_function.php"], "psr-4": {"Env\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "description": "Simple library to consume environment variables", "homepage": "https://github.com/oscarotero/env", "keywords": ["env"], "support": {"email": "<EMAIL>", "issues": "https://github.com/oscarotero/env/issues", "source": "https://github.com/oscarotero/env/tree/v2.1.1"}, "time": "2024-12-03T01:02:28+00:00"}, {"name": "paragonie/random-lib", "version": "v2.0.3", "source": {"type": "git", "url": "https://github.com/paragonie/RandomLib.git", "reference": "556bb9c3b70c5e9bf74838fbd22c989c633f5d52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/RandomLib/zipball/556bb9c3b70c5e9bf74838fbd22c989c633f5d52", "reference": "556bb9c3b70c5e9bf74838fbd22c989c633f5d52", "shasum": ""}, "require": {"ircmaxell/security-lib": "^1.1", "paragonie/random_compat": "^2|~9.99", "paragonie/sodium_compat": "^1|^2", "php": ">=5.3.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "^1.11", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^4.8 || >=5.0.0 <5.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-0": {"RandomLib": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://blog.ircmaxell.com"}], "description": "A Library For Generating Secure Random Numbers", "homepage": "https://github.com/ircmaxell/RandomLib", "keywords": ["cryptography", "random", "random-numbers", "random-strings"], "support": {"source": "https://github.com/paragonie/RandomLib/tree/v2.0.3"}, "time": "2024-04-21T01:17:05+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "paragonie/sodium_compat", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/paragonie/sodium_compat.git", "reference": "a673d5f310477027cead2e2f2b6db5d8368157cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/sodium_compat/zipball/a673d5f310477027cead2e2f2b6db5d8368157cb", "reference": "a673d5f310477027cead2e2f2b6db5d8368157cb", "shasum": ""}, "require": {"php": "^8.1", "php-64bit": "*"}, "require-dev": {"phpunit/phpunit": "^7|^8|^9", "vimeo/psalm": "^4|^5"}, "suggest": {"ext-sodium": "Better performance, password hashing (Argon2i), secure memory management (memzero), and better security."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"files": ["autoload.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Pure PHP implementation of libsodium; uses the PHP extension if it exists", "keywords": ["Authentication", "BLAKE2b", "ChaCha20", "ChaCha20-Poly1305", "Chapoly", "Curve25519", "Ed25519", "EdDSA", "Edwards-curve Digital Signature Algorithm", "Elliptic Curve <PERSON>-<PERSON><PERSON>", "Poly1305", "Pure-PHP cryptography", "RFC 7748", "RFC 8032", "Salpoly", "Salsa20", "X25519", "XChaCha20-Poly1305", "XSalsa20-Poly1305", "Xchacha20", "Xsalsa20", "aead", "cryptography", "ecdh", "elliptic curve", "elliptic curve cryptography", "encryption", "libsodium", "php", "public-key cryptography", "secret-key cryptography", "side-channel resistant"], "support": {"issues": "https://github.com/paragonie/sodium_compat/issues", "source": "https://github.com/paragonie/sodium_compat/tree/v2.1.0"}, "time": "2024-09-04T12:51:01+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.3", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/e3fac8b24f56113f7cb96af14958c0dd16330f54", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2024-07-20T21:41:07+00:00"}, {"name": "rbdwllr/wordpress-salts-generator", "version": "0.2.0", "source": {"type": "git", "url": "https://github.com/RobDWaller/wordpress-salts-generator.git", "reference": "f55f34ee3243ec81215a05e62c4e802210cecdeb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RobDWaller/wordpress-salts-generator/zipball/f55f34ee3243ec81215a05e62c4e802210cecdeb", "reference": "f55f34ee3243ec81215a05e62c4e802210cecdeb", "shasum": ""}, "require": {"paragonie/random-lib": "2.0.*", "php": ">=7.0.0"}, "require-dev": {"phpmd/phpmd": "2.6.*", "phpstan/phpstan": "0.9.*", "phpunit/phpunit": "6.5.*"}, "bin": ["wpsalts"], "type": "library", "autoload": {"psr-4": {"WPSalts\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A key and salts generator for WordPress that works with both wp-config and .env files.", "keywords": ["cli", "keys", "salts", "wordpress"], "support": {"issues": "https://github.com/RobDWaller/wordpress-salts-generator/issues", "source": "https://github.com/RobDWaller/wordpress-salts-generator/tree/master"}, "time": "2019-02-11T12:15:30+00:00"}, {"name": "roots/bedrock-autoloader", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/roots/bedrock-autoloader.git", "reference": "f508348a3365ab5ce7e045f5fd4ee9f0a30dd70f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roots/bedrock-autoloader/zipball/f508348a3365ab5ce7e045f5fd4ee9f0a30dd70f", "reference": "f508348a3365ab5ce7e045f5fd4ee9f0a30dd70f", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"10up/wp_mock": "^0.4.2", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"Roots\\Bedrock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/foxaii"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/swalkinshaw"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/austinpray"}], "description": "An autoloader that enables standard plugins to be required just like must-use plugins", "keywords": ["autoloader", "bedrock", "mu-plugin", "must-use", "plugin", "wordpress"], "support": {"forum": "https://discourse.roots.io/", "issues": "https://github.com/roots/bedrock-autoloader/issues", "source": "https://github.com/roots/bedrock-autoloader/tree/1.0.4"}, "funding": [{"url": "https://github.com/roots", "type": "github"}, {"url": "https://www.patreon.com/rootsdev", "type": "patreon"}], "time": "2020-12-04T15:59:12+00:00"}, {"name": "roots/bedrock-disallow-indexing", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/roots/bedrock-disallow-indexing.git", "reference": "6c28192e17cb9e02a5c0c99691a18552b85e1615"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roots/bedrock-disallow-indexing/zipball/6c28192e17cb9e02a5c0c99691a18552b85e1615", "reference": "6c28192e17cb9e02a5c0c99691a18552b85e1615", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "wordpress-muplugin", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/retlehs"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/swalkinshaw"}, {"name": "QWp6t", "email": "<EMAIL>", "homepage": "https://github.com/qwp6t"}], "description": "Disallow indexing of your site on non-production environments", "keywords": ["wordpress"], "support": {"forum": "https://discourse.roots.io/", "issues": "https://github.com/roots/bedrock-disallow-indexing/issues", "source": "https://github.com/roots/bedrock-disallow-indexing/tree/2.0.0"}, "funding": [{"url": "https://github.com/roots", "type": "github"}, {"url": "https://www.patreon.com/rootsdev", "type": "patreon"}], "time": "2020-05-20T01:25:07+00:00"}, {"name": "roots/wordpress", "version": "6.7.2", "source": {"type": "git", "url": "https://github.com/roots/wordpress.git", "reference": "c53e4173d239dcaf8889f9f84c0b827a0cf643e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roots/wordpress/zipball/c53e4173d239dcaf8889f9f84c0b827a0cf643e9", "reference": "c53e4173d239dcaf8889f9f84c0b827a0cf643e9", "shasum": ""}, "require": {"roots/wordpress-core-installer": "^1.0.0", "roots/wordpress-no-content": "self.version"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT", "GPL-2.0-or-later"], "description": "WordPress is open source software you can use to create a beautiful website, blog, or app.", "homepage": "https://wordpress.org/", "keywords": ["blog", "cms", "wordpress"], "support": {"issues": "https://github.com/roots/wordpress/issues", "source": "https://github.com/roots/wordpress/tree/6.7.2"}, "funding": [{"url": "https://github.com/roots", "type": "github"}], "time": "2024-12-15T16:32:37+00:00"}, {"name": "roots/wordpress-core-installer", "version": "1.100.0", "source": {"type": "git", "url": "https://github.com/roots/wordpress-core-installer.git", "reference": "73f8488e5178c5d54234b919f823a9095e2b1847"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roots/wordpress-core-installer/zipball/73f8488e5178c5d54234b919f823a9095e2b1847", "reference": "73f8488e5178c5d54234b919f823a9095e2b1847", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.6.0"}, "conflict": {"composer/installers": "<1.0.6"}, "replace": {"johnpbloch/wordpress-core-installer": "*"}, "require-dev": {"composer/composer": "^1.0 || ^2.0", "phpunit/phpunit": ">=5.7.27"}, "type": "composer-plugin", "extra": {"class": "Roots\\Composer\\WordPressCorePlugin"}, "autoload": {"psr-4": {"Roots\\Composer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Roots", "email": "<EMAIL>"}], "description": "A custom installer to handle deploying WordPress with composer", "keywords": ["wordpress"], "support": {"issues": "https://github.com/roots/wordpress-core-installer/issues", "source": "https://github.com/roots/wordpress-core-installer/tree/master"}, "funding": [{"url": "https://github.com/roots", "type": "github"}, {"url": "https://www.patreon.com/rootsdev", "type": "patreon"}], "time": "2020-08-20T00:27:30+00:00"}, {"name": "roots/wordpress-no-content", "version": "6.7.2", "source": {"type": "git", "url": "https://github.com/WordPress/WordPress.git", "reference": "6.7.2"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/release/wordpress-6.7.2-no-content.zip", "shasum": "c33ca276601dee0ddf1e80d3e66403929a696e63"}, "require": {"php": ">= 7.2.24"}, "provide": {"wordpress/core-implementation": "6.7.2"}, "suggest": {"ext-curl": "Performs remote request operations.", "ext-dom": "Used to validate Text Widget content and to automatically configuring IIS7+.", "ext-exif": "Works with metadata stored in images.", "ext-fileinfo": "Used to detect mimetype of file uploads.", "ext-hash": "Used for hashing, including passwords and update packages.", "ext-imagick": "Provides better image quality for media uploads.", "ext-json": "Used for communications with other servers.", "ext-libsodium": "Validates Signatures and provides securely random bytes.", "ext-mbstring": "Used to properly handle UTF8 text.", "ext-mysqli": "Connects to MySQL for database interactions.", "ext-openssl": "Permits SSL-based connections to other hosts.", "ext-pcre": "Increases performance of pattern matching in code searches.", "ext-xml": "Used for XML parsing, such as from a third-party site.", "ext-zip": "Used for decompressing Plugins, Themes, and WordPress update packages."}, "type": "wordpress-core", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "WordPress Community", "homepage": "https://wordpress.org/about/"}], "description": "WordPress is open source software you can use to create a beautiful website, blog, or app.", "homepage": "https://wordpress.org/", "keywords": ["blog", "cms", "wordpress"], "support": {"docs": "https://developer.wordpress.org/", "forum": "https://wordpress.org/support/", "irc": "irc://irc.freenode.net/wordpress", "issues": "https://core.trac.wordpress.org/", "rss": "https://wordpress.org/news/feed/", "source": "https://core.trac.wordpress.org/browser", "wiki": "https://codex.wordpress.org/"}, "funding": [{"url": "https://wordpressfoundation.org/donate/", "type": "other"}], "time": "2025-02-11T16:29:31+00:00"}, {"name": "roots/wp-config", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/roots/wp-config.git", "reference": "37c38230796119fb487fa03346ab0706ce6d4962"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roots/wp-config/zipball/37c38230796119fb487fa03346ab0706ce6d4962", "reference": "37c38230796119fb487fa03346ab0706ce6d4962", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5.7", "roave/security-advisories": "dev-master", "squizlabs/php_codesniffer": "^3.3"}, "type": "library", "autoload": {"psr-4": {"Roots\\WPConfig\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Collect configuration values and safely define() them", "support": {"issues": "https://github.com/roots/wp-config/issues", "source": "https://github.com/roots/wp-config/tree/master"}, "time": "2018-08-10T14:18:38+00:00"}, {"name": "roots/wp-password-bcrypt", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/roots/wp-password-bcrypt.git", "reference": "15f0d8919fb3731f79a0cf2fb47e1baecb86cb26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roots/wp-password-bcrypt/zipball/15f0d8919fb3731f79a0cf2fb47e1baecb86cb26", "reference": "15f0d8919fb3731f79a0cf2fb47e1baecb86cb26", "shasum": ""}, "require": {"php": ">=5.6.0"}, "require-dev": {"brain/monkey": "^2.6", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "mockery/mockery": "^1.4", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "<= 9.3", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "autoload": {"files": ["wp-password-bcrypt.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/swalkinshaw"}, {"name": "QWp6t", "homepage": "https://github.com/qwp6t"}, {"name": "<PERSON>", "homepage": "https://github.com/log1x"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://janpingel.com"}], "description": "WordPress plugin which replaces wp_hash_password and wp_check_password's phpass hasher with PHP 5.5's password_hash and password_verify using bcrypt.", "homepage": "https://roots.io/plugins/wp-password-bcrypt", "keywords": ["bcrypt", "passwords", "wordpress"], "support": {"forum": "https://discourse.roots.io/", "issues": "https://github.com/roots/wp-password-bcrypt/issues", "source": "https://github.com/roots/wp-password-bcrypt/tree/1.1.0"}, "funding": [{"url": "https://github.com/roots", "type": "github"}, {"url": "https://www.patreon.com/rootsdev", "type": "patreon"}], "time": "2021-10-31T01:18:58+00:00"}, {"name": "symfony/css-selector", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "cb23e97813c5837a041b73a6d63a9ddff0778f5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/cb23e97813c5837a041b73a6d63a9ddff0778f5e", "reference": "cb23e97813c5837a041b73a6d63a9ddff0778f5e", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.6.1", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/a59a13791077fe3d44f90e7133eb68e7d22eaff2", "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.3", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-filter": "*", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "5.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2024-07-20T21:52:34+00:00"}, {"name": "voku/simple_html_dom", "version": "4.8.10", "source": {"type": "git", "url": "https://github.com/voku/simple_html_dom.git", "reference": "716822ed52ed3a1881542be07a786270de390e99"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/simple_html_dom/zipball/716822ed52ed3a1881542be07a786270de390e99", "reference": "716822ed52ed3a1881542be07a786270de390e99", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-simplexml": "*", "php": ">=7.0.0", "symfony/css-selector": "~3.0 || ~4.0 || ~5.0 || ~6.0 || ~7.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"voku/portable-utf8": "If you need e.g. UTF-8 fixed output."}, "type": "library", "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "dimabdc", "email": "<EMAIL>", "homepage": "https://github.com/dimabdc", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://www.moelleken.org/", "role": "Fork-Maintainer"}], "description": "Simple HTML DOM package.", "homepage": "https://github.com/voku/simple_html_dom", "keywords": ["HTML Parser", "dom", "php dom"], "support": {"issues": "https://github.com/voku/simple_html_dom/issues", "source": "https://github.com/voku/simple_html_dom/tree/4.8.10"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/simple_html_dom", "type": "tidelift"}], "time": "2024-07-03T16:05:14+00:00"}, {"name": "wp-media/apply-filters-typed", "version": "v1.2", "source": {"type": "git", "url": "https://github.com/wp-media/apply-filters-typed.git", "reference": "d8f5bca83e85196d1f01d92283b63ef6f886ac4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-media/apply-filters-typed/zipball/d8f5bca83e85196d1f01d92283b63ef6f886ac4d", "reference": "d8f5bca83e85196d1f01d92283b63ef6f886ac4d", "shasum": ""}, "require-dev": {"phpcompatibility/phpcompatibility-wp": "^2.0", "phpstan/extension-installer": "^1.4", "szepeviktor/phpstan-wordpress": "^1.3", "wp-coding-standards/wpcs": "^3", "wp-media/phpunit": "^3"}, "type": "library", "autoload": {"files": ["functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "authors": [{"name": "WP Media", "email": "<EMAIL>"}], "description": "Library for usage of WordPress filters in a safe-type way", "support": {"issues": "https://github.com/wp-media/apply-filters-typed/issues", "source": "https://github.com/wp-media/apply-filters-typed/tree/v1.2"}, "time": "2024-09-16T12:21:47+00:00"}, {"name": "wp-media/plugin-family", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/wp-media/plugin-family.git", "reference": "83714bdfb7d98d4fe74781844c325be909db13a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-media/plugin-family/zipball/83714bdfb7d98d4fe74781844c325be909db13a4", "reference": "83714bdfb7d98d4fe74781844c325be909db13a4", "shasum": ""}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/phpcompatibility-wp": "^2.0", "phpstan/extension-installer": "^1.4", "szepeviktor/phpstan-wordpress": "^1.3", "wp-coding-standards/wpcs": "^3", "wp-media/phpunit": "^3"}, "type": "library", "autoload": {"psr-4": {"WPMedia\\PluginFamily\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "authors": [{"name": "WP Media", "email": "<EMAIL>", "homepage": "https://wp-media.me"}], "description": "Organizes and displays WP Media plugin family across other members.", "support": {"issues": "https://github.com/wp-media/plugin-family/issues", "source": "https://github.com/wp-media/plugin-family/tree/v1.0.4"}, "time": "2024-12-30T10:08:18+00:00"}, {"name": "wp-media/wp-rocket", "version": "v3.18.1.5", "source": {"type": "git", "url": "https://github.com/wp-media/wp-rocket.git", "reference": "80eb913fc0fe76b56a68ca632cc6348a028dc0a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-media/wp-rocket/zipball/80eb913fc0fe76b56a68ca632cc6348a028dc0a6", "reference": "80eb913fc0fe76b56a68ca632cc6348a028dc0a6", "shasum": ""}, "require": {"berlindb/core": "^2.0", "cloudflare/cf-ip-rewrite": "^1.0", "composer/installers": "^1.0 || ^2.0", "php": ">=7.3", "voku/simple_html_dom": "^4.8", "wp-media/apply-filters-typed": "^1.0", "wp-media/plugin-family": "^1.0"}, "require-dev": {"brain/monkey": "^2.0", "coenjacobs/mozart": "^0.7", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "league/container": "^4.2", "mikey179/vfsstream": "1.6.11", "mnsami/composer-custom-directory-installer": "^2.0", "mobiledetect/mobiledetectlib": "^2.8", "php": "^7 || ^8", "php-stubs/wordpress-tests-stubs": "^6.5", "phpcompatibility/phpcompatibility-wp": "^2.0", "phpstan/extension-installer": "^1.3", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.4", "phpunit/phpunit": "^7.5 || ^8 || ^9", "psr/container": "1.1.1", "roave/security-advisories": "dev-master", "szepeviktor/phpstan-wordpress": "^1.3", "woocommerce/action-scheduler": "3.9.0", "wp-coding-standards/wpcs": "^3", "wp-media/background-processing": "^1.3", "wp-media/monolog": "^0.0", "wp-media/phpunit": "^3", "wp-media/rocket-lazyload-common": "^3.0.17", "wp-media/wp-imagify-partner": "^1.0", "wpackagist-plugin/amp": "^1.1.4", "wpackagist-plugin/cloudflare": "4.12.7", "wpackagist-plugin/hummingbird-performance": "2.0.1", "wpackagist-plugin/jetpack": "9.3.2", "wpackagist-plugin/pdf-embedder": "4.6.*", "wpackagist-plugin/simple-custom-css": "^4.0.3", "wpackagist-plugin/spinupwp": "^1.1", "wpackagist-plugin/the-events-calendar": "6.5.0.1", "wpackagist-plugin/woocommerce": "^8", "wpackagist-plugin/wp-smushit": "^3"}, "type": "wordpress-plugin", "extra": {"mozart": {"packages": ["mobiledetect/mobiledetectlib", "wp-media/background-processing", "wp-media/rocket-lazyload-common", "wp-media/wp-imagify-partner", "wp-media/monolog", "league/container", "berlindb/core", "wp-media/plugin-family"], "dep_directory": "/inc/Dependencies/", "dep_namespace": "WP_Rocket\\Dependencies\\", "classmap_prefix": "WP_Rocket_", "classmap_directory": "/inc/classes/dependencies/"}, "plugin_domain": "rocket", "installer-paths": {"vendor/{$vendor}/{$name}/": ["type:wordpress-plugin"], "./inc/Dependencies/ActionScheduler/": ["woocommerce/action-scheduler"]}}, "autoload": {"psr-4": {"WP_Rocket\\": "inc/", "WPMedia\\Cloudflare\\": "inc/Addon/Cloudflare/"}, "classmap": ["inc/classes", "inc/vendors/classes", "inc/deprecated"], "exclude-from-classmap": ["inc/vendors/classes/class-rocket-mobile-detect.php", "inc/classes/class-wp-rocket-requirements-check.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "WP Media", "email": "<EMAIL>", "homepage": "https://wp-media.me"}], "description": "Performance optimization plugin for WordPress", "homepage": "https://wp-rocket.me", "keywords": ["cache", "lazyload", "minification", "wordpress"], "support": {"issues": "https://github.com/wp-media/wp-rocket/issues", "source": "https://github.com/wp-media/wp-rocket"}, "time": "2025-02-11T12:59:56+00:00"}, {"name": "wpackagist-plugin/acf-quickedit-fields", "version": "3.3.8", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/acf-quickedit-fields/", "reference": "tags/3.3.8"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/acf-quickedit-fields.3.3.8.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/acf-quickedit-fields/"}, {"name": "wpackagist-plugin/admin-bar-user-switching", "version": "1.4", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/admin-bar-user-switching/", "reference": "tags/1.4"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/admin-bar-user-switching.1.4.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/admin-bar-user-switching/"}, {"name": "wpackagist-plugin/elasticpress", "version": "5.1.4", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/elasticpress/", "reference": "tags/5.1.4"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/elasticpress.5.1.4.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/elasticpress/"}, {"name": "wpackagist-plugin/enable-media-replace", "version": "4.1.5", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/enable-media-replace/", "reference": "tags/4.1.5"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/enable-media-replace.4.1.5.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/enable-media-replace/"}, {"name": "wpackagist-plugin/manage-privacy-options", "version": "1.1", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/manage-privacy-options/", "reference": "tags/1.1"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/manage-privacy-options.1.1.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/manage-privacy-options/"}, {"name": "wpackagist-plugin/media-library-organizer", "version": "1.6.5", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/media-library-organizer/", "reference": "tags/1.6.5"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/media-library-organizer.1.6.5.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/media-library-organizer/"}, {"name": "wpackagist-plugin/password-protected", "version": "2.7.7", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/password-protected/", "reference": "tags/2.7.7"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/password-protected.2.7.7.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/password-protected/"}, {"name": "wpackagist-plugin/safe-svg", "version": "2.3.1", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/safe-svg/", "reference": "tags/2.3.1"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/safe-svg.2.3.1.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/safe-svg/"}, {"name": "wpackagist-plugin/simple-custom-post-order", "version": "2.5.10", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/simple-custom-post-order/", "reference": "tags/2.5.10"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/simple-custom-post-order.2.5.10.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/simple-custom-post-order/"}, {"name": "wpackagist-plugin/smtp-mailer", "version": "1.1.17", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/smtp-mailer/", "reference": "trunk"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/smtp-mailer.zip?timestamp=1731556335"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/smtp-mailer/"}, {"name": "wpackagist-plugin/spatie-ray", "version": "1.7.9", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/spatie-ray/", "reference": "tags/1.7.9"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/spatie-ray.1.7.9.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/spatie-ray/"}, {"name": "wpackagist-plugin/user-switching", "version": "1.9.1", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/user-switching/", "reference": "tags/1.9.1"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/user-switching.1.9.1.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/user-switching/"}, {"name": "wpackagist-plugin/wordfence", "version": "8.0.3", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/wordfence/", "reference": "tags/8.0.3"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/wordfence.8.0.3.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/wordfence/"}, {"name": "wpackagist-plugin/wp-seopress", "version": "*******", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/wp-seopress/", "reference": "tags/*******"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/wp-seopress.*******.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/wp-seopress/"}, {"name": "wpengine/advanced-custom-fields-pro", "version": "6.3.12", "dist": {"type": "zip", "url": "https://connect.advancedcustomfields.com/v2/plugins/composer_download?s=composer&p=pro&t=6.3.12"}, "require": {"composer/installers": "~1.0 || ~2.0"}, "replace": {"wpackagist-plugin/advanced-custom-fields": "self.version", "wpengine/advanced-custom-fields": "self.version"}, "type": "wordpress-plugin"}], "packages-dev": [], "aliases": [], "minimum-stability": "dev", "stability-flags": {"roots/wordpress": 0}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": ">=8.1"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}