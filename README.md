# 🦁 Grahou

### A WordPress starter made by [Les Animals](https://lesanimals.digital) which helps make Internet great again!

## 🏄🏻 Read this first

* Installation instructions: [`INSTALLATION.md`](docs/INSTALLATION.md)
* PhpStorm configuration: [`PHPSTORM.md`](docs/PHPSTORM.md)

## 🧑🏻‍🍳 Documented modules

* Cookies: [`app/components/statics/cookies/README.md`](web/app/themes/lesanimals/base/app/components/statics/cookies/README.md)
* Forms: [`app/components/partials/form/README.md`](web/app/themes/lesanimals/base/app/components/partials/form/README.md)
* Images: [`app/components/partials/img/README.md`](web/app/themes/lesanimals/app/components/partials/img/README.md)
* Custom post type: [`app/components/pages/custom-post-type/README.md`](web/app/themes/lesanimals/base/app/components/pages/custom-post-type/README.md)
* Flexible manager: [`app/core/flexible-manager/README.md`](web/app/themes/lesanimals/app/core/flexible-manager/README.md)
* Accordion: [`app/components/partials/accordion/README.md`](web/app/themes/lesanimals/base/app/components/partials/accordion/README.md)
* Pagination: [`app/components/partials/pagination/README.md`](web/app/themes/lesanimals/base/app/components/partials/pagination/README.md)

## ✍️ CSS naming

* \-- : separator between the main module (`.flexible--image`, `.page--block`, `.front_page--header`)
* \- : hierarchy separator (parent > child)
* \_ : space between two words (sub_child, block_wrapper...)
* \__ : variation (color, size...)

Here are a few examples of how to use CSS naming:

```css
.page--block {}
.page--block_wrapper {}
.page--block_container {}

.page--block-child {}
.page--block-child_wrapper {}
.page--block-child-title {}
.page--block-child-txt {}

.page--block-sub_block {}
.page--block-child-sub_child {}
.page--block-child-sub_child-title {}
.page--block-child-sub_child-txt {}

.block_id--title {}
.block_id--child {}
.block_id--sub_block-title {}
.block_id--sub_block-txt {}

.block_id--title__color {}
.block_id--title__bold {}
.block_id--txt__small {}
.block_id--txt__big {}

.fp_intro--slider_wrapper-slider-item-title {}
.fp_intro--slider_wrapper-slider-item-txt {}
.fp_intro--slider_wrapper-slider-item-txt__white {}
.fp_intro--slider_wrapper-pagination-bullet {}
```
