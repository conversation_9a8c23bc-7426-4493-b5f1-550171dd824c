<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="LesAnimals"
		 xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/PHPCSStandards/PHP_CodeSniffer/master/phpcs.xsd">

	<description>Les Animals</description>

	<!-- Strip the filepaths down to the relevant bit. -->
	<arg name="basepath" value="."/>

	<!-- Check up to 8 files simultaneously. -->
	<arg name="parallel" value="8"/>

	<!-- Exclude the Composer Vendor directory. -->
	<exclude-pattern>/vendor/*</exclude-pattern>

	<!-- Exclude the Node Modules directory. -->
	<exclude-pattern>/node_modules/*</exclude-pattern>

	<!-- Exclude the Base directory. -->
	<exclude-pattern>/base/*</exclude-pattern>

	<!-- Exclude PHP files of acf fields -->
	<exclude-pattern>/app/components/*/*/acf/*.php</exclude-pattern>
	<exclude-pattern>/app/core/*/acf/*.php</exclude-pattern>
	<exclude-pattern>/app/core/acf_default_sync/*.php</exclude-pattern>

	<!-- Exclude PHP languages files -->
	<exclude-pattern>/app/core/languages/l10n/*</exclude-pattern>

	<!-- Include the WordPress-Extra standard. -->
	<rule ref="WordPress-Extra">
		<!--
		We may want a middle ground though. The best way to do this is add the
		entire ruleset, then rule by rule, remove ones that don't suit a project.
		We can do this by running `phpcs` with the '-s' flag, which allows us to
		see the names of the sniffs reporting errors.
		Once we know the sniff names, we can opt to exclude sniffs which don't
		suit our project like so.

		The below two examples just show how you can exclude rules.
		They are not intended as advice about which sniffs to exclude.
		-->

		<!--
		<exclude name="WordPress.WhiteSpace.ControlStructureSpacing"/>
		<exclude name="WordPress.Security.EscapeOutput"/>
		-->
	</rule>

	<!-- Let's also check that everything is properly documented. -->
	<rule ref="WordPress-Docs"/>

	<!-- Add in some extra rules from other standards. -->
	<rule ref="Generic.CodeAnalysis.UnusedFunctionParameter"/>
	<rule ref="Generic.Commenting.Todo"/>

	<!-- Check for PHP cross-version compatibility. -->
	<!--
	To enable this, the PHPCompatibilityWP standard needs
	to be installed.
	See the readme for installation instructions:
	https://github.com/PHPCompatibility/PHPCompatibilityWP
	For more information, also see:
	https://github.com/PHPCompatibility/PHPCompatibility
	-->
	<!--
	<config name="testVersion" value="5.2-"/>
	<rule ref="PHPCompatibilityWP"/>
	-->

	<!--
	To get the optimal benefits of using WPCS, we should add a couple of
	custom properties.
	Adjust the values of these properties to fit our needs.

	For information on additional custom properties available, check out
	the wiki:
	https://github.com/WordPress/WordPress-Coding-Standards/wiki/Customizable-sniff-properties
	-->
	<config name="minimum_supported_wp_version" value="6.0"/>

	<rule ref="WordPress.WP.I18n">
		<properties>
			<property name="text_domain" type="array">
				<element value="lesanimals"/>
				<element value="acf"/>
				<element value="acfe"/>
			</property>
		</properties>
	</rule>

	<rule ref="WordPress.NamingConventions.PrefixAllGlobals">
		<properties>
			<property name="prefixes" type="array">
				<element value="LesAnimals"/>
				<element value="lesanimals"/>
				<element value="LESANIMALS"/>
			</property>
		</properties>
	</rule>

	<rule ref="PEAR.Functions.FunctionCallSignature.ContentAfterOpenBracket">
		<severity>0</severity>
	</rule>

	<rule ref="PEAR.Functions.FunctionCallSignature.CloseBracketLine">
		<severity>0</severity>
	</rule>

	<rule ref="Squiz.Commenting.InlineComment.InvalidEndChar">
		<severity>0</severity>
	</rule>

	<rule ref="Squiz.Commenting.FunctionComment.ParamCommentFullStop">
		<severity>0</severity>
	</rule>

	<rule ref="Squiz.Commenting.VariableComment.Missing">
		<severity>0</severity>
	</rule>

	<rule ref="Squiz.Commenting.ClassComment.Missing">
		<severity>0</severity>
	</rule>

	<rule ref="PEAR.Functions.FunctionCallSignature.MultipleArguments">
		<severity>0</severity>
	</rule>

	<rule ref="WordPress.NamingConventions.ValidVariableName.PropertyNotSnakeCase">
		<severity>0</severity>
	</rule>

	<rule ref="WordPress">
		<exclude name="WordPress.Arrays.ArrayKeySpacingRestrictions.SpacesAroundArrayKeys"/>
	</rule>

	<rule ref="Generic.Formatting.MultipleStatementAlignment.NotSameWarning">
		<severity>0</severity>
	</rule>

	<rule ref="Generic.Files.LineEndings.InvalidEOLChar">
		<severity>0</severity>
	</rule>

	<rule ref="WordPress.PHP.YodaConditions.NotYoda">
		<severity>0</severity>
	</rule>

	<rule ref="WordPress.Files.FileName.NotHyphenatedLowercase">
		<severity>0</severity>
	</rule>

	<rule ref="Squiz.Commenting.BlockComment.NoNewLine">
		<severity>0</severity>
	</rule>

</ruleset>
