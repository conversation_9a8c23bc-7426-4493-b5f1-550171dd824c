<?php
/**
 * Filter by collection in admin
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\Publication;

use LesAnimals\Components\Pages\Collection\Collection;
use LesAnimals\Components\Pages\Collection\Options as OptionsCollection;
use LesAnimals\Components\Pages\Playlist\Options as OptionsPlaylist;
use LesAnimals\Components\Pages\Account\Options as OptionsAccount;

class Admin_Filter_By_Collection {

	/**
	 * Class construct
	 */
	public function __construct() {
		add_action( 'restrict_manage_posts', array( $this, 'add_collection_filter' ) );
		add_filter( 'parse_query', array( $this, 'filter_by_collection' ) );
	}

	/**
	 * Add collection filter to admin
	 *
	 * @return void
	 */
	public function add_collection_filter(): void {
		if ( ! ( is_admin() && current_user_can( 'edit_posts' ) && $this->is_publication_list() ) ) {
			return;
		}

		$all_collections = get_posts( array(
			'post_type'      => 'collection',
			'posts_per_page' => -1,
			'post_status'    => 'any',
			'orderby'        => 'title',
			'order'          => 'ASC',
		) );

		$selected_value = isset( $_GET[ 'collection_id' ] ) ? sanitize_key( $_GET[ 'collection_id' ] ) : '';
		?>
		<label for="collection_id-selector" class="screen-reader-text">Filtrer par collection</label>
		<select name="collection_id" id="collection_id-selector">
			<option value="">Filtrer par collection</option>
			<?php foreach ( $all_collections as $collection ) : ?>
				<option value="<?php echo esc_html( $collection->ID ); ?>" <?php echo esc_html( selected( $selected_value, $collection->ID ) ); ?>><?php echo esc_html( $collection->post_title ); ?></option>
			<?php endforeach; ?>
		</select>
		<?php
	}

	/**
	 * Filter by collection
	 *
	 * @param \WP_Query $query The query
	 *
	 * @return void
	 */
	public function filter_by_collection( \WP_Query $query ): void {
		if ( ! ( is_admin() && $this->is_publication_list() && $query->is_main_query() ) ) {
			return;
		}

		if ( isset( $_GET[ 'collection_id' ] ) && ! empty( $_GET[ 'collection_id' ] ) ) {
			$collection_id = sanitize_key( $_GET[ 'collection_id' ] );
			$query->set( 'meta_query', array(
				array(
					'key'     => 'collection_id',
					'value'   => $collection_id,
					'compare' => '=',
				),
			) );
		}
	}

	/**
	 * Is the publication list?
	 *
	 * @return bool
	 */
	private function is_publication_list(): bool {
		return isset( $_GET[ 'post_type' ] ) && $_GET[ 'post_type' ] === 'publication';
	}
}

new Admin_Filter_By_Collection();
