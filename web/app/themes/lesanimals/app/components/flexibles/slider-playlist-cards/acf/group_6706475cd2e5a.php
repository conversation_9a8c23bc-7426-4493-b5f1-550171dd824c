<?php 

if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
	'key' => 'group_6706475cd2e5a',
	'title' => 'Options - Playlist',
	'fields' => array(
		array(
			'allow_backendsearch' => false,
			'show_column_filter' => false,
			'allow_bulkedit' => false,
			'allow_quickedit' => false,
			'show_column' => false,
			'show_column_weight' => 1000,
			'show_column_sortable' => false,
			'key' => 'field_670d1ae139a38',
			'label' => '',
			'name' => 'playlist',
			'aria-label' => '',
			'type' => 'group',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'layout' => 'block',
			'acfe_seamless_style' => 0,
			'acfe_group_modal' => 0,
			'acfe_group_modal_close' => 0,
			'acfe_group_modal_button' => '',
			'acfe_group_modal_size' => 'large',
			'sub_fields' => array(
				array(
					'allow_backendsearch' => false,
					'show_column_filter' => false,
					'allow_bulkedit' => false,
					'allow_quickedit' => false,
					'show_column' => 0,
					'show_column_weight' => 1000,
					'show_column_sortable' => false,
					'key' => 'field_678f8fb46944f',
					'label' => 'Images d\'en-tête',
					'name' => 'header_img_ids',
					'aria-label' => '',
					'type' => 'gallery',
					'instructions' => '<i>Taille d\'image minimum recommandée&nbsp;: 2000&nbsp;x&nbsp;800&nbsp;px.<br>
La sélection d\'images s\'affiche en en-tête des playlists utilisateur, de façon aléatoire.</i>',
					'required' => 1,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'return_format' => 'id',
					'library' => 'all',
					'translations' => 'copy_once',
					'required_message' => '',
					'min' => '',
					'max' => '',
					'min_width' => '',
					'min_height' => '',
					'min_size' => '',
					'max_width' => '',
					'max_height' => '',
					'max_size' => '',
					'mime_types' => '',
					'insert' => 'append',
					'preview_size' => 'thumbnail',
				),
				array(
					'allow_backendsearch' => false,
					'show_column_filter' => false,
					'allow_bulkedit' => false,
					'allow_quickedit' => false,
					'show_column' => false,
					'show_column_weight' => 1000,
					'show_column_sortable' => false,
					'key' => 'field_670649b7237f0',
					'label' => 'Variations de playlist',
					'name' => 'push_slider_playlist_cards_variations',
					'aria-label' => '',
					'type' => 'repeater',
					'instructions' => '<i style="color: #f00; font-weight: 700;">Attention, chaque titre doit être unique.</i>',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'ep_acf_repeater_index_field' => 0,
					'acfe_repeater_stylised_button' => 0,
					'layout' => 'block',
					'pagination' => 0,
					'min' => 0,
					'max' => 0,
					'collapsed' => 'field_670649f7d0921',
					'button_label' => 'Ajouter une variation de playlist',
					'rows_per_page' => 20,
					'sub_fields' => array(
						array(
							'allow_backendsearch' => false,
							'show_column_filter' => false,
							'allow_bulkedit' => false,
							'allow_quickedit' => false,
							'show_column' => false,
							'show_column_weight' => 1000,
							'show_column_sortable' => false,
							'key' => 'field_670649f7d0921',
							'label' => 'Titre',
							'name' => 'title',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 1,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '30',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'required_message' => '',
							'maxlength' => '',
							'allow_in_bindings' => 0,
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'parent_repeater' => 'field_670649b7237f0',
						),
						array(
							'allow_backendsearch' => false,
							'show_column_filter' => false,
							'allow_bulkedit' => false,
							'allow_quickedit' => false,
							'show_column' => false,
							'show_column_weight' => 1000,
							'show_column_sortable' => false,
							'key' => 'field_67064a18d0922',
							'label' => 'Sélection de playlists',
							'name' => 'playlist_ids',
							'aria-label' => '',
							'type' => 'relationship',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '70',
								'class' => '',
								'id' => '',
							),
							'relevanssi_exclude' => 0,
							'post_type' => array(
								0 => 'playlist_collection',
								1 => 'playlist_user',
							),
							'post_status' => '',
							'taxonomy' => '',
							'filters' => array(
								0 => 'search',
							),
							'return_format' => 'id',
							'acfe_add_post' => 0,
							'acfe_edit_post' => 0,
							'acfe_bidirectional' => array(
								'acfe_bidirectional_enabled' => '0',
							),
							'translations' => 'copy_once',
							'min' => '',
							'max' => 8,
							'allow_in_bindings' => 0,
							'elements' => '',
							'bidirectional' => 0,
							'bidirectional_target' => array(
							),
							'parent_repeater' => 'field_670649b7237f0',
						),
					),
				),
			),
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'options_page',
				'operator' => '==',
				'value' => 'acf-options-options',
			),
		),
	),
	'menu_order' => 31,
	'position' => 'normal',
	'style' => 'seamless',
	'label_placement' => 'left',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'acfe_autosync' => array(
		0 => 'php',
	),
	'acfe_form' => 0,
	'acfe_display_title' => 'Playlist',
	'qef_simple_location_rules' => 0,
	'acfe_meta' => '',
	'acfe_note' => '',
	'acfe_categories' => array(
		'options' => 'Options',
	),
	'modified' => 1752823429,
));

endif;