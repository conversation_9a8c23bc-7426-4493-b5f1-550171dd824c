<?php
/**
 * ACF for SliderPlaylistCards flexible
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\SliderPlaylistCards;

class ACF {
	use \LesAnimals\Components\Flexibles\SliderPublicationCards\Aid;

	/**
	 * Class construct
	 */
	public function __construct() {
		if ( ! is_admin() ) {
			return;
		}

		add_filter( 'acf/load_field/key=field_67064457477e3', array( $this, 'add_push_slider_playlist_cards_variations' ) );
		add_filter( 'acf/load_field/key=field_67064a18d0922', array( $this, 'customize_playlist_manual_selection' ) );
	}

	/**
	 * Add push slider playlist cards manual variations
	 *
	 * @param array $field field
	 * @return array
	 */
	public function add_push_slider_playlist_cards_variations( array $field ): array {
		if ( acfe_is_admin_screen() ) {
			return $field;
		}

		$field[ 'choices' ] = array();
		foreach ( Options::get()->push_slider_playlist_cards_variations as $variation ) {
			$field[ 'choices' ][ sanitize_title( $variation[ 'title' ] ) ] = $variation[ 'title' ];
		}

		return $field;
	}

	/**
	 * Customize the manual selection of playlists
	 *
	 * @param array $field Field
	 * @return array
	 */
	public function customize_playlist_manual_selection( array $field ): array {
		$field[ 'max' ] = $this->nb_item_max;

		return $field;
	}
}

new ACF();
