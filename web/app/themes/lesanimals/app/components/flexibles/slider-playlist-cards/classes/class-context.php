<?php
/**
 * Context for SliderPlaylistCards flexible
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\SliderPlaylistCards;

class Context {
	use \LesAnimals\Components\Flexibles\SliderPublicationCards\Aid;

	/**
	 * Initialize the flexible
	 * Enables hooking the variables
	 *
	 * @param array $flx The flexible context
	 * @return array
	 */
	public function init( array $flx ): array {
		$flx[ 'type' ] = array(
			'value' => 'playlist',
			'label' => 'Playlist',
		);
		$flx = $this->get_slider_data( $flx );

		return $flx;
	}
}
