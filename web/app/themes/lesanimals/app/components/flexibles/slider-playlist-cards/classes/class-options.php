<?php
/**
 * Options for SliderPlaylistCards flexible
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\SliderPlaylistCards;

use LesAnimals\Core\Options\Options_Abstract;

class Options extends Options_Abstract {

	public array $push_slider_playlist_cards_variations;

	/**
	 * Init
	 *
	 * @return void
	 */
	protected function init(): void {
		$this->push_slider_playlist_cards_variations = self::get_option( 'push_slider_playlist_cards_variations', 'playlist' );
	}
}
