<?php 

if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
	'key' => 'group_6706445742839',
	'title' => 'Flexible - Slider cartes de playlist',
	'fields' => array(
		array(
			'key' => 'field_67064457477e3',
			'label' => 'Affichage',
			'name' => 'display',
			'aria-label' => '',
			'type' => 'radio',
			'instructions' => '<i>L\'édition des variations de playlist se fait dans la page <a href="admin.php?page=acf-options-options">Options > Playlist > Variations de playlist</a>.</i>',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '70',
				'class' => '',
				'id' => '',
			),
			'choices' => array(
			),
			'default_value' => '',
			'return_format' => 'array',
			'allow_null' => 0,
			'other_choice' => 0,
			'allow_in_bindings' => 1,
			'layout' => 'vertical',
			'save_other_choice' => 0,
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'page_type',
				'operator' => '==',
				'value' => 'front_page',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'seamless',
	'label_placement' => 'left',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => 0,
	'description' => '',
	'show_in_rest' => 0,
	'acfe_autosync' => array(
		0 => 'php',
	),
	'acfe_form' => 0,
	'acfe_display_title' => 'Slider cartes de playlist',
	'acfe_meta' => '',
	'acfe_note' => '',
	'lesanimals_save_as_layout' => 1,
	'lesanimals_layout_slug' => 'slider-playlist-cards',
	'lesanimals_layout_settings' => '',
	'lesanimals_layout_min' => '',
	'lesanimals_layout_max' => '',
	'acfe_categories' => array(
		'flexible' => 'Flexible',
	),
	'modified' => 1737378376,
));

endif;