<?php 

if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
	'key' => 'group_673338c36f1bd',
	'title' => 'Flexible - Accordéons',
	'fields' => array(
		array(
			'key' => 'field_672e095cbe7f2',
			'label' => 'Titre',
			'name' => 'title',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '60',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'translations' => 'translate',
			'maxlength' => '',
			'allow_in_bindings' => 0,
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'parent_repeater' => 'field_672e0945be7f1',
		),
		array(
			'key' => 'field_672e0cb8be7f4',
			'label' => 'Questions',
			'name' => 'questions',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'acfe_repeater_stylised_button' => 1,
			'layout' => 'table',
			'min' => 0,
			'max' => 0,
			'collapsed' => 'field_672e0ccabe7f5',
			'button_label' => 'Ajouter une question',
			'rows_per_page' => 20,
			'sub_fields' => array(
				array(
					'key' => 'field_672e0ccabe7f5',
					'label' => 'Question',
					'name' => 'question',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'default_value' => '',
					'translations' => 'translate',
					'maxlength' => '',
					'allow_in_bindings' => 0,
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
					'parent_repeater' => 'field_672e0cb8be7f4',
				),
				array(
					'key' => 'field_672e0cd2be7f6',
					'label' => 'Réponse',
					'name' => 'answer',
					'aria-label' => '',
					'type' => 'wysiwyg',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'default_value' => '',
					'acfe_wysiwyg_auto_init' => 0,
					'acfe_wysiwyg_height' => 300,
					'acfe_wysiwyg_max_height' => '',
					'acfe_wysiwyg_valid_elements' => '',
					'acfe_wysiwyg_custom_style' => '',
					'acfe_wysiwyg_disable_wp_style' => 0,
					'acfe_wysiwyg_autoresize' => 0,
					'acfe_wysiwyg_disable_resize' => 0,
					'acfe_wysiwyg_remove_path' => 0,
					'acfe_wysiwyg_menubar' => 0,
					'acfe_wysiwyg_transparent' => 0,
					'acfe_wysiwyg_merge_toolbar' => 0,
					'acfe_wysiwyg_custom_toolbar' => 0,
					'translations' => 'translate',
					'allow_in_bindings' => 0,
					'tabs' => 'all',
					'toolbar' => 'basic',
					'media_upload' => 0,
					'delay' => 0,
					'acfe_wysiwyg_min_height' => 300,
					'acfe_wysiwyg_toolbar_buttons' => array(
					),
					'parent_repeater' => 'field_672e0cb8be7f4',
				),
			),
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_template',
				'operator' => '==',
				'value' => 'app/components/pages/faq/controllers/template-faq.php',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'seamless',
	'label_placement' => 'left',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => 0,
	'description' => '',
	'show_in_rest' => 0,
	'acfe_autosync' => array(
		0 => 'php',
	),
	'acfe_form' => 0,
	'acfe_display_title' => 'Accordéons',
	'acfe_meta' => '',
	'acfe_note' => '',
	'lesanimals_save_as_layout' => 1,
	'lesanimals_layout_slug' => 'accordions',
	'lesanimals_layout_settings' => '',
	'lesanimals_layout_min' => '',
	'lesanimals_layout_max' => '',
	'acfe_categories' => array(
		'flexible' => 'Flexible',
	),
	'modified' => 1737377614,
));

endif;