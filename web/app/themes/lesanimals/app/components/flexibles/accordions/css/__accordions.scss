// *****************************************
// -------- Flexible - accordions --------
// *****************************************


.flx_accordions {

	@extend %flx;
	@extend %flx_margin;
}

.flx_accordions--title {
	margin-bottom: 16px;
	font: var(--title_m);
}

.flx_accordions--question {
	border-bottom: 1px solid $color_black;

	&:first-child {
		border-top: 1px solid $color_black;
	}
}

.flx_accordions--question-inner {
	display: grid;
	grid-template-rows: 0fr;
	transition: grid-template-rows $ease_default;

	.flx_accordions--question.active & {
		grid-template-rows: 1fr;
	}
}

.flx_accordions--question-content {
	overflow: hidden;
}

.flx_accordions--question-btn {
	position: relative;
	width: 100%;
	padding-right: 58px;
	color: $color_light;
	font: var(--title_s);
	padding-block: 16px;

	svg {
		pointer-events: none;
		position: absolute;
		top: 23px;
		right: 24px;
		width: 8px;
		height: 14px;
		color: $color_purple_light;
		transition: rotate $ease_default;
		rotate: 90deg;

		.flx_accordions--question.active & {
			rotate: -90deg;
		}
	}

	@include hover {
		color: $color_purple_light;
	}
}

.flx_accordions--question-answer {
	padding-block: 10px 24px;
}


@include query($until: "mobile") {

	.flx_accordions--title {
		font: var(--title_s);
		font-weight: 700;
	}

	.flx_accordions--question-btn {
		padding-right: 26px;
		font: var(--text_l);

		svg {
			top: 24px;
			right: 7px;
			width: 6px;
			height: 10px;
		}
	}
}
