import AbstractView from 'abstracts/AbstractView.js';

export default class Accordions extends AbstractView {
	/**
	 * @type {NodeList} Accordion buttons
	 */
	#$$btnsAcc;

	/**
	 * @param {HTMLElement} $wrapper
	 */
	constructor( $wrapper ) {
		super( $wrapper );

		this.init();
	}

	initDOM() {
		this.#$$btnsAcc = this.$wrapper.querySelectorAll( '.flx_accordions--question-btn' );
	}

	initEl() {
	}

	bindEvents() {
		this.eh.onClickAccordion = this.#onClickAccordion.bind( this );
		this.#$$btnsAcc.forEach( ( $btn ) => $btn.addEventListener( 'click', this.eh.onClickAccordion ) );
	}

	unbindEvents() {
		this.#$$btnsAcc.forEach( ( $btn ) => $btn.removeEventListener( 'click', this.eh.onClickAccordion ) );
	}

	#onClickAccordion( e ) {
		e.preventDefault();

		const $btnClicked = e.currentTarget;
		const $question = $btnClicked.closest( '.flx_accordions--question' );

		if ( $question.classList.contains( 'active' ) ) {
			$question.classList.remove( 'active' );
			return;
		}

		this.$wrapper.querySelectorAll( '.flx_accordions--question' ).forEach( ( $item ) => {
			if ( $item.classList.contains( 'active' ) ) {
				$item.classList.remove( 'active' );
			}
		} );

		$question.classList.toggle( 'active' );
	}
}

