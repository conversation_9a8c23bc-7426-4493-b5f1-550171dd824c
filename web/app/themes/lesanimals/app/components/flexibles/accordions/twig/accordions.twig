{% if flx.title is not empty and flx.questions is not empty %}
	<section class="flx_accordions" data-component="accordions">
		<div class="flx_accordions--inner">
			{% if flx.title is not empty %}
				<h2 class="flx_accordions--title">{{ flx.title }}</h2>
			{% endif %}
			{% if flx.questions is not empty %}
				<div class="flx_accordions--questions">
					{% for question_item in flx.questions %}
						<div class="flx_accordions--question">
							<h3>
								<button class="flx_accordions--question-btn">{{ question_item.question }}{{ svg('chevron') }}</button>
							</h3>
							<div class="flx_accordions--question-inner">
								<div class="flx_accordions--question-content">
									<div class="flx_accordions--question-answer rich_text">{{ question_item.answer }}</div>
								</div>
							</div>
						</div>
					{% endfor %}
				</div>
			{% endif %}
		</div>
	</section>
{% endif %}
