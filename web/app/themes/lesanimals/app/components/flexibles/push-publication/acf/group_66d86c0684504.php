<?php 

if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
	'key' => 'group_66d86c0684504',
	'title' => 'Flexible - Mise en avant d\'une publication',
	'fields' => array(
		array(
			'allow_backendsearch' => false,
			'show_column_filter' => false,
			'allow_bulkedit' => false,
			'allow_quickedit' => false,
			'show_column' => false,
			'show_column_weight' => 1000,
			'show_column_sortable' => false,
			'key' => 'field_66d86c070638c',
			'label' => 'Publication',
			'name' => 'pub_id',
			'aria-label' => '',
			'type' => 'post_object',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'publication',
			),
			'post_status' => '',
			'taxonomy' => '',
			'return_format' => 'id',
			'multiple' => 0,
			'max' => '',
			'save_custom' => 0,
			'save_post_status' => 'publish',
			'acfe_add_post' => 0,
			'acfe_edit_post' => 0,
			'acfe_bidirectional' => array(
				'acfe_bidirectional_enabled' => '0',
			),
			'allow_null' => 0,
			'allow_in_bindings' => 0,
			'bidirectional' => 0,
			'ui' => 1,
			'bidirectional_target' => array(
			),
			'save_post_type' => '',
			'min' => '',
		),
		array(
			'allow_backendsearch' => false,
			'show_column_filter' => false,
			'allow_bulkedit' => 0,
			'allow_quickedit' => 0,
			'show_column' => 0,
			'show_column_weight' => 1000,
			'show_column_sortable' => false,
			'key' => 'field_66d86d450638d',
			'label' => 'Image',
			'name' => 'img_id',
			'aria-label' => '',
			'type' => 'image',
			'instructions' => '<i>Taille d\'image minimum recommandée&nbsp;: 2000&nbsp;x&nbsp;900&nbsp;px.</i>',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'uploader' => '',
			'return_format' => 'id',
			'library' => 'all',
			'upload_folder' => '',
			'translations' => 'copy_once',
			'acfe_thumbnail' => 0,
			'required_message' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
			'allow_in_bindings' => 0,
			'preview_size' => 'thumbnail',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'page_type',
				'operator' => '==',
				'value' => 'front_page',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'seamless',
	'label_placement' => 'left',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => 0,
	'description' => '',
	'show_in_rest' => 0,
	'acfe_autosync' => array(
		0 => 'php',
	),
	'acfe_form' => 0,
	'acfe_display_title' => 'Mise en avant d\'une publication',
	'qef_simple_location_rules' => 0,
	'acfe_meta' => '',
	'acfe_note' => '',
	'lesanimals_save_as_layout' => 1,
	'lesanimals_layout_slug' => 'push-publication',
	'lesanimals_layout_settings' => '',
	'lesanimals_layout_min' => '',
	'lesanimals_layout_max' => '',
	'acfe_categories' => array(
		'flexible' => 'Flexible',
	),
	'modified' => 1738771059,
));

endif;