<?php
/**
 * Context for PushPublication flexible
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\PushPublication;

use LesAnimals\Components\Pages\Account\User;

class Context {

	/**
	 * Initialize the flexible
	 * Enables hooking the variables
	 *
	 * @param array $flx The flexible context
	 * @return array
	 */
	public function init( array $flx ): array {
		$flx = $this->set_content( $flx );

		return $flx;
	}

	/**
	 * Set content
	 *
	 * @param array $flx Flexible content
	 * @return array
	 */
	public function set_content( array $flx ): array {
		if ( get_post_status( $flx[ 'pub_id' ] ) !== 'publish' ) {
			return $flx;
		}

		$pub_id = $flx[ 'pub_id' ];
		$pub = get_post( $pub_id );
		$member_only = get_field( 'member_only', $pub_id );
		$category_id = get_field( 'category', $pub_id );
		$category = get_term( $category_id );
		$txt = get_field( 'presentation', $pub_id );
		$link = get_permalink( $pub_id );
		$user = User::get();

		$flx[ 'member_only' ] = $member_only;
		$flx[ 'available' ] = $this->get_available( boolval( $member_only ), $user->is_logged_in );
		$flx[ 'title' ] = $pub->post_title;
		$flx[ 'suptitle' ] = $category->name;
		$flx[ 'txt' ] = $this->get_txt_preview( $txt );
		$flx[ 'link' ] = $link;

		return $flx;
	}

	/**
	 * Get if the publication is available for playing
	 *
	 * @param bool $member_only    Is the publication available for member only?
	 * @param bool $user_logged_in Is the user logged in?
	 * @return bool
	 */
	public function get_available( bool $member_only, bool $user_logged_in ): bool {
		$available = false;

		if ( ! $member_only || ( $member_only && $user_logged_in ) ) {
			$available = true;
		}

		return $available;
	}

	/**
	 * Get the text preview
	 *
	 * @param string $txt Text publication
	 * @return array
	 */
	public function get_txt_preview( string $txt ): string {
		if ( grapheme_strlen( $txt ) > 250 ) {
			$txt = substr( $txt, 0, 250 );
			$last_space_pos = strrpos( $txt, ' ' );
			$txt = substr( $txt, 0, $last_space_pos ) . '...';
		}

		return $txt;
	}
}
