// *********************************************
// -------- Flexible - Push publication --------
// *********************************************


.flx_push_pub {

	@extend %flx;
	@extend %flx_margin;
	display: flex;
	flex-direction: column;
	justify-content: center;
	aspect-ratio: 1440/648;
	width: calc(100% + var(--padding) * 2);
	max-width: var(--width_max);
	padding: 40px 0;
	margin-left: calc(var(--padding) * -1);

	html.flx_preview & {
		margin-left: 0;

		.flx_push_pub--content {
			margin-right: var(--grid_column_gap);
			margin-left: var(--grid_column_gap);
		}
	}
}

.flx_push_pub--img_cont {
	position: absolute;
	inset: 0;
	z-index: -1;

	&::after {
		content: "";
		position: absolute;
		inset: 0;
		background: linear-gradient(180deg, rgba($color_dark_strong, 0) 62.38%, $color_dark 100%), linear-gradient(236deg, rgba($color_dark_strong, 0) 0%, rgba($color_dark_strong, 0.7) 70.25%);
	}
}

.flx_push_pub--content {

	@extend %cw;
	margin-top: 18px;
}

.flx_push_pub--suptitle {

	@extend %txt_label_m;
	display: flex;
}

.flx_push_pub--suptitle-icon {
	flex-shrink: 0;
	width: 24px;
	height: 24px;
	margin-right: 13px;
	color: $color_purple_light;
}

.flx_push_pub--title {
	margin-top: 2px;
	font: var(--title_xl);
}

.flx_push_pub--txt {
	width: col_w(6);
	max-width: var(--text_max_width);
	margin-top: 16px;
	color: $color_light;
}

.flx_push_pub--link {
	margin-top: 24px;
}


// -------- Responsive --------

@include query($until: 1280px) {

	.flx_push_pub--txt {
		width: col_w(8);
	}

}

@include query($until: 1120px) {

	.flx_push_pub {
		aspect-ratio: auto;
		min-height: 500px;
	}

}

@include query($until: 960px) {

	.flx_push_pub--txt {
		width: col_w(10);
	}

}

@include query($until: 640px) {

	.flx_push_pub--txt {
		width: 100%;
		margin-top: 8px;
	}

	.flx_push_pub--link {
		margin-top: 16px;
	}

}

