// *****************************************
// -------- Flexible - Push content --------
// *****************************************


.flx_push_content {

	@extend %flx;
	@extend %flx_margin;
	position: relative;
	z-index: 0;
	outline: 1px solid $color_black;
	background-color: $color_dark_strong;

	.flx_wrapper__default & {
		padding: 80px col_w(1, 1) 72px;
	}

	.flx_wrapper__large & {
		padding: 116px col_w(2, 2) 104px;
	}
}

.flx_push_content--img_cont {
	position: absolute;
	inset: 0;
	z-index: -1;
	mix-blend-mode: overlay;
}

.flx_push_content--title {
	font: var(--title_m);
}

.flx_push_content--txt {
	margin-top: 18px;
	color: $color_light;
}

.flx_push_content--title,
.flx_push_content--txt {
	width: col_w(6);
	max-width: var(--text_max_width);
}

.flx_push_content--link {
	margin-top: 16px;
}


// -------- Responsive --------

@include query($until: 1280px) {

	.flx_push_content--title,
	.flx_push_content--txt {
		width: col_w(8);
	}

}

@include query($until: 960px) {

	.flx_push_content {

		.flx_wrapper__default &,
		.flx_wrapper__large & {
			padding: 72px col_w(1, 1);
		}
	}

	.flx_push_content--title,
	.flx_push_content--txt {
		width: col_w(10);
		max-width: unset;
	}

}

@include query($until: 640px) {

	.flx_push_content {

		.flx_wrapper__default &,
		.flx_wrapper__large & {
			padding: 48px col_w(1);
		}
	}

	.flx_push_content--title,
	.flx_push_content--txt {
		width: col_w(10, 11);
	}

	.flx_push_content--txt {
		margin-top: 10px;
	}

	.flx_push_content--link {
		margin-top: 12px;
	}

}

