<?php 

if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
	'key' => 'group_66d6eca695a26',
	'title' => 'Flexible - Mise en avant de contenu',
	'fields' => array(
		array(
			'allow_backendsearch' => false,
			'show_column_filter' => false,
			'allow_bulkedit' => false,
			'allow_quickedit' => false,
			'show_column' => false,
			'show_column_weight' => 1000,
			'show_column_sortable' => false,
			'key' => 'field_66d6eca69ad4b',
			'label' => 'Affichage',
			'name' => 'display',
			'aria-label' => '',
			'type' => 'radio',
			'instructions' => '<i>L\'édition des variations de la mise en avant se fait dans la page <a href="admin.php?page=acf-options-options">Options > Mise en avant de contenu > Variations de la mise en avant</a>.</i>',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'choices' => array(
				'custom' => 'Personnalisé',
			),
			'default_value' => '',
			'return_format' => 'array',
			'allow_null' => 0,
			'other_choice' => 0,
			'allow_in_bindings' => 1,
			'layout' => 'vertical',
			'save_other_choice' => 0,
		),
		array(
			'allow_backendsearch' => false,
			'show_column_filter' => false,
			'allow_bulkedit' => false,
			'allow_quickedit' => false,
			'show_column' => false,
			'show_column_weight' => 1000,
			'show_column_sortable' => false,
			'key' => 'field_66d71f7e998c0',
			'label' => 'Titre',
			'name' => 'title',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_66d6eca69ad4b',
						'operator' => '==',
						'value' => 'custom',
					),
				),
			),
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'required_message' => '',
			'maxlength' => '',
			'allow_in_bindings' => 1,
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'allow_backendsearch' => false,
			'show_column_filter' => false,
			'allow_bulkedit' => false,
			'allow_quickedit' => false,
			'show_column' => false,
			'show_column_weight' => 1000,
			'show_column_sortable' => false,
			'key' => 'field_66d720142ab15',
			'label' => 'Texte',
			'name' => 'txt',
			'aria-label' => '',
			'type' => 'textarea',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_66d6eca69ad4b',
						'operator' => '==',
						'value' => 'custom',
					),
				),
			),
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'acfe_textarea_code' => 0,
			'required_message' => '',
			'maxlength' => '',
			'allow_in_bindings' => 0,
			'rows' => 3,
			'placeholder' => '',
			'new_lines' => 'br',
		),
		array(
			'allow_backendsearch' => false,
			'show_column_filter' => false,
			'allow_bulkedit' => false,
			'allow_quickedit' => false,
			'show_column' => false,
			'show_column_weight' => 1000,
			'show_column_sortable' => false,
			'key' => 'field_66d720352ab16',
			'label' => 'Bouton',
			'name' => 'link',
			'aria-label' => '',
			'type' => 'acfe_advanced_link',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_66d6eca69ad4b',
						'operator' => '==',
						'value' => 'custom',
					),
				),
			),
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => '',
			'taxonomy' => '',
			'required_message' => '',
			'allow_in_bindings' => 0,
		),
		array(
			'allow_backendsearch' => false,
			'show_column_filter' => false,
			'allow_bulkedit' => 0,
			'allow_quickedit' => 0,
			'show_column' => 0,
			'show_column_weight' => 1000,
			'show_column_sortable' => false,
			'key' => 'field_66d7204f2ab17',
			'label' => 'Image de fond',
			'name' => 'img_id',
			'aria-label' => '',
			'type' => 'image',
			'instructions' => '<i>Taille d\'image minimum recommandée&nbsp;: 1600&nbsp;x&nbsp;500&nbsp;px.</i>',
			'required' => 1,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_66d6eca69ad4b',
						'operator' => '==',
						'value' => 'custom',
					),
				),
			),
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'uploader' => '',
			'return_format' => 'id',
			'library' => 'all',
			'upload_folder' => '',
			'translations' => 'copy_once',
			'acfe_thumbnail' => 0,
			'required_message' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
			'allow_in_bindings' => 0,
			'preview_size' => 'thumbnail',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'page',
			),
			array(
				'param' => 'page_template',
				'operator' => '!=',
				'value' => 'app/components/pages/faq/controllers/template-faq.php',
			),
		),
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'all',
			),
			array(
				'param' => 'post_type',
				'operator' => '!=',
				'value' => 'page',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'seamless',
	'label_placement' => 'left',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => 0,
	'description' => '',
	'show_in_rest' => 0,
	'acfe_autosync' => array(
		0 => 'php',
	),
	'acfe_form' => 0,
	'acfe_display_title' => 'Mise en avant de contenu',
	'qef_simple_location_rules' => 0,
	'acfe_meta' => '',
	'acfe_note' => '',
	'lesanimals_save_as_layout' => 1,
	'lesanimals_layout_slug' => 'push-content',
	'lesanimals_layout_settings' => '',
	'lesanimals_layout_min' => '',
	'lesanimals_layout_max' => '',
	'acfe_categories' => array(
		'flexible' => 'Flexible',
	),
	'modified' => 1738772033,
));

endif;