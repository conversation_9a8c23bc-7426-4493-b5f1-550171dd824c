<?php
/**
 * Context for PushContent flexible
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\PushContent;

class Context {

	/**
	 * Initialize the flexible
	 * Enables hooking the variables
	 *
	 * @param array $flx The flexible context
	 * @return array
	 */
	public function init( array $flx ): array {
		if ( $flx[ 'display' ][ 'value' ] !== 'custom' ) {
			$flx = $this->set_values_from_variation( $flx );
		}

		return $flx;
	}

	/**
	 * Set values from variation
	 *
	 * @param array $flx Flexible content
	 * @return array
	 */
	private function set_values_from_variation( array $flx ): array {
		foreach ( Options::get()->variations as $variation ) {
			if ( sanitize_title( $variation[ 'title' ] ) === $flx[ 'display' ][ 'value' ] ) {
				$flx = array_merge( $flx, $variation );
				break;
			}
		}

		return $flx;
	}
}
