<?php
/**
 * ACF for PushContent flexible
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\PushContent;

class ACF {

	/**
	 * Class construct
	 */
	public function __construct() {
		if ( ! is_admin() ) {
			return;
		}

		add_filter( 'acf/load_field/key=field_66d6eca69ad4b', array( $this, 'add_push_content_variations' ) );
	}

	/**
	 * Add push content variations
	 *
	 * @param array $field field
	 * @return array
	 */
	public function add_push_content_variations( array $field ): array {
		if ( acfe_is_admin_screen() ) {
			return $field;
		}

		$default_choice = $field[ 'choices' ];
		$field[ 'choices' ] = array();
		foreach ( Options::get()->variations as $variation ) {
			$field[ 'choices' ][ sanitize_title( $variation[ 'title' ] ) ] = $variation[ 'title' ];
		}
		$field[ 'choices' ] = array_merge( $field[ 'choices' ], $default_choice );

		return $field;
	}
}

new ACF();
