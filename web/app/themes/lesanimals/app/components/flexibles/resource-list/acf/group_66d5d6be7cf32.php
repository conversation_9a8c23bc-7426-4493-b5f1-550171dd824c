<?php 

if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
	'key' => 'group_66d5d6be7cf32',
	'title' => 'Flexible - Liste de ressources',
	'fields' => array(
		array(
			'key' => 'field_66d5d6bff7753',
			'label' => '',
			'name' => 'list',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'acfe_repeater_stylised_button' => 0,
			'layout' => 'block',
			'pagination' => 0,
			'min' => 0,
			'max' => 0,
			'collapsed' => '',
			'button_label' => 'Ajouter un élément',
			'rows_per_page' => 20,
			'sub_fields' => array(
				array(
					'key' => 'field_66d5d9b6e6cc9',
					'label' => 'Titre',
					'name' => 'title',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 1,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '40',
						'class' => '',
						'id' => '',
					),
					'default_value' => '',
					'required_message' => '',
					'maxlength' => '',
					'allow_in_bindings' => 0,
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
					'parent_repeater' => 'field_66d5d6bff7753',
				),
				array(
					'key' => 'field_66d5d78163bf7',
					'label' => 'Type',
					'name' => 'type',
					'aria-label' => '',
					'type' => 'radio',
					'instructions' => '',
					'required' => 1,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '30',
						'class' => '',
						'id' => '',
					),
					'choices' => array(
						'file' => 'Fichier',
						'dl_file' => 'Fichier à télécharger',
						'external_link' => 'Lien externe',
					),
					'default_value' => 'file',
					'return_format' => 'array',
					'required_message' => '',
					'allow_null' => 0,
					'other_choice' => 0,
					'allow_in_bindings' => 0,
					'layout' => 'vertical',
					'save_other_choice' => 0,
					'parent_repeater' => 'field_66d5d6bff7753',
				),
				array(
					'key' => 'field_66d6c062641e1',
					'label' => 'Fichier',
					'name' => 'file_id',
					'aria-label' => '',
					'type' => 'file',
					'instructions' => '',
					'required' => 1,
					'conditional_logic' => array(
						array(
							array(
								'field' => 'field_66d5d78163bf7',
								'operator' => '==',
								'value' => 'file',
							),
						),
					),
					'wrapper' => array(
						'width' => '30',
						'class' => '',
						'id' => '',
					),
					'uploader' => '',
					'return_format' => 'id',
					'preview_style' => 'default',
					'library' => 'all',
					'upload_folder' => '',
					'button_label' => 'Ajouter un fichier',
					'file_count' => 0,
					'multiple' => 0,
					'max' => '',
					'required_message' => '',
					'min_size' => '',
					'max_size' => '',
					'mime_types' => '',
					'allow_in_bindings' => 0,
					'placeholder' => 'Select',
					'stylised_button' => 0,
					'min' => '',
					'parent_repeater' => 'field_66d5d6bff7753',
				),
				array(
					'key' => 'field_66d5d88950061',
					'label' => 'Fichier à télécharger',
					'name' => 'dl_file_id',
					'aria-label' => '',
					'type' => 'file',
					'instructions' => '',
					'required' => 1,
					'conditional_logic' => array(
						array(
							array(
								'field' => 'field_66d5d78163bf7',
								'operator' => '==',
								'value' => 'dl_file',
							),
						),
					),
					'wrapper' => array(
						'width' => '30',
						'class' => '',
						'id' => '',
					),
					'uploader' => '',
					'return_format' => 'id',
					'preview_style' => 'default',
					'library' => 'all',
					'upload_folder' => '',
					'button_label' => 'Ajouter un fichier',
					'file_count' => 0,
					'multiple' => 0,
					'max' => '',
					'required_message' => '',
					'min_size' => '',
					'max_size' => '',
					'mime_types' => '',
					'allow_in_bindings' => 0,
					'placeholder' => 'Select',
					'stylised_button' => 0,
					'min' => '',
					'parent_repeater' => 'field_66d5d6bff7753',
				),
				array(
					'key' => 'field_66d5d86250060',
					'label' => 'Lien externe',
					'name' => 'external_link',
					'aria-label' => '',
					'type' => 'url',
					'instructions' => '',
					'required' => 1,
					'conditional_logic' => array(
						array(
							array(
								'field' => 'field_66d5d78163bf7',
								'operator' => '==',
								'value' => 'external_link',
							),
						),
					),
					'wrapper' => array(
						'width' => '30',
						'class' => '',
						'id' => '',
					),
					'default_value' => '',
					'required_message' => '',
					'allow_in_bindings' => 1,
					'placeholder' => '',
					'parent_repeater' => 'field_66d5d6bff7753',
				),
			),
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'page',
			),
			array(
				'param' => 'page_type',
				'operator' => '!=',
				'value' => 'front_page',
			),
			array(
				'param' => 'page_template',
				'operator' => '!=',
				'value' => 'app/components/pages/faq/controllers/template-faq.php',
			),
		),
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'all',
			),
			array(
				'param' => 'post_type',
				'operator' => '!=',
				'value' => 'page',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'seamless',
	'label_placement' => 'left',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => 0,
	'description' => '',
	'show_in_rest' => 0,
	'acfe_autosync' => array(
		0 => 'php',
	),
	'acfe_form' => 0,
	'acfe_display_title' => 'Liste de ressources',
	'acfe_meta' => '',
	'acfe_note' => '',
	'lesanimals_save_as_layout' => 1,
	'lesanimals_layout_slug' => 'resource-list',
	'lesanimals_layout_settings' => '',
	'lesanimals_layout_min' => '',
	'lesanimals_layout_max' => '',
	'acfe_categories' => array(
		'flexible' => 'Flexible',
	),
	'modified' => 1737378158,
));

endif;