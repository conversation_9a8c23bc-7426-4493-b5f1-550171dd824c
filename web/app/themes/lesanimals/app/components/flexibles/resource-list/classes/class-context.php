<?php
/**
 * Context for ResourceList flexible
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\ResourceList;

class Context {

	/**
	 * Initialize the flexible
	 * Enables hooking the variables
	 *
	 * @param array $flx The flexible context
	 * @return array
	 */
	public function init( array $flx ): array {
		$flx[ 'resources' ] = $this->get_resource_items( $flx[ 'list' ] ?? array() );

		return $flx;
	}

	/**
	 * Get resource items
	 *
	 * @param array|bool $resources Resource list
	 * @return array
	 */
	public function get_resource_items( array|bool $resources ): array {
		if ( ! is_array( $resources ) ) {
			return array();
		}

		$items = array();

		foreach ( $resources as $item ) {
			$link = $this->get_resource_item_link( $item );
			$icon = $item[ 'type' ][ 'value' ] === 'dl_file' ? 'download' : 'arrow-external';

			$items[] = array(
				'title' => $item[ 'title' ],
				'link'  => $link,
				'icon'  => $icon,
				'is_dl' => $item[ 'type' ][ 'value' ] === 'dl_file',
			);
		}

		return $items;
	}

	/**
	 * Get resource item link
	 *
	 * @param array $item Item to get link from
	 * @return string
	 */
	public function get_resource_item_link( array $item ): string {
		$link = '';

		if ( $item[ 'type' ][ 'value' ] === 'file' ) {
			$link = wp_get_attachment_url( $item[ 'file_id' ] );
		} elseif ( $item[ 'type' ][ 'value' ] === 'dl_file' ) {
			$link = wp_get_attachment_url( $item[ 'dl_file_id' ] );
		} elseif ( $item[ 'type' ][ 'value' ] === 'external_link' ) {
			$link = $item[ 'external_link' ];
		}

		return $link;
	}
}
