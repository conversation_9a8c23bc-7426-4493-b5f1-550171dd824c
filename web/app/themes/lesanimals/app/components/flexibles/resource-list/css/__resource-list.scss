// ******************************************
// -------- Flexible - Resource list --------
// ******************************************


.flx_resource_list {

	@extend %flx;
	@extend %flx_margin;
}

.flx_resource_list--items {
	display: flex;
	row-gap: 16px;
	flex-direction: column;
}

.flx_resource_list--items-link {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 22px 32px;
	color: $color_light;
	font: var(--text_l);
	font-weight: 700;
	background-color: $color_strong;

	@include hover {
		color: $color_white;
		background-color: $color_black;

		.flx_resource_list--items-link-icon {
			color: $color_purple_light;
		}
	}

	@include focus {

		.flx_resource_list--items-link-icon {
			color: $color_black;
		}
	}
}

.flx_resource_list--items-link-icon {
	flex-shrink: 0;
	width: 24px;
	height: 24px;
	margin-left: 24px;
	color: $color_white;
	transition: color $ease_default;
}


// -------- Responsive --------

@include query($until: 640px) {

	.flx_resource_list--items {
		row-gap: 8px;
	}

	.flx_resource_list--items-link {
		padding: 16px 24px;
	}

}

