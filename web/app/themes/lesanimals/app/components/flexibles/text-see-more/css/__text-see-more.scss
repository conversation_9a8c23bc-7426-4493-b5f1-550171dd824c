// ******************************************
// -------- Flexible - Text see more --------
// ******************************************


.flx_text_see_more {

	@extend %flx;
	@extend %flx_margin;
}

.flx_text_sm--title {
	margin-bottom: 15px;
	font: var(--title_m);
}

.flx_text_sm--infos {
	margin-bottom: 15px;
	color: $color_medium;
	font: var(--text_m);
}

.flx_text_sm--txt {

	@extend .rich_text;
	--see_more_nb_line_max: 8;
}


// -------- Responsive --------

@include query($until: 640px) {

	.flx_text_sm--title,
	.flx_text_sm--infos {
		margin-bottom: 8px;
	}

}

