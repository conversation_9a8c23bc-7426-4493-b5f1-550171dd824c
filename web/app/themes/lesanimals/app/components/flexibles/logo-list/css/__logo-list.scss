// **************************************
// -------- Flexible - Logo list --------
// **************************************


.flx_logo_list {

	@extend %flx;
	@extend %flx_margin;
}

.flx_logo_list--title {
	font: var(--title_m);
}

.flx_logo_list--logos {
	display: flex;
	gap: var(--flx_logo_list_gap);
	flex-wrap: wrap;
	margin-top: 36px;

	--flx_logo_list_width: #{col_w(9)};
	--flx_logo_list_gap: 24px;
	--flx_logo_list_nb_logo_per_line: 4;
}

.flx_logo_list--img_cont {
	width: calc((var(--flx_logo_list_width) - var(--flx_logo_list_gap) * (var(--flx_logo_list_nb_logo_per_line) - 1)) / var(--flx_logo_list_nb_logo_per_line));
}


// -------- Responsive --------

@include query($until: 1280px) {

	.flx_logo_list--logos {
		--flx_logo_list_nb_logo_per_line: 3;
	}

}

@include query($until: 960px) {

	.flx_logo_list--logos {
		--flx_logo_list_width: 100%;
		--flx_logo_list_gap: 16px;
	}

}

@include query($until: 480px) {

	.flx_logo_list--logos {
		--flx_logo_list_nb_logo_per_line: 2;
	}

}

