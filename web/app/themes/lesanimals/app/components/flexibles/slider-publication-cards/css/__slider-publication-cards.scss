// *****************************************************
// -------- Flexible - Slider publication cards --------
// *****************************************************


.flx_slider_publi_cards {

	@extend %flx;
	@extend %flx_margin;
	grid-column: 1/12 span;
	display: flex;
	flex-wrap: wrap;

	--flx_margin: 120px;

	.slider--btn {
		position: absolute;
	}
}

.flx_slider_publi_cards--title {

	@extend %txt_label_m;
	margin-top: 7px;
}

.flx_slider_publi_cards--cta {
	margin-left: auto;
}

.flx_slider_publi_cards--slider {
	width: 100%;
	margin-top: 16px;
}

.flx_slider_publi_cards--slider__type_playlist {
	margin-top: 21px;
}


// -------- Responsive --------

@include query($until: 1280px) {

	.flx_slider_publi_cards--slider {
		--slider_item_width: #{col_w(5)};
	}

}

@include query($until: 1080px) {

	.flx_slider_publi_cards--slider {
		width: calc(100% - var(--slider_btn_width) * 0.5);
	}

}

@include query($until: 960px) {

	.flx_slider_publi_cards--slider {
		--slider_item_width: #{calc(col_w(6) - var(--slider_btn_width) * 0.25)};
	}

}

@include query($until: 800px) {

	.flx_slider_publi_cards--slider {
		--slider_item_width: #{col_w(7)};
	}

}

@include query($until: 640px) {

	.flx_slider_publi_cards--slider {
		width: 100%;

		--slider_item_width: 70%;
	}

}

@include query($until: 480px) {

	.flx_slider_publi_cards--slider {
		--slider_item_width: 90%;
	}

}

