<?php
/**
 * I18n for SliderPublicationCards
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\SliderPublicationCards;

class I18n {

	/**
	 * Class construct
	 */
	public function __construct() {
		add_filter( 'lesanimals_i18n', array( $this, 'setup_i18n' ) );
	}

	/**
	 * Add translations to Timber context
	 *
	 * @param array $i18n Translations
	 * @return array
	 */
	public function setup_i18n( array $i18n ): array {
		$i18n[ 'slider_pub_cards' ] = array(
			'title_news'       => __( 'Nouveautés', 'lesanimals' ),
			'title_focus'      => __( 'Focus', 'lesanimals' ),
			'title_history'    => __( 'Votre historique', 'lesanimals' ),
			'title_suggestion' => __( 'Suggestions', 'lesanimals' ),
			'see_all'          => __( 'Voir tout', 'lesanimals' ),
		);

		return $i18n;
	}
}

new I18n();
