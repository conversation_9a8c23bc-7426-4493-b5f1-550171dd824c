<?php
/**
 * Aid for SliderPublicationCards
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\SliderPublicationCards;

use LesAnimals\Core\Languages\I18n;
use LesAnimals\Components\Pages\Publication\Options as OptionsPublication;
use LesAnimals\Components\Flexibles\SliderPlaylistCards\Options as OptionsSliderPlaylistCards;
use LesAnimals\Components\Pages\Search\Options as OptionsSearch;
use LesAnimals\Components\Pages\Account\Options as OptionsAccount;

use Timber;

trait Aid {

	protected int $nb_item_max = 8;

	/**
	 * Get the slider data
	 *
	 * @param array $flx Flexible data
	 * @return array
	 */
	private function get_slider_data( array $flx ): array {
		$type = $flx[ 'type' ][ 'value' ] ?? '';
		$post_type = get_post_type( acfe_get_post_id() );

		if ( $post_type === 'publication' ) {
			$type = '';
			$flx = $this->get_data_from_publications_ids( $flx );
		} elseif ( $type === 'playlist' ) {
			$flx = $this->get_data_from_variations( $type, $flx );
		} elseif ( $type !== 'manual' ) {
			$pub_posts = $this->get_publication_posts( $type );

			$flx[ 'title' ] = I18n::get()[ 'slider_pub_cards' ][ 'title_' . $type ];
			$flx[ 'items' ] = $this->get_slider_publication_items( $pub_posts );
		} else {
			$flx = $this->get_data_from_variations( 'publication', $flx );
		}
		$flx[ 'btn_link' ] = $this->get_btn_link( $type, $flx );

		return $flx;
	}

	/**
	 * Get the publication posts
	 *
	 * @param string $type Publication type
	 * @return array
	 */
	private function get_publication_posts( string $type ): array {
		$posts = array();

		if ( $type === 'news' ) {
			$posts = get_posts( array(
				'post_type'      => 'publication',
				'post_status'    => 'publish',
				'posts_per_page' => $this->nb_item_max,
				'meta_query'     => array( // phpcs:ignore
					array(
						'key'   => 'push_to_novelty',
						'value' => true,
					),
				),
			) );
		} elseif ( $type === 'focus' ) {
			$pub_ids = array_slice( OptionsPublication::get()->focus_publication_ids, 0, $this->nb_item_max );
			$posts = $this->convert_ids_list_to_fake_posts_list( $pub_ids );
		} /*elseif ( $type === 'history' ) {
			// TODO : à gérer
		}*/

		return $posts;
	}

	/**
	 * Get data from variations
	 *
	 * @param string $display Display
	 * @param array  $flx     Flexible content
	 * @return array
	 */
	private function get_data_from_variations( string $display, array $flx ): array {
		if ( $display === 'publication' ) {
			$variations = Options::get()->push_slider_publication_cards_manual_variations;
		} elseif ( $display === 'playlist' ) {
			$variations = OptionsSliderPlaylistCards::get()->push_slider_playlist_cards_variations;
		}

		if ( ! $variations ) {
			return $flx;
		}

		foreach ( $variations as $variation ) {
			if ( sanitize_title( $variation[ 'title' ] ) === $flx[ 'display' ][ 'value' ] ) {
				if ( $display === 'publication' && ! empty( $variation[ 'publication_ids' ] ) ) {
					$posts = $this->convert_ids_list_to_fake_posts_list( $variation[ 'publication_ids' ] );
				} elseif ( $display === 'playlist' && ! empty( $variation[ 'playlist_ids' ] ) ) {
					$posts = $this->convert_ids_list_to_fake_posts_list( $variation[ 'playlist_ids' ] );
				} else {
					$posts = array();
				}

				$flx[ 'title' ] = $variation[ 'title' ];
				$flx[ 'items' ] = $this->get_slider_publication_items( $posts, $display );
				$flx[ 'link' ] = ! empty( $variation[ 'link_page_id' ] ) ? get_permalink( $variation[ 'link_page_id' ] ) : false;
				break;
			}
		}

		return $flx;
	}

	/**
	 * Get data from publications
	 *
	 * @param array $flx Flexible content
	 * @return array
	 */
	private function get_data_from_publications_ids( array $flx ): array {
		$posts = get_posts( array(
			'post_type'      => 'publication',
			'post_status'    => 'publish',
			'posts_per_page' => -1,
			'post__in'       => $flx[ 'publications_ids' ],
			'orderby'        => 'post__in',
		) );
		$flx[ 'items' ] = $this->get_slider_publication_items( $posts );

		return $flx;
	}

	/**
	 * Convert a post ids list to a fake posts list
	 *
	 * @param array $post_ids Post ids list
	 * @return array
	 */
	private function convert_ids_list_to_fake_posts_list( array $post_ids ): array {
		$posts = array();

		foreach ( $post_ids as $post_id ) {
			$posts[] = (object) array( 'ID' => $post_id );
		}

		return $posts;
	}

	/**
	 * Get the publication items
	 *
	 * @param array       $posts   Posts
	 * @param string|null $display Display
	 * @return array
	 */
	private function get_slider_publication_items( array $posts, string|null $display = null ): array {
		$pub_card_ctx = array(
			'i18n'        => I18n::get(),
			'is_preview'  => is_admin(),
			'class'       => 'slider--item',
			'class_inner' => 'slider--item_inner',
			'display'     => $display,
		);

		$items = array();
		if ( ! empty( $posts ) ) {
			foreach ( $posts as $post ) {
				if ( get_post_status( $post->ID ) === 'publish' ) {
					$pub_card_ctx[ 'pub_id' ] = $post->ID;
					$items[] = Timber::compile( 'publication-card.twig', $pub_card_ctx );
				}
			}
		}

		return $items;
	}

	/**
	 * Get button link
	 *
	 * @param string $type Type
	 * @param array  $flx  Flexible content
	 * @return array
	 */
	private function get_btn_link( string $type, array $flx ): string {
		$link = '';

		if ( $type === 'news' ) {
			$link = OptionsSearch::get()->page_link;
		} elseif ( $type === 'history' ) {
			$link = OptionsAccount::get()->history[ 'link' ];
		} elseif ( $type === 'manual' && isset( $flx[ 'link' ] ) ) {
			$link = $flx[ 'link' ];
		}

		return $link ?? '';
	}
}
