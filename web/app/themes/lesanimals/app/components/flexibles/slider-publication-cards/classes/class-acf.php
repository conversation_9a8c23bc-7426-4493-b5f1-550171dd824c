<?php
/**
 * ACF for SliderPublicationCards flexible
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\SliderPublicationCards;

class ACF {
	use Aid;

	/**
	 * Class construct
	 */
	public function __construct() {
		if ( ! is_admin() ) {
			return;
		}

		add_filter( 'acf/load_field/key=field_66b4a0c9ea938', array( $this, 'add_push_slider_publication_cards_manual_variations' ) );
		add_filter( 'acf/load_field/key=field_66fe6f3855799', array( $this, 'customize_publication_manual_selection' ) );

		// Hide on publication
		add_filter( 'acf/load_field/key=field_66b1f96081f19', array( $this, 'hide_on_publication' ) );
		add_filter( 'acf/load_field/key=field_66b4a0c9ea938', array( $this, 'hide_on_publication' ) );
		add_filter( 'acf/load_field/key=field_66feb327aa001', array( $this, 'hide_on_publication' ) );

		// Show on publications
		add_filter( 'acf/load_field/key=field_6762c306c8e8c', array( $this, 'show_on_publication_only' ) );
	}

	/**
	 * Add push slider publication cards manual variations
	 *
	 * @param array $field field
	 * @return array
	 */
	public function add_push_slider_publication_cards_manual_variations( array $field ): array {
		if ( acfe_is_admin_screen() ) {
			return $field;
		}

		$field[ 'choices' ] = array();
		foreach ( Options::get()->push_slider_publication_cards_manual_variations as $variation ) {
			$field[ 'choices' ][ sanitize_title( $variation[ 'title' ] ) ] = $variation[ 'title' ];
		}

		return $field;
	}

	/**
	 * Customize the manual selection of publications
	 *
	 * @param array $field Field
	 * @return array
	 */
	public function customize_publication_manual_selection( array $field ): array {
		$field[ 'max' ] = $this->nb_item_max;

		return $field;
	}

	/**
	 * Customize the manual selection of publications
	 *
	 * @param array $field Field
	 * @return array|bool
	 */
	public function hide_on_publication( array $field ): array|bool {
		if ( ! is_admin() || acfe_is_admin_screen() ) {
			return $field;
		}

		$post_type = get_post_type( acfe_get_post_id() );
		if ( ! empty( $post_type ) && $post_type === 'publication' ) {
			$field[ 'wrapper' ][ 'class' ] .= ' acf-hidden';
		}

		return $field;
	}

	/**
	 * Customize the manual selection of publications
	 *
	 * @param array $field Field
	 * @return array|bool
	 */
	public function show_on_publication_only( array $field ): array|bool {
		if ( ! is_admin() || acfe_is_admin_screen() ) {
			return $field;
		}

		$post_type = get_post_type( acfe_get_post_id() );
		if ( ! empty( $post_type ) && $post_type !== 'publication' ) {
			$field[ 'wrapper' ][ 'class' ] .= ' acf-hidden';
		}

		return $field;
	}
}

new ACF();
