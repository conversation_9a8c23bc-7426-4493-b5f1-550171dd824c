<?php
/**
 * Context for SliderPublicationCards flexible
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\SliderPublicationCards;

class Context {
	use Aid;

	/**
	 * Initialize the flexible
	 * Enables hooking the variables
	 *
	 * @param array $flx The flexible context
	 * @return array
	 */
	public function init( array $flx ): array {
		$flx = $this->get_slider_data( $flx );

		return $flx;
	}
}

new Context();
