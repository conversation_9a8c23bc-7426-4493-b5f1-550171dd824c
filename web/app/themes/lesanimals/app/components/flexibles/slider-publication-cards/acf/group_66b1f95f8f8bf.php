<?php

if ( function_exists( 'acf_add_local_field_group' ) ):

	acf_add_local_field_group( array(
		'key'                        => 'group_66b1f95f8f8bf',
		'title'                      => 'Flexible - Slider cartes de publication',
		'fields'                     => array(
			array(
				'key'               => 'field_66b1f96081f19',
				'label'             => 'Type',
				'name'              => 'type',
				'aria-label'        => '',
				'type'              => 'radio',
				'instructions'      => '',
				'required'          => 0,
				'conditional_logic' => 0,
				'wrapper'           => array(
					'width' => '30',
					'class' => '',
					'id'    => '',
				),
				'choices'           => array(
					'news'    => 'Nouveautés',
					'focus'   => 'Focus',
					'history' => 'Historique',
					'manual'  => 'Sélection manuelle',
				),
				'default_value'     => 'news',
				'return_format'     => 'array',
				'allow_null'        => 0,
				'other_choice'      => 0,
				'allow_in_bindings' => 1,
				'layout'            => 'vertical',
				'save_other_choice' => 0,
			),
			array(
				'key'               => 'field_66b4a0c9ea938',
				'label'             => 'Affichage',
				'name'              => 'display',
				'aria-label'        => '',
				'type'              => 'radio',
				'instructions'      => '<i>L\'édition des sélections manuelles de publications se fait dans la page <a href="admin.php?page=acf-options-options">Options > Vidéos > Variations de sélection manuelle</a>.</i>',
				'required'          => 0,
				'conditional_logic' => array(
					array(
						array(
							'field'    => 'field_66b1f96081f19',
							'operator' => '==',
							'value'    => 'manual',
						),
					),
				),
				'wrapper'           => array(
					'width' => '70',
					'class' => '',
					'id'    => '',
				),
				'choices'           => array(),
				'default_value'     => '',
				'return_format'     => 'array',
				'allow_null'        => 0,
				'other_choice'      => 0,
				'allow_in_bindings' => 1,
				'layout'            => 'vertical',
				'save_other_choice' => 0,
			),
			array(
				'key'               => 'field_66feb327aa001',
				'label'             => '',
				'name'              => '',
				'aria-label'        => '',
				'type'              => 'message',
				'instructions'      => '',
				'required'          => 0,
				'conditional_logic' => array(
					array(
						array(
							'field'    => 'field_66b1f96081f19',
							'operator' => '==',
							'value'    => 'focus',
						),
					),
				),
				'wrapper'           => array(
					'width' => '70',
					'class' => '',
					'id'    => '',
				),
				'message'           => '<i>L\'édition des publications à l\'honneur se fait dans la page <a href="admin.php?page=acf-options-options">Options > Vidéos > Sélection de publications Focus</a>.</i>',
				'new_lines'         => 'wpautop',
				'esc_html'          => 0,
			),
			array(
				'key'                  => 'field_6762c306c8e8c',
				'label'                => 'Publications',
				'name'                 => 'publications_ids',
				'aria-label'           => '',
				'type'                 => 'relationship',
				'instructions'         => '',
				'required'             => 0,
				'conditional_logic'    => 0,
				'wrapper'              => array(
					'width' => '',
					'class' => '',
					'id'    => '',
				),
				'post_type'            => array(
					0 => 'publication',
				),
				'post_status'          => '',
				'taxonomy'             => '',
				'filters'              => array(
					0 => 'search',
					1 => 'taxonomy',
				),
				'return_format'        => 'id',
				'acfe_add_post'        => 0,
				'acfe_edit_post'       => 0,
				'acfe_bidirectional'   => array(
					'acfe_bidirectional_enabled' => '0',
				),
				'translations'         => 'copy_once',
				'min'                  => '',
				'max'                  => '',
				'allow_in_bindings'    => 0,
				'elements'             => '',
				'bidirectional'        => 0,
				'bidirectional_target' => array(),
			),
		),
		'location'                   => array(
			array(
				array(
					'param'    => 'post_type',
					'operator' => '==',
					'value'    => 'page',
				),
				array(
					'param'    => 'page_template',
					'operator' => '!=',
					'value'    => 'app/components/pages/faq/controllers/template-faq.php',
				),
			),
			array(
				array(
					'param'    => 'post_type',
					'operator' => '==',
					'value'    => 'all',
				),
				array(
					'param'    => 'post_type',
					'operator' => '!=',
					'value'    => 'page',
				),
			),
		),
		'menu_order'                 => 0,
		'position'                   => 'normal',
		'style'                      => 'seamless',
		'label_placement'            => 'left',
		'instruction_placement'      => 'label',
		'hide_on_screen'             => '',
		'active'                     => 0,
		'description'                => '',
		'show_in_rest'               => 0,
		'acfe_autosync'              => array(
			0 => 'php',
		),
		'acfe_form'                  => 0,
		'acfe_display_title'         => 'Slider cartes de publication',
		'acfe_meta'                  => '',
		'acfe_note'                  => '',
		'lesanimals_save_as_layout'  => 1,
		'lesanimals_layout_slug'     => 'slider-publication-cards',
		'lesanimals_layout_settings' => '',
		'lesanimals_layout_min'      => '',
		'lesanimals_layout_max'      => '',
		'acfe_categories'            => array(
			'flexible' => 'Flexible',
		),
		'modified'                   => 1737386569,
	) );

endif;
