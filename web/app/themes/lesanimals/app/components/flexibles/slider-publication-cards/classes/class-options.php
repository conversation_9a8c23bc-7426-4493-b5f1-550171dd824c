<?php
/**
 * Options for SliderPublicationCards flexible
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\SliderPublicationCards;

use LesAnimals\Core\Options\Options_Abstract;

class Options extends Options_Abstract {

	public array $push_slider_publication_cards_manual_variations;

	/**
	 * Init
	 *
	 * @return void
	 */
	protected function init(): void {
		$this->push_slider_publication_cards_manual_variations = self::get_option( 'push_slider_publication_cards_manual_variations', 'publication' );
	}
}
