<?php
/**
 * Aid for EmbeddedPublication
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\EmbeddedPublication;

use Timber;

trait Aid {

	/**
	 * Get the data
	 *
	 * @param int $pub_id The publication id
	 * @return array
	 */
	private function get_data( int $pub_id ): array {
		if ( ! ( get_post_status( $pub_id ) === 'publish' || get_post_status( $pub_id ) === 'private' ) ) {
			return array();
		}

		$pub = Timber::get_post( $pub_id );
		$media_type = get_field( 'media_type', $pub_id )[ 'value' ];
		$credits = get_field( 'credits', $pub_id );
		$pushed_credits = false;
		if ( ! empty( $credits ) ) {
			$pushed_credits = $this->get_pushed_credits( $credits );
		}
		$data = array(
			'title'                        => $pub->title,
			'link'                         => $pub->link,
			'media_type'                   => $media_type,
			'media_' . $media_type . '_id' => get_field( 'media_' . $media_type . '_id', $pub_id ),
			'credits'                      => $pushed_credits,
			'status'                       => $pub->post_status,
		);

		return $data;
	}

	/**
	 * Get the credits pushed
	 *
	 * @param array|bool $credits Credits
	 * @return array
	 */
	private function get_pushed_credits( array|bool $credits ): array {
		$pushed_credits = array();

		if ( $credits ) {
			foreach ( $credits as $credit ) {
				if ( $credit[ 'display' ] === 'main' ) {
					$label_id = $credit[ 'label' ];
					$label = Timber::get_term( $label_id );
					$credit[ 'label' ] = $label->name;

					$pushed_credits[] = array(
						'label' => $label->name,
						'value' => $credit[ 'txt' ],
					);
				}
			}
		}

		return $pushed_credits;
	}
}
