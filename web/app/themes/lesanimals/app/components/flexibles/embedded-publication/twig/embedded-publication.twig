{% if flx.title and flx.media_type %}
	<section class="flx_embedded_publication">

		{% if flx.media_type == 'video' %}
			{% include 'iframe-vimeo.twig' with {
				video_id: flx.media_video_id,
				title: flx.title,
				class: 'flx_embedded_pub--iframe',
			} %}
		{% elseif flx.media_type == 'img' %}
			{% include 'img.twig' with {
				id: flx.media_img_id,
				class: 'flx_embedded_publication--img_cont',
				width: [ 600, 835, 1070, 1430, 1790 ],
				ratio: 16/9,
				sizes: '(max-width: 640px) calc(100vw - 32px), (max-width: 960px) calc(100vw - 48px), (max-width: 1440px) 70vw, (max-width: 1999px) 63vw, 1192px',
			} %}
		{% endif %}
		<h3 class="flx_embedded_pub--title">
			{% if flx.status == 'publish' %}
				<a href="{{ flx.link }}" class="flx_embedded_pub--title-link">{{ flx.title }}</a>
			{% else %}
				{{ flx.title }}
			{% endif %}
		</h3>
		{% include 'data-list.twig' with {
			data: flx.credits,
			class: 'flx_embedded_pub--credits',
			display: 'inline_large',
		} %}

	</section>
{% endif %}
