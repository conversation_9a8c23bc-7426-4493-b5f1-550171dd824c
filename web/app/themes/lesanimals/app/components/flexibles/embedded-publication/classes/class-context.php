<?php
/**
 * Context for EmbeddedPublication flexible
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\EmbeddedPublication;

use Timber;

class Context {

	use Aid;

	/**
	 * Initialize the flexible
	 * Enables hooking the variables
	 *
	 * @param array $flx The flexible context
	 * @return array
	 */
	public function init( array $flx ): array {
		$flx = array_merge( $flx, $this->get_data( $flx[ 'pub_id' ] ) );

		return $flx;
	}
}
