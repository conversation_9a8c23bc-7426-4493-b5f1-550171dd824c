<?php 

if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
	'key' => 'group_66f2ba2caabd2',
	'title' => 'Flexible - Publication intégrée',
	'fields' => array(
		array(
			'key' => 'field_66f2ba2cafa95',
			'label' => 'Publication',
			'name' => 'pub_id',
			'aria-label' => '',
			'type' => 'post_object',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'publication',
			),
			'post_status' => '',
			'taxonomy' => '',
			'return_format' => 'id',
			'multiple' => 0,
			'max' => '',
			'save_custom' => 0,
			'save_post_status' => 'publish',
			'acfe_add_post' => 0,
			'acfe_edit_post' => 0,
			'acfe_bidirectional' => array(
				'acfe_bidirectional_enabled' => '0',
			),
			'translations' => 'copy_once',
			'required_message' => '',
			'allow_null' => 0,
			'allow_in_bindings' => 0,
			'bidirectional' => 0,
			'ui' => 1,
			'bidirectional_target' => array(
			),
			'save_post_type' => '',
			'min' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'page',
			),
			array(
				'param' => 'page_type',
				'operator' => '!=',
				'value' => 'front_page',
			),
			array(
				'param' => 'page_template',
				'operator' => '!=',
				'value' => 'app/components/pages/faq/controllers/template-faq.php',
			),
		),
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'all',
			),
			array(
				'param' => 'post_type',
				'operator' => '!=',
				'value' => 'page',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'seamless',
	'label_placement' => 'left',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => false,
	'description' => '',
	'show_in_rest' => 0,
	'acfe_autosync' => array(
		0 => 'php',
	),
	'acfe_form' => 0,
	'acfe_display_title' => 'Publication intégrée',
	'acfe_meta' => '',
	'acfe_note' => '',
	'lesanimals_save_as_layout' => 1,
	'lesanimals_layout_slug' => 'embedded-publication',
	'lesanimals_layout_settings' => '',
	'lesanimals_layout_min' => '',
	'lesanimals_layout_max' => '',
	'acfe_categories' => array(
		'flexible' => 'Flexible',
	),
	'modified' => 1737377304,
));

endif;