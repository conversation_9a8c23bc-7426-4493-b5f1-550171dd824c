// *************************************************
// -------- Flexible - Embedded publication --------
// *************************************************


.flx_embedded_publication {

	@extend %flx;
	@extend %flx_margin;
}

.flx_embedded_pub--iframe {
}

.flx_embedded_pub--img_cont {
}

.flx_embedded_pub--title {
	margin-top: 21px;
	font: var(--title_s);
}

.flx_embedded_pub--title-link {
	text-decoration: underline;

	&:hover {
		color: $color_purple_light;
	}
}

.flx_embedded_pub--credits {
	margin-top: 11px;
}

.flx_embedded_pub--credits-item {
	display: flex;
	border-bottom: 1px solid $color_black;
}

.flx_embedded_pub--credits-label {
}

.flx_embedded_pub--credits-value {
}


// -------- Responsive --------

@include query($until: 640px) {

	.flx_embedded_pub--title {
		margin-top: 16px;
	}

}

