{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "0a332eb3f0ae0c4557630ea1fb4c33f5", "packages": [{"name": "ankitpokhrel/tus-php", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/ankitpokhrel/tus-php.git", "reference": "a466a9b835f3ea29ec4df7e37a2dc9e00d194304"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ankitpokhrel/tus-php/zipball/a466a9b835f3ea29ec4df7e37a2dc9e00d194304", "reference": "a466a9b835f3ea29ec4df7e37a2dc9e00d194304", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^7.2", "nesbot/carbon": "^2.67 || ^3.0", "php": "^8.1", "predis/predis": "^2.0.3", "ramsey/uuid": "^4.0", "symfony/console": "^6.0 || ^7.0", "symfony/event-dispatcher": "^6.0 || ^7.0", "symfony/http-foundation": "^6.0 || ^7.0", "symfony/mime": "^6.0 || ^7.0"}, "require-dev": {"ext-pcntl": "*", "friendsofphp/php-cs-fixer": "^3.0", "mockery/mockery": "^1.4.2", "phpunit/phpunit": "^10.5"}, "bin": ["bin/tus"], "type": "library", "extra": {"branch-alias": {"dev-main": "2.3-dev"}}, "autoload": {"psr-4": {"TusPhp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A pure PHP server and client for the tus resumable upload protocol v1.0.0", "support": {"issues": "https://github.com/ankitpokhrel/tus-php/issues", "source": "https://github.com/ankitpokhrel/tus-php/tree/v2.4.0"}, "funding": [{"url": "https://paypal.me/pokharelankit", "type": "custom"}], "time": "2024-02-17T19:37:26+00:00"}, {"name": "brick/math", "version": "0.14.0", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "113a8ee2656b882d4c3164fa31aa6e12cbb7aaa2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/113a8ee2656b882d4c3164fa31aa6e12cbb7aaa2", "reference": "113a8ee2656b882d4c3164fa31aa6e12cbb7aaa2", "shasum": ""}, "require": {"php": "^8.2"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpstan/phpstan": "2.1.22", "phpunit/phpunit": "^11.5"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.14.0"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2025-08-29T12:40:03+00:00"}, {"name": "carbonphp/carbon-doctrine-types", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/dbal": "<4.0.0 || >=5.0.0"}, "require-dev": {"doctrine/dbal": "^4.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/3.2.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2024-02-09T16:56:22+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.10.0", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "b51ac707cfa420b7bfd4e4d5e510ba8008e822b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/b51ac707cfa420b7bfd4e4d5e510ba8008e822b4", "reference": "b51ac707cfa420b7bfd4e4d5e510ba8008e822b4", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^2.3", "guzzlehttp/psr7": "^2.8", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.10.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-08-23T22:36:01+00:00"}, {"name": "guzzlehttp/promises", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "481557b130ef3790cf82b713667b43030dc9c957"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/481557b130ef3790cf82b713667b43030dc9c957", "reference": "481557b130ef3790cf82b713667b43030dc9c957", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.44 || ^9.6.25"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.3.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2025-08-22T14:34:08+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "21dc724a0583619cd1652f673303492272778051"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/21dc724a0583619cd1652f673303492272778051", "reference": "21dc724a0583619cd1652f673303492272778051", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.44 || ^9.6.25"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.8.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-08-23T21:21:41+00:00"}, {"name": "nesbot/carbon", "version": "3.10.3", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "8e3643dcd149ae0fe1d2ff4f2c8e4bbfad7c165f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/8e3643dcd149ae0fe1d2ff4f2c8e4bbfad7c165f", "reference": "8e3643dcd149ae0fe1d2ff4f2c8e4bbfad7c165f", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "<100.0", "ext-json": "*", "php": "^8.1", "psr/clock": "^1.0", "symfony/clock": "^6.3.12 || ^7.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^4.4.18 || ^5.2.1 || ^6.0 || ^7.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^3.6.3 || ^4.0", "doctrine/orm": "^2.15.2 || ^3.0", "friendsofphp/php-cs-fixer": "^v3.87.1", "kylekatarnls/multi-tester": "^2.5.3", "phpmd/phpmd": "^2.15.0", "phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^2.1.22", "phpunit/phpunit": "^10.5.53", "squizlabs/php_codesniffer": "^3.13.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2025-09-06T13:39:36+00:00"}, {"name": "predis/predis", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "f49e13ee3a2a825631562aa0223ac922ec5d058b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/f49e13ee3a2a825631562aa0223ac922ec5d058b", "reference": "f49e13ee3a2a825631562aa0223ac922ec5d058b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.3", "phpstan/phpstan": "^1.9", "phpunit/phpcov": "^6.0 || ^8.0", "phpunit/phpunit": "^8.0 || ^9.4"}, "suggest": {"ext-relay": "Faster connection with in-memory caching (>=0.6.2)"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "A flexible and feature-complete Redis/Valkey client for PHP.", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.4.0"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2025-04-30T15:16:02+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/344572933ad0181accbf4ba763e85a0306a8c5e2", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.45", "fakerphp/faker": "^1.24", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^2.1", "mockery/mockery": "^1.6", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4", "phpspec/prophecy-phpunit": "^2.3", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.5", "ramsey/coding-standard": "^2.3", "ramsey/conventional-commits": "^1.6", "roave/security-advisories": "dev-latest"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/2.1.1"}, "time": "2025-03-22T05:38:12+00:00"}, {"name": "ramsey/uuid", "version": "4.9.1", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "81f941f6f729b1e3ceea61d9d014f8b6c6800440"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/81f941f6f729b1e3ceea61d9d014f8b6c6800440", "reference": "81f941f6f729b1e3ceea61d9d014f8b6c6800440", "shasum": ""}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12 || ^0.13 || ^0.14", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.25", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "ergebnis/composer-normalize": "^2.47", "mockery/mockery": "^1.6", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.6", "php-mock/php-mock-mockery": "^1.5", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpbench/phpbench": "^1.2.14", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^9.6", "slevomat/coding-standard": "^8.18", "squizlabs/php_codesniffer": "^3.13"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.9.1"}, "time": "2025-09-04T20:59:21+00:00"}, {"name": "symfony/cache", "version": "v7.3.2", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "6621a2bee5373e3e972b2ae5dbedd5ac899d8cb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/6621a2bee5373e3e972b2ae5dbedd5ac899d8cb6", "reference": "6621a2bee5373e3e972b2ae5dbedd5ac899d8cb6", "shasum": ""}, "require": {"php": ">=8.2", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^3.6", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.4|^7.0"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/dependency-injection": "<6.4", "symfony/http-kernel": "<6.4", "symfony/var-dumper": "<6.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/clock": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/filesystem": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v7.3.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-30T17:13:41+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/5d68a57d66910405e5c0b63d6f0af941e66fc868", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T15:25:07+00:00"}, {"name": "symfony/clock", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/clock.git", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/clock/zipball/b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "shasum": ""}, "require": {"php": ">=8.2", "psr/clock": "^1.0", "symfony/polyfill-php83": "^1.28"}, "provide": {"psr/clock-implementation": "1.0"}, "type": "library", "autoload": {"files": ["Resources/now.php"], "psr-4": {"Symfony\\Component\\Clock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Decouples applications from the system clock", "homepage": "https://symfony.com", "keywords": ["clock", "psr20", "time"], "support": {"source": "https://github.com/symfony/clock/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/config", "version": "v7.3.2", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "faef36e271bbeb74a9d733be4b56419b157762e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/faef36e271bbeb74a9d733be4b56419b157762e2", "reference": "faef36e271bbeb74a9d733be4b56419b157762e2", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/filesystem": "^7.1", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<6.4", "symfony/service-contracts": "<2.5"}, "require-dev": {"symfony/event-dispatcher": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v7.3.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-26T13:55:06+00:00"}, {"name": "symfony/console", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "cb0102a1c5ac3807cf3fdf8bea96007df7fdbea7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/cb0102a1c5ac3807cf3fdf8bea96007df7fdbea7", "reference": "cb0102a1c5ac3807cf3fdf8bea96007df7fdbea7", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^7.2"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-25T06:35:40+00:00"}, {"name": "symfony/dependency-injection", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "ab6c38dad5da9b15b1f7afb2f5c5814112e70261"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/ab6c38dad5da9b15b1f7afb2f5c5814112e70261", "reference": "ab6c38dad5da9b15b1f7afb2f5c5814112e70261", "shasum": ""}, "require": {"php": ">=8.2", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^3.5", "symfony/var-exporter": "^6.4.20|^7.2.5"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<6.4", "symfony/finder": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"psr/container-implementation": "1.1|2.0", "symfony/service-implementation": "1.1|2.0|3.0"}, "require-dev": {"symfony/config": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-14T09:54:27+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/error-handler", "version": "v7.3.2", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "0b31a944fcd8759ae294da4d2808cbc53aebd0c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/0b31a944fcd8759ae294da4d2808cbc53aebd0c3", "reference": "0b31a944fcd8759ae294da4d2808cbc53aebd0c3", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^6.4|^7.0"}, "conflict": {"symfony/deprecation-contracts": "<2.5", "symfony/http-kernel": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-kernel": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/webpack-encore-bundle": "^1.0|^2.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v7.3.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-07T08:17:57+00:00"}, {"name": "symfony/event-dispatcher", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "b7dc69e71de420ac04bc9ab830cf3ffebba48191"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b7dc69e71de420ac04bc9ab830cf3ffebba48191", "reference": "b7dc69e71de420ac04bc9ab830cf3ffebba48191", "shasum": ""}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-13T11:49:31+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "59eb412e93815df44f05f342958efa9f46b1e586"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/59eb412e93815df44f05f342958efa9f46b1e586", "reference": "59eb412e93815df44f05f342958efa9f46b1e586", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/filesystem", "version": "v7.3.2", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "edcbb768a186b5c3f25d0643159a787d3e63b7fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/edcbb768a186b5c3f25d0643159a787d3e63b7fd", "reference": "edcbb768a186b5c3f25d0643159a787d3e63b7fd", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v7.3.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-07T08:17:47+00:00"}, {"name": "symfony/finder", "version": "v7.3.2", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "2a6614966ba1074fa93dae0bc804227422df4dfe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/2a6614966ba1074fa93dae0bc804227422df4dfe", "reference": "2a6614966ba1074fa93dae0bc804227422df4dfe", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.3.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-15T13:41:35+00:00"}, {"name": "symfony/framework-bundle", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "19ec4ab6be90322ed190e041e2404a976ed22571"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/19ec4ab6be90322ed190e041e2404a976ed22571", "reference": "19ec4ab6be90322ed190e041e2404a976ed22571", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "ext-xml": "*", "php": ">=8.2", "symfony/cache": "^6.4|^7.0", "symfony/config": "^7.3", "symfony/dependency-injection": "^7.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^7.3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/filesystem": "^7.1", "symfony/finder": "^6.4|^7.0", "symfony/http-foundation": "^7.3", "symfony/http-kernel": "^7.2", "symfony/polyfill-mbstring": "~1.0", "symfony/routing": "^6.4|^7.0"}, "conflict": {"doctrine/persistence": "<1.3", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/asset": "<6.4", "symfony/asset-mapper": "<6.4", "symfony/clock": "<6.4", "symfony/console": "<6.4", "symfony/dom-crawler": "<6.4", "symfony/dotenv": "<6.4", "symfony/form": "<6.4", "symfony/http-client": "<6.4", "symfony/json-streamer": ">=7.4", "symfony/lock": "<6.4", "symfony/mailer": "<6.4", "symfony/messenger": "<6.4", "symfony/mime": "<6.4", "symfony/object-mapper": ">=7.4", "symfony/property-access": "<6.4", "symfony/property-info": "<6.4", "symfony/runtime": "<6.4.13|>=7.0,<7.1.6", "symfony/scheduler": "<6.4.4|>=7.0.0,<7.0.4", "symfony/security-core": "<6.4", "symfony/security-csrf": "<7.2", "symfony/serializer": "<7.2.5", "symfony/stopwatch": "<6.4", "symfony/translation": "<7.3", "symfony/twig-bridge": "<6.4", "symfony/twig-bundle": "<6.4", "symfony/validator": "<6.4", "symfony/web-profiler-bundle": "<6.4", "symfony/webhook": "<7.2", "symfony/workflow": "<7.3.0-beta2"}, "require-dev": {"doctrine/persistence": "^1.3|^2|^3", "dragonmantank/cron-expression": "^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "seld/jsonlint": "^1.10", "symfony/asset": "^6.4|^7.0", "symfony/asset-mapper": "^6.4|^7.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/clock": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/dotenv": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/html-sanitizer": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/json-streamer": "7.3.*", "symfony/lock": "^6.4|^7.0", "symfony/mailer": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/notifier": "^6.4|^7.0", "symfony/object-mapper": "^v7.3.0-beta2", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/scheduler": "^6.4.4|^7.0.4", "symfony/security-bundle": "^6.4|^7.0", "symfony/semaphore": "^6.4|^7.0", "symfony/serializer": "^7.2.5", "symfony/stopwatch": "^6.4|^7.0", "symfony/string": "^6.4|^7.0", "symfony/translation": "^7.3", "symfony/twig-bundle": "^6.4|^7.0", "symfony/type-info": "^7.1.8", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/web-link": "^6.4|^7.0", "symfony/webhook": "^7.2", "symfony/workflow": "^7.3", "symfony/yaml": "^6.4|^7.0", "twig/twig": "^3.12"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration between Symfony components and the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/framework-bundle/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-27T07:45:05+00:00"}, {"name": "symfony/http-foundation", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "7475561ec27020196c49bb7c4f178d33d7d3dc00"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/7475561ec27020196c49bb7c4f178d33d7d3dc00", "reference": "7475561ec27020196c49bb7c4f178d33d7d3dc00", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/clock": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-20T08:04:18+00:00"}, {"name": "symfony/http-kernel", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "72c304de37e1a1cec6d5d12b81187ebd4850a17b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/72c304de37e1a1cec6d5d12b81187ebd4850a17b", "reference": "72c304de37e1a1cec6d5d12b81187ebd4850a17b", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^7.3", "symfony/http-foundation": "^7.3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<6.4", "symfony/cache": "<6.4", "symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<6.4", "symfony/form": "<6.4", "symfony/http-client": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/mailer": "<6.4", "symfony/messenger": "<6.4", "symfony/translation": "<6.4", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<6.4", "symfony/validator": "<6.4", "symfony/var-dumper": "<6.4", "twig/twig": "<3.12"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/clock": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^7.1", "symfony/routing": "^6.4|^7.0", "symfony/serializer": "^7.1", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0", "symfony/var-exporter": "^6.4|^7.0", "twig/twig": "^3.12"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-29T08:23:45+00:00"}, {"name": "symfony/intl", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/intl.git", "reference": "754d5ad02c889e380efc5a74fa3f6cfe56b7454d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/intl/zipball/754d5ad02c889e380efc5a74fa3f6cfe56b7454d", "reference": "754d5ad02c889e380efc5a74fa3f6cfe56b7454d", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"symfony/string": "<7.1"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Intl\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides access to the localization data of the ICU library", "homepage": "https://symfony.com", "keywords": ["i18n", "icu", "internationalization", "intl", "l10n", "localization"], "support": {"source": "https://github.com/symfony/intl/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-19T14:06:46+00:00"}, {"name": "symfony/mime", "version": "v7.3.2", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "e0a0f859148daf1edf6c60b398eb40bfc96697d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/e0a0f859148daf1edf6c60b398eb40bfc96697d1", "reference": "e0a0f859148daf1edf6c60b398eb40bfc96697d1", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v7.3.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-15T13:41:35+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.33.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.33.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "380872130d3a5dd3ace2f4010d95125fde5d5c70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/380872130d3a5dd3ace2f4010d95125fde5d5c70", "reference": "380872130d3a5dd3ace2f4010d95125fde5d5c70", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T09:58:17+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.33.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/9614ac4d8061dc257ecc64cba1b140873dce8ad3", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-10T14:38:51+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.33.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.33.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.33.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "17f6f9a6b1735c0f163024d959f700cfbc5155e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/17f6f9a6b1735c0f163024d959f700cfbc5155e5", "reference": "17f6f9a6b1735c0f163024d959f700cfbc5155e5", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-08T02:45:35+00:00"}, {"name": "symfony/routing", "version": "v7.3.2", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "7614b8ca5fa89b9cd233e21b627bfc5774f586e4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/7614b8ca5fa89b9cd233e21b627bfc5774f586e4", "reference": "7614b8ca5fa89b9cd233e21b627bfc5774f586e4", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"symfony/config": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/yaml": "<6.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v7.3.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-15T11:36:08+00:00"}, {"name": "symfony/service-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-25T09:37:31+00:00"}, {"name": "symfony/string", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "17a426cce5fd1f0901fefa9b2a490d0038fd3c9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/17a426cce5fd1f0901fefa9b2a490d0038fd3c9c", "reference": "17a426cce5fd1f0901fefa9b2a490d0038fd3c9c", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-25T06:35:40+00:00"}, {"name": "symfony/translation", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "e0837b4cbcef63c754d89a4806575cada743a38d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/e0837b4cbcef63c754d89a4806575cada743a38d", "reference": "e0837b4cbcef63c754d89a4806575cada743a38d", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"nikic/php-parser": "<5.0", "symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-01T21:02:37+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-27T08:32:26+00:00"}, {"name": "symfony/twig-bridge", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "33558f013b7f6ed72805527c8405cae0062e47c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/33558f013b7f6ed72805527c8405cae0062e47c5", "reference": "33558f013b7f6ed72805527c8405cae0062e47c5", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/translation-contracts": "^2.5|^3", "twig/twig": "^3.21"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/console": "<6.4", "symfony/form": "<6.4", "symfony/http-foundation": "<6.4", "symfony/http-kernel": "<6.4", "symfony/mime": "<6.4", "symfony/serializer": "<6.4", "symfony/translation": "<6.4", "symfony/workflow": "<6.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/asset": "^6.4|^7.0", "symfony/asset-mapper": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/emoji": "^7.1", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/form": "^6.4.20|^7.2.5", "symfony/html-sanitizer": "^6.4|^7.0", "symfony/http-foundation": "^7.3", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/property-info": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/security-acl": "^2.8|^3.0", "symfony/security-core": "^6.4|^7.0", "symfony/security-csrf": "^6.4|^7.0", "symfony/security-http": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/web-link": "^6.4|^7.0", "symfony/workflow": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0", "twig/cssinliner-extra": "^3", "twig/inky-extra": "^3", "twig/markdown-extra": "^3"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Twig with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bridge/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-18T13:10:53+00:00"}, {"name": "symfony/twig-bundle", "version": "v7.3.2", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "5d85220df4d8d79e6a9ca57eea6f70004de39657"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/5d85220df4d8d79e6a9ca57eea6f70004de39657", "reference": "5d85220df4d8d79e6a9ca57eea6f70004de39657", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "php": ">=8.2", "symfony/config": "^7.3", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/twig-bridge": "^7.3", "twig/twig": "^3.12"}, "conflict": {"symfony/framework-bundle": "<6.4", "symfony/translation": "<6.4"}, "require-dev": {"symfony/asset": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/web-link": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of Twig into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bundle/tree/v7.3.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-10T08:47:49+00:00"}, {"name": "symfony/var-dumper", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "34d8d4c4b9597347306d1ec8eb4e1319b1e6986f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/34d8d4c4b9597347306d1ec8eb4e1319b1e6986f", "reference": "34d8d4c4b9597347306d1ec8eb4e1319b1e6986f", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/uid": "^6.4|^7.0", "twig/twig": "^3.12"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-13T11:49:31+00:00"}, {"name": "symfony/var-exporter", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "d4dfcd2a822cbedd7612eb6fbd260e46f87b7137"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/d4dfcd2a822cbedd7612eb6fbd260e46f87b7137", "reference": "d4dfcd2a822cbedd7612eb6fbd260e46f87b7137", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-18T13:10:53+00:00"}, {"name": "timber/timber", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/timber/timber.git", "reference": "5fc65f235323e8119fddc3cadbfef39ec97a663a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/timber/timber/zipball/5fc65f235323e8119fddc3cadbfef39ec97a663a", "reference": "5fc65f235323e8119fddc3cadbfef39ec97a663a", "shasum": ""}, "require": {"php": "^8.1", "twig/twig": "^3.5"}, "require-dev": {"ergebnis/composer-normalize": "^2.28", "php-parallel-lint/php-parallel-lint": "^1.3", "php-stubs/acf-pro-stubs": "^6.0", "php-stubs/wp-cli-stubs": "^2.0", "phpro/grumphp": "^2.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.7", "phpunit/phpunit": "^9.0", "rector/rector": "^1.0", "squizlabs/php_codesniffer": "^3.0", "symplify/easy-coding-standard": "^12.0", "szepeviktor/phpstan-wordpress": "^1.1", "twig/cache-extra": "^3.3", "wpackagist-plugin/advanced-custom-fields": "^6.0", "wpackagist-plugin/co-authors-plus": "^3.6", "yoast/wp-test-utils": "^1.2"}, "suggest": {"php-coveralls/php-coveralls": "^2.0 for code coverage", "twig/cache-extra": "For using the cache tag in Twig"}, "type": "library", "autoload": {"psr-4": {"Timber\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://basedonline.nl"}, {"name": "<PERSON><PERSON>", "email": "lukas.g<PERSON><PERSON><PERSON>@mind.ch", "homepage": "https://www.mind.ch"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://n5s.dev"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://upstatement.com"}, {"name": "Timber Community", "homepage": "https://github.com/timber/timber"}], "description": "Create WordPress themes with beautiful OOP code and the Twig Template Engine", "homepage": "https://timber.upstatement.com", "keywords": ["templating", "themes", "timber", "twig", "wordpress"], "support": {"docs": "https://timber.github.io/docs/", "issues": "https://github.com/timber/timber/issues", "source": "https://github.com/timber/timber"}, "funding": [{"url": "https://opencollective.com/timber", "type": "open_collective"}], "time": "2024-05-15T13:47:33+00:00"}, {"name": "twig/extra-bundle", "version": "v3.10.0", "source": {"type": "git", "url": "https://github.com/twigphp/twig-extra-bundle.git", "reference": "cdc6e23aeb7f4953c1039568c3439aab60c56454"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/twig-extra-bundle/zipball/cdc6e23aeb7f4953c1039568c3439aab60c56454", "reference": "cdc6e23aeb7f4953c1039568c3439aab60c56454", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/framework-bundle": "^5.4|^6.4|^7.0", "symfony/twig-bundle": "^5.4|^6.4|^7.0", "twig/twig": "^3.0"}, "require-dev": {"league/commonmark": "^1.0|^2.0", "symfony/phpunit-bridge": "^6.4|^7.0", "twig/cache-extra": "^3.0", "twig/cssinliner-extra": "^3.0", "twig/html-extra": "^3.0", "twig/inky-extra": "^3.0", "twig/intl-extra": "^3.0", "twig/markdown-extra": "^3.0", "twig/string-extra": "^3.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Twig\\Extra\\TwigExtraBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}], "description": "A Symfony bundle for extra Twig extensions", "homepage": "https://twig.symfony.com", "keywords": ["bundle", "extra", "twig"], "support": {"source": "https://github.com/twigphp/twig-extra-bundle/tree/v3.10.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2024-05-11T07:35:57+00:00"}, {"name": "twig/intl-extra", "version": "v3.10.0", "source": {"type": "git", "url": "https://github.com/twigphp/intl-extra.git", "reference": "693f6beb8ca91fc6323e01b3addf983812f65c93"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/intl-extra/zipball/693f6beb8ca91fc6323e01b3addf983812f65c93", "reference": "693f6beb8ca91fc6323e01b3addf983812f65c93", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/intl": "^5.4|^6.4|^7.0", "twig/twig": "^3.10"}, "require-dev": {"symfony/phpunit-bridge": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Twig\\Extra\\Intl\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}], "description": "A Twig extension for Intl", "homepage": "https://twig.symfony.com", "keywords": ["intl", "twig"], "support": {"source": "https://github.com/twigphp/intl-extra/tree/v3.10.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2024-05-11T07:35:57+00:00"}, {"name": "twig/twig", "version": "v3.21.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/285123877d4dd97dd7c11842ac5fb7e86e60d81d", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpstan/phpstan": "^2.0", "psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.4|^7.0"}, "type": "library", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.21.1"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-05-03T07:21:55+00:00"}, {"name": "vimeo/vimeo-api", "version": "3.0.10", "source": {"type": "git", "url": "https://github.com/vimeo/vimeo.php.git", "reference": "79885b642594b17f6c356dc949d3e4a58c356c9b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vimeo/vimeo.php/zipball/79885b642594b17f6c356dc949d3e4a58c356c9b", "reference": "79885b642594b17f6c356dc949d3e4a58c356c9b", "shasum": ""}, "require": {"ankitpokhrel/tus-php": "^v1.0.0 || ^v2.0.0", "ext-curl": "*", "ext-json": "*", "php": ">=7.1.0"}, "require-dev": {"php-coveralls/php-coveralls": "^1.0", "phpunit/phpunit": "^7.4", "vimeo/psalm": "^2.0"}, "type": "library", "autoload": {"psr-4": {"Vimeo\\": "src/Vimeo"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Vimeo", "homepage": "http://vimeo.com"}], "description": "Official PHP library for the Vimeo API.", "homepage": "https://github.com/vimeo/vimeo.php", "keywords": ["video", "vimeo"], "support": {"issues": "https://github.com/vimeo/vimeo.php/issues", "source": "https://github.com/vimeo/vimeo.php/tree/3.0.10"}, "time": "2022-02-18T14:05:55+00:00"}], "packages-dev": [{"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/composer-installer.git", "reference": "e9cf5e4bbf7eeaf9ef5db34938942602838fc2b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/composer-installer/zipball/e9cf5e4bbf7eeaf9ef5db34938942602838fc2b1", "reference": "e9cf5e4bbf7eeaf9ef5db34938942602838fc2b1", "shasum": ""}, "require": {"composer-plugin-api": "^2.2", "php": ">=5.4", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "^2.2", "ext-json": "*", "ext-zip": "*", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpcompatibility/php-compatibility": "^9.0", "yoast/phpunit-polyfills": "^1.0"}, "type": "composer-plugin", "extra": {"class": "PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "autoload": {"psr-4": {"PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://frenck.dev", "role": "Open source developer"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/PHPCSStandards/composer-installer/issues", "security": "https://github.com/PHPCSStandards/composer-installer/security/policy", "source": "https://github.com/PHPCSStandards/composer-installer"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "time": "2025-07-17T20:45:56+00:00"}, {"name": "phpcsstandards/phpcsextra", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHPCSExtra.git", "reference": "11d387c6642b6e4acaf0bd9bf5203b8cca1ec489"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSExtra/zipball/11d387c6642b6e4acaf0bd9bf5203b8cca1ec489", "reference": "11d387c6642b6e4acaf0bd9bf5203b8cca1ec489", "shasum": ""}, "require": {"php": ">=5.4", "phpcsstandards/phpcsutils": "^1.0.9", "squizlabs/php_codesniffer": "^3.8.0"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcsstandards/phpcsdevcs": "^1.1.6", "phpcsstandards/phpcsdevtools": "^1.2.1", "phpunit/phpunit": "^4.5 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSExtra/graphs/contributors"}], "description": "A collection of sniffs and standards for use with PHP_CodeSniffer.", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHPCSExtra/issues", "security": "https://github.com/PHPCSStandards/PHPCSExtra/security/policy", "source": "https://github.com/PHPCSStandards/PHPCSExtra"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2023-12-08T16:49:07+00:00"}, {"name": "phpcsstandards/phpcsutils", "version": "1.0.12", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHPCSUtils.git", "reference": "87b233b00daf83fb70f40c9a28692be017ea7c6c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSUtils/zipball/87b233b00daf83fb70f40c9a28692be017ea7c6c", "reference": "87b233b00daf83fb70f40c9a28692be017ea7c6c", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.4.1 || ^0.5 || ^0.6.2 || ^0.7 || ^1.0", "php": ">=5.4", "squizlabs/php_codesniffer": "^3.10.0 || 4.0.x-dev@dev"}, "require-dev": {"ext-filter": "*", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcsstandards/phpcsdevcs": "^1.1.6", "yoast/phpunit-polyfills": "^1.1.0 || ^2.0.0"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "autoload": {"classmap": ["PHPCSUtils/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSUtils/graphs/contributors"}], "description": "A suite of utility functions for use with PHP_CodeSniffer", "homepage": "https://phpcsutils.com/", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "phpcs3", "standards", "static analysis", "tokens", "utility"], "support": {"docs": "https://phpcsutils.com/", "issues": "https://github.com/PHPCSStandards/PHPCSUtils/issues", "security": "https://github.com/PHPCSStandards/PHPCSUtils/security/policy", "source": "https://github.com/PHPCSStandards/PHPCSUtils"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2024-05-20T13:34:27+00:00"}, {"name": "qferr/mjml-php", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/qferr/mjml-php.git", "reference": "c7185024d8b561bd3b532b29f46cba0f4b789b70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qferr/mjml-php/zipball/c7185024d8b561bd3b532b29f46cba0f4b789b70", "reference": "c7185024d8b561bd3b532b29f46cba0f4b789b70", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": ">=7.2"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "autoload": {"psr-4": {"Qferrer\\Mjml\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple PHP library to render MJML to HTML.", "support": {"issues": "https://github.com/qferr/mjml-php/issues", "source": "https://github.com/qferr/mjml-php/tree/2.0.0"}, "time": "2022-04-09T21:34:38+00:00"}, {"name": "qferr/mjml-twig", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/qferr/mjml-twig.git", "reference": "3bf8db325fd95fcdfb860c174cbc2fb01104992d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qferr/mjml-twig/zipball/3bf8db325fd95fcdfb860c174cbc2fb01104992d", "reference": "3bf8db325fd95fcdfb860c174cbc2fb01104992d", "shasum": ""}, "require": {"php": ">=7.2", "qferr/mjml-php": "^2.0", "twig/twig": "^2.0|^3.0"}, "require-dev": {"phpunit/phpunit": "~7.0|~8.0"}, "type": "library", "autoload": {"psr-4": {"Qferrer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Twig extension that provides a filter that processes a mjml email template", "support": {"issues": "https://github.com/qferr/mjml-twig/issues", "source": "https://github.com/qferr/mjml-twig/tree/2.0.1"}, "time": "2022-05-27T08:43:39+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.10.1", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "reference": "8f90f7a53ce271935282967f53d0894f8f1ff877"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/8f90f7a53ce271935282967f53d0894f8f1ff877", "reference": "8f90f7a53ce271935282967f53d0894f8f1ff877", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "bin": ["bin/phpcbf", "bin/phpcs"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2024-05-22T21:24:41+00:00"}, {"name": "wp-coding-standards/wpcs", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/WordPress/WordPress-Coding-Standards.git", "reference": "9333efcbff231f10dfd9c56bb7b65818b4733ca7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/WordPress-Coding-Standards/zipball/9333efcbff231f10dfd9c56bb7b65818b4733ca7", "reference": "9333efcbff231f10dfd9c56bb7b65818b4733ca7", "shasum": ""}, "require": {"ext-filter": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-xmlreader": "*", "php": ">=5.4", "phpcsstandards/phpcsextra": "^1.2.1", "phpcsstandards/phpcsutils": "^1.0.10", "squizlabs/php_codesniffer": "^3.9.0"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.0", "phpcsstandards/phpcsdevtools": "^1.2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "suggest": {"ext-iconv": "For improved results", "ext-mbstring": "For improved results"}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Contributors", "homepage": "https://github.com/WordPress/WordPress-Coding-Standards/graphs/contributors"}], "description": "PHP_CodeSniffer rules (sniffs) to enforce WordPress coding conventions", "keywords": ["phpcs", "standards", "static analysis", "wordpress"], "support": {"issues": "https://github.com/WordPress/WordPress-Coding-Standards/issues", "source": "https://github.com/WordPress/WordPress-Coding-Standards", "wiki": "https://github.com/WordPress/WordPress-Coding-Standards/wiki"}, "funding": [{"url": "https://opencollective.com/php_codesniffer", "type": "custom"}], "time": "2024-03-25T16:39:00+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=8.1"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}