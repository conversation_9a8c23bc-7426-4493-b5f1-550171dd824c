{"extends": ["plugin:@wordpress/eslint-plugin/jsdoc", "plugin:@wordpress/eslint-plugin/esnext", "plugin:@wordpress/eslint-plugin/custom"], "env": {"browser": true, "node": true}, "parser": "@babel/eslint-parser", "parserOptions": {"requireConfigFile": false, "babelOptions": {"plugins": ["@babel/plugin-syntax-import-assertions"]}}, "globals": {"$": "readonly", "Cash": "readonly", "gsap": "readonly", "ScrollTrigger": "readonly", "ScrollSmoother": "readonly", "Draggable": "readonly"}, "rules": {"linebreak-style": 0, "@wordpress/no-global-active-element": 0}}