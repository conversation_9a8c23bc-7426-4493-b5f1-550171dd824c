<?php
/**
 * Entry point for the WordPress theme
 *
 * @package lesanimals
 */

namespace LesAnimals;

/**
 * Composer autoload
 */
require_once __DIR__ . '/vendor/autoload.php';
require_once ABSPATH . 'wp-admin/includes/plugin.php';

use <PERSON><PERSON>\URLHelper;
use LesAnimals\Components\Pages\Account\User;

class Functions {

	/**
	 * Class construct
	 */
	public function __construct() {
		add_action( 'after_setup_theme', array( $this, 'after_setup_theme' ), 1 );
	}

	/**
	 * Trigger the theme initialization as early as possible to avoid any conflict
	 * and to allows other `after_setup_theme` hooks to be called after
	 *
	 * @return void
	 */
	public function after_setup_theme(): void {
		load_theme_textdomain( 'lesanimals', get_stylesheet_directory() . '/app/core/languages/l10n' );

		if ( ! $this->lesanimals_check_required() ) {
			return;
		}

		// phpcs:ignore WordPress.Security.NonceVerification.Recommended
		if ( array_key_exists( 'acfe_wysiwyg_source_code', $_GET ) ||
			( defined( 'LESANIMALS_IS_MIGRATING' ) && LESANIMALS_IS_MIGRATING )
		) {
			return;
		}

		$this->set_ray_configs();
		$this->autoload_files();
		$this->define_constants();
	}

	/**
	 * Check various requirements to allow the theme to initialize
	 *
	 * @return bool
	 */
	private function lesanimals_check_required(): bool {
		$valid = true;

		$required_plugins = array(
			'Advanced Custom Fields PRO'           => array(
				'slug' => 'advanced-custom-fields-pro/acf.php',
				'url'  => 'https://www.advancedcustomfields.com/my-account/view-licenses/',
			),
			'Advanced Custom Fields: Extended PRO' => array(
				'slug' => 'acf-extended-pro/acf-extended.php',
				'url'  => 'https://www.acf-extended.com/account',
			),
		);
		foreach ( $required_plugins as $required_plugin_name => $required_plugin_data ) {
			if ( ! is_plugin_active( $required_plugin_data[ 'slug' ] ) ) {

				if ( ! defined( 'WP_CLI' ) || ! WP_CLI ) {
					wp_admin_notice(
					// translators: the name of the plugin
					// translators: the link url to download the plugin
					// translators: the end of the link tag
					// translators: the link url to install a plugin
					// translators: the end of the link tag
						sprintf( __( 'The plugin %1$s must be activated to run the website.<br>%2$sSee the download page%3$s&nbsp;%4$sInstall plugin page%5$s', 'lesanimals' ),
							$required_plugin_name,
							'<a class="button" href="' . $required_plugin_data[ 'url' ] . '" target="_blank">',
							'</a>',
							'<a class="button" href="' . admin_url( 'plugin-install.php' ) . '">',
							'</a>',
						),
						array(
							'type' => 'error',
						)
					);
				}
				$valid = false;
			}
		}

		$required_classes = array(
			'Timber' => array(
				'slug' => '\Timber\Timber',
				'url'  => 'https://github.com/timber/timber',
			),
		);
		foreach ( $required_classes as $required_class_name => $required_data ) {
			if ( ! class_exists( $required_data[ 'slug' ] ) ) {
				wp_admin_notice(
				// translators: the class name
				// translators: the module url
					sprintf( __( 'The module %1$s is missing. <a href="%2$s" target="_blank">See the download page</a>', 'lesanimals' ), $required_class_name, $required_data[ 'url' ] ),
					array(
						'type' => 'error',
					)
				);
				$valid = false;
			}
		}

		return $valid;
	}

	/**
	 * Set ray configs
	 *
	 * @return void
	 */
	private function set_ray_configs(): void {
		if ( WP_ENV === 'development' && class_exists( 'Spatie\WordPressRay\Ray' ) && function_exists( 'ray' ) && method_exists( ray(), 'showMails' ) ) {
			ray()->showMails();
		}
	}

	/**
	 * Autoload files
	 *
	 * @return void
	 */
	private function autoload_files(): void {
		$this->lesanimals_autoload_php_files( get_stylesheet_directory() . '/app/{,*/*,*/*/*,*/*/*/*}', '/class-config.php' );
		$this->lesanimals_autoload_php_files( get_stylesheet_directory() . '/app/{,*/*,*/*/*,*/*/*/*}', '/class-aid*.php' );
		$this->lesanimals_autoload_php_files( get_stylesheet_directory() . '/app/core' );
		$this->lesanimals_autoload_php_files( get_stylesheet_directory() . '/app/core/*' );
		$this->lesanimals_autoload_php_files( get_stylesheet_directory() . '/app/core/*/classes' );
		$this->lesanimals_autoload_php_files( get_stylesheet_directory() . '/app/components/*/*' );
		$this->lesanimals_autoload_php_files( get_stylesheet_directory() . '/app/components/*/*/classes' );
	}

	/**
	 * Autoload some php files
	 *
	 * @param string $path         The absolute path to a directory.
	 * @param string $file_pattern The file pattern
	 * @return void
	 */
	private function lesanimals_autoload_php_files( string $path, string $file_pattern = '/*.php' ): void {
		$all_files = glob( $path . $file_pattern, GLOB_BRACE );
		$excluded_files = array( '.', '..' );

		foreach ( $all_files as $file ) {
			if ( file_exists( $file ) && ! in_array( $file, $excluded_files, true ) ) {
				require_once $file;
			}
		}
	}

	/**
	 * Setup constants
	 *
	 * @return void
	 */
	private function define_constants(): void {
		define( 'LESANIMALS_HOME_URL', pll_home_url() );
		define( 'LESANIMALS_DISPLAY_URL', str_replace( array( 'https://', 'http://' ), '', home_url() ) );
		define( 'LESANIMALS_THEME_URL', get_template_directory_uri() );
		define( 'LESANIMALS_THEME_PATH', get_template_directory() );
		define( 'LESANIMALS_AJAX_URL', admin_url( 'admin-ajax.php' ) );

		$current_url = URLHelper::get_current_url();
		// remove port added by Local
		if ( IS_DEV ) {
			if ( ! empty( $_SERVER ) && array_key_exists( 'SERVER_PORT', $_SERVER ) ) {
				$server_port = sanitize_key( $_SERVER[ 'SERVER_PORT' ] );
				$current_url = str_replace( ':' . $server_port, '', $current_url );
			}
		}
		define( 'LESANIMALS_CURRENT_URL', $current_url );

		define( 'LESANIMALS_3D_DIR', LESANIMALS_THEME_URL . '/app/assets/dist/3d/' );
		define( 'LESANIMALS_IMG_DIR', LESANIMALS_THEME_URL . '/app/assets/dist/img/' );
		define( 'LESANIMALS_SVG_DIR', LESANIMALS_THEME_URL . '/app/assets/dist/svg/' );
		define( 'LESANIMALS_VIDEO_DIR', LESANIMALS_THEME_URL . '/app/assets/dist/video/' );

		$user = User::get();
		define( 'LESANIMALS_IS_ADMINISTRATOR', $user->is_admin );
	}
}

new Functions();
