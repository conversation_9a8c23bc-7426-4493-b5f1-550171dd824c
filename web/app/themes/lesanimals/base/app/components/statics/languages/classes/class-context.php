<?php
/**
 * Context for Languages
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Statics\Languages;

use Timber;

class Context {

	/**
	 * Class construct
	 */
	public function __construct() {
		if ( LESANIMALS_LANGUAGES ) {
			add_filter( 'timber/context', array( $this, 'setup_context' ) );
		}
	}

	/**
	 * Add variables to Timber Context
	 *
	 * @param array $context Timber context
	 * @return array
	 */
	public function setup_context( array $context ): array {
		$context[ 'lg' ] = get_fields( 'options' )[ 'lg' ];

		$other_lg_code = LESANIMALS_CURRENT_LANG_CODE === 'fr' ? 'en' : 'fr';

		$post_trans = pll_get_post_translations( acfe_get_post_id() );
		if ( array_key_exists( $other_lg_code, $post_trans ) ) {
			$post_alt = Timber::get_post( $post_trans[ $other_lg_code ] );
			$context[ 'lg' ][ 'link_alt' ] = $post_alt->link;
		}

		return $context;
	}
}

new Context();
