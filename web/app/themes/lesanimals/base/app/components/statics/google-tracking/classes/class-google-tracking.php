<?php
/**
 * Google Tracking
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Statics\GoogleTracking;

use Timber;

class Google_Tracking {

	private $google_tracking = false;

	/**
	 * Class construct
	 */
	public function __construct() {
		$options = get_fields( 'options' );

		if ( ! $options || ! array_key_exists( 'google_tracking', $options ) ) {
			return;
		}
		$google_tracking = $options[ 'google_tracking' ];

		if ( ! IS_PROD || empty( $options ) || empty( $google_tracking[ 'type' ] ) ||
			( $google_tracking[ 'type' ] === 'ga' && empty( $google_tracking[ 'ga_id' ] ) ) ||
			( $google_tracking[ 'type' ] === 'gtm' && empty( $google_tracking[ 'gtm_id' ] ) )
		) {
			return;
		}
		$this->google_tracking = $google_tracking;

		add_filter( 'timber/context', array( $this, 'setup_context' ) );

		if ( $google_tracking[ 'type' ] === 'ga' ) {
			add_filter( 'wp_head', array( $this, 'insert_google_analytics' ) );
		} elseif ( $google_tracking[ 'type' ] === 'gtm' ) {
			add_filter( 'wp_head', array( $this, 'insert_google_tag_manager' ) );
			add_action( 'wp_body_open', array( $this, 'insert_google_tag_manager_no_scripts' ) );
		}
	}

	/**
	 * Add variables to Timber Context
	 *
	 * @param array $context Timber context
	 * @return array
	 */
	public function setup_context( array $context ): array {
		$context[ 'google_tracking' ] = $this->google_tracking;

		return $context;
	}

	/**
	 * Insert the Google Analytics tracker in the DOM
	 *
	 * @return void
	 */
	public function insert_google_analytics() {
		Timber::render( 'google-analytics.twig', Timber::context() );
	}

	/**
	 * Insert the Google Tag Manager tracker in the DOM
	 *
	 * @return void
	 */
	public function insert_google_tag_manager() {
		Timber::render( 'google-tag-manager.twig', Timber::context() );
	}

	/**
	 * Insert the Google Tag Manager No Script in the DOM
	 *
	 * @return void
	 */
	public function insert_google_tag_manager_no_scripts() {
		Timber::render( 'google-tag-manager-no-script.twig', Timber::context() );
	}
}

new Google_Tracking();
