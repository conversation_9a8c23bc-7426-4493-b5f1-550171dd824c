import JsCookies from 'js-cookie';

import AbstractView from 'abstracts/AbstractView';
import Config from 'configs/Config';

export default new class LanguagesPopIn extends AbstractView {
	/**
	 * @type {HTMLElement} $lgPopIn
	 */
	#$lgPopIn;

	/**
	 * @type {NodeList} $btn
	 */
	#$$btn;

	init() {
		this.initDOM();

		if ( ! this.#$lgPopIn ) {
			return;
		}

		this.initEl();
		this.initGSAP();
		this.bindEvents();
		this.resize();
	}

	initDOM() {
		this.#$lgPopIn = document.getElementById( 'lg_pop_in' );
		if ( this.#$lgPopIn ) {
			this.#$$btn = this.#$lgPopIn.querySelectorAll( '.lg_pop_in--btn' );
		}
	}

	initEl() {
		this.#checkLanguages();
	}

	bindEvents() {
		this.eh.clickBtn = this.#setCookies.bind( this );
		this.#$$btn.forEach( ( $el ) => $el.addEventListener( 'click', this.eh.clickBtn ) );
	}

	unbindEvents() {
		this.#$$btn.forEach( ( $el ) => $el.removeEventListener( 'click', this.eh.clickBtn ) );
	}

	#checkLanguages() {
		const navLg = navigator.language.split( '-' )[ 0 ].split( '-' )[ 0 ];

		if ( Config.CURRENT_LANG === navLg || ( Config.CURRENT_LANG !== navLg && Config.CURRENT_LANG === 'en' && navLg !== 'fr' ) ) {
			return;
		}

		const lgIsSet = JsCookies.get( 'GRAHOU_lg-is_set' );

		if ( lgIsSet === undefined ) {
			this.#$lgPopIn.style.display = 'block';
		}
	}

	#setCookies( e ) {
		e.preventDefault();

		this.#hide();
		if ( e.currentTarget.tagName === 'A' && e.currentTarget.href !== undefined ) {
			window.location.href = e.currentTarget.href;
		} else if ( e.currentTarget.tagName !== 'A' ) {
			JsCookies.set( 'GRAHOU_lg-is_set', true, { expires: 30 } );
		}
	}

	#hide() {
		this.gsap.hide = gsap.timeline( {
			onComplete: this.#onHideComplete.bind( this ),
		} )
			.to( this.#$lgPopIn, { duration: 0.3, y: 20, opacity: 0, ease: 'power2.out' }, 0 );
	}

	#onHideComplete() {
		this.#$lgPopIn.style.display = 'none';
		this.destroy();
	}
};
