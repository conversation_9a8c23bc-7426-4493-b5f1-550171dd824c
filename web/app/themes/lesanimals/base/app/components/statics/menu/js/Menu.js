import Panel from 'components/partials/panel/js/Panel';
import Main from 'controllers/Main';

export default class Menu extends Panel {
	/**
	 * @type {Cash} $menu
	 */
	#$menu;

	/**
	 * @type {Cash} $submenuLinks
	 */
	#$submenuLinks;

	/**
	 * @type {Cash} $submenus
	 */
	#$submenus;

	/**
	 * @type {Cash} $currentSubmenu
	 */
	#$currentSubmenu;

	/**
	 * @type {Cash} $hideSubmenuLink
	 */
	#$hideSubmenuLink;

	/**
	 * @param {Cash} $wrapper
	 */
	constructor( $wrapper ) {
		super( $wrapper );

		this.init();
	}

	initDOM() {
		super.initDOM();

		this.#$menu = this.$wrapper.find( '.menu__items' );
		this.#$submenuLinks = this.$wrapper.find( '.menu__link[data-submenu-id]' );
		this.#$submenus = this.$wrapper.find( '.submenu' );
		this.#$currentSubmenu = false;
		this.#$hideSubmenuLink = $( '[data-submenu-close]' );
	}

	initEl() {
		super.initEl();

		this.$wrapper.trigger( 'forceLoadImages' );
		this.#$submenus.addClass( 'hidden' );
		this.#$submenus.each( ( index, item ) => {
			item.inert = true;
		} );
		this.#$hideSubmenuLink.each( ( index, item ) => {
			item.inert = true;
		} );
	}

	bindEvents() {
		super.bindEvents();

		this.eh.menuItemClick = this.menuItemClick.bind( this );
		this.#$submenuLinks.on( 'click', this.eh.menuItemClick );

		this.eh.submenuCloseClick = this.#hideSubmenu.bind( this );
		this.#$hideSubmenuLink.on( 'click', this.eh.submenuCloseClick );
	}

	menuItemClick( event ) {
		const $link = $( event.currentTarget ),
			$submenuTarget = this.#$submenus.filter( '[data-submenu-id="' + $link.attr( 'data-submenu-id' ) + '"]' );

		if ( ! $submenuTarget.hasClass( 'hidden' ) ) {
			this.#hideSubmenu();
		} else {
			this.#showSubmenu( $submenuTarget );
		}
	}

	/**
	 * Hide the current submenu if any
	 */
	#hideSubmenu() {
		if ( this.#$currentSubmenu && this.#$currentSubmenu.length ) {
			this.#$currentSubmenu.addClass( 'hidden' );
			this.#$currentSubmenu[ 0 ].inert = true;
			this.#$menu[ 0 ].inert = false;
			Main.$html.removeClass( 'submenu--open' );
			this.#$hideSubmenuLink.each( ( index, item ) => {
				item.inert = true;
			} );
		}
	}

	/**
	 * Shows a submenu
	 *
	 * @param {Cash} $submenu
	 */
	#showSubmenu( $submenu ) {
		this.#hideSubmenu();
		$submenu.removeClass( 'hidden' );
		$submenu[ 0 ].inert = false;
		this.#$menu[ 0 ].inert = true;
		Main.$html.addClass( 'submenu--open' );
		this.#$currentSubmenu = $submenu;
		this.#$hideSubmenuLink.each( ( index, item ) => {
			item.inert = false;
		} );
	}

	afterClose() {
		super.afterClose();

		this.#hideSubmenu();
	}
}
