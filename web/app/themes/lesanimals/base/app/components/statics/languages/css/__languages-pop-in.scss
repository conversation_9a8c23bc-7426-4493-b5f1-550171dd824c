// ************************************
// -------- Languages - Pop in --------
// ************************************


#lg_pop_in {
	display: none;
	position: fixed;
	top: auto;
	left: var(--grid_column_gap);
	bottom: var(--grid_column_gap);
	width: 340px;
	max-width: calc(100% - var(--grid_column_gap) * 2);
	padding: 20px;
	border: 1px solid $color_black;
	background-color: $color_white;
	overflow: hidden;
	z-index: 93;
}

.lg_pop_in--txt {
	flex: 1;
	color: $color_highlight;
	font: var(--font_xs);
}

.lg_pop_in--btns {
	display: flex;
	gap: 10px;
	justify-content: flex-end;
	margin-top: 20px;
}

.lg_pop_in--btn {
	padding: 4px 13px 5px 13px;
	color: $color_highlight;
	font: var(--font_xs);
	border-radius: 15px;
	border: 2px solid $color_highlight;
	cursor: pointer;

	@include hover {
		color: $color_white;
		background-color: $color_highlight;
	}
}

.lg_pop_in--btn__yes {
}

.lg_pop_in--btn__no {
}

