# Cookies

To make a javascript file dependant of the cookie acceptance, you need to
add `type="text/plain" class="cookies_to_accept"` to the `<script>` tag

## Example

### From

```html
<script async src="https://www.googletagmanager.com/gtag/js?id=***"></script>
<script>
	window.dataLayer = window.dataLayer || [];
	function gtag(){dataLayer.push(arguments);}
	gtag('js', new Date());
	gtag('config', '***');
</script>
```

### To

```html
<script type="text/plain" class="cookies_to_accept" async src="https://www.googletagmanager.com/gtag/js?id=***"></script>
<script type="text/plain" class="cookies_to_accept">
	window.dataLayer = window.dataLayer || [];
	function gtag(){dataLayer.push(arguments);}
	gtag('js', new Date());
	gtag('config', '***');
</script>
```

## Automatize example for inspiration

```php
add_filter( 'script_loader_tag', function ( $tag, $handle ) {
	if ( !is_admin() && ($handle === 'google-tag-manager') ) {
		$tag = str_replace( ' type="text/javascript"', ' type="text/plain" class="cookies_to_accept"', $tag );
	}

	return $tag;
}, 10, 2 );
```
