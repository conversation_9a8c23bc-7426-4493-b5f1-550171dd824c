import JsCookies from 'js-cookie';
import AbstractView from 'abstracts/AbstractView';
import Main from 'controllers/Main';

export default new class Cookies extends AbstractView {
	/**
	 * @type {HTMLElement} $seikooc
	 */
	#$seikooc;

	/**
	 * @type {HTMLElement} $btnAccept
	 */
	#$btnAccept;

	/**
	 * @type {HTMLElement} $btnRefuse
	 */
	#$btnRefuse;

	init() {
		super.init();

		this.#checkCookiesAccepted();
	}

	initDOM() {
		this.#$seikooc = document.getElementById( 'seikooc' );
		this.#$btnAccept = this.#$seikooc.querySelector( '.seikooc--btn__accept' );
		this.#$btnRefuse = this.#$seikooc.querySelector( '.seikooc--btn__refuse' );
	}

	bindEvents() {
		this.eh.clickBtnAccept = this.#accept.bind( this );
		this.#$btnAccept.addEventListener( 'click', this.eh.clickBtnAccept );
		this.eh.clickBtnRefuse = this.#refuse.bind( this );
		this.#$btnRefuse.addEventListener( 'click', this.eh.clickBtnRefuse );
	}

	unbindEvents() {
		this.#$btnAccept.removeEventListener( 'click', this.eh.clickBtnAccept );
		this.#$btnRefuse.removeEventListener( 'click', this.eh.clickBtnRefuse );
	}

	#checkCookiesAccepted() {
		const cookiesAccepted = JsCookies.get( 'GRAHOU_cookies-has_accepted' );

		if ( cookiesAccepted === undefined ) {
			this.#$seikooc.style.display = 'block';
		} else if ( cookiesAccepted === 'true' ) {
			this.#activateScripts();
			this.destroy();
		} else if ( cookiesAccepted === 'false' ) {
			this.destroy();
		}
	}

	#refuse() {
		this.#setCookiesAcceptance( 'false' );
		this.#hide();
	}

	#accept() {
		this.#setCookiesAcceptance( 'true' );
		this.#activateScripts();
		this.#hide();
	}

	#setCookiesAcceptance( value ) {
		JsCookies.set( 'GRAHOU_cookies-has_accepted', value, { expires: 30 } );
	}

	#activateScripts() {
		const $scripts = Main.$html.querySelectorAll( 'script.cookies_to_accept' );

		$scripts.forEach( ( $el ) => {
			if ( $el.src ) {
				const src = $el.src;
				$el.src = '';
				$el.type = 'text/javascript';
				$el.src = src;
			} else {
				const inner = $el.innerText.replace( /(\r\n|\n|\r|\t)/gm, '' ); // remove all tabs & line breaks
				$el.innerText = '';
				$el.type = 'text/javascript';
				$el.innerText = inner;
			}
		} );
	}

	#hide() {
		this.gsap.hide = gsap.timeline( {
			onComplete: this.#onHideComplete.bind( this ),
		} )
			.to( this.#$seikooc, { duration: 0.3, y: 20, opacity: 0, ease: 'power2.out' }, 0 );
	}

	#onHideComplete() {
		this.#$seikooc.style.display = 'none';
		this.destroy();
	}
};

