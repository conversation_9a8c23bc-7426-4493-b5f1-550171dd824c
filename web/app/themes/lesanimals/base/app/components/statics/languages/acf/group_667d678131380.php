<?php 

if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
	'key' => 'group_667d678131380',
	'title' => 'Options - Langues',
	'fields' => array(
		array(
			'key' => 'field_667d67aa865bb',
			'label' => '',
			'name' => 'lg',
			'aria-label' => '',
			'type' => 'group',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'layout' => 'block',
			'acfe_seamless_style' => 0,
			'acfe_group_modal' => 0,
			'sub_fields' => array(
				array(
					'key' => 'field_667d682cdf3bc',
					'label' => 'Texte',
					'name' => 'txt',
					'aria-label' => '',
					'type' => 'textarea',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'default_value' => 'Would you like to visit the page in&nbsp;English?
ou
Voulez-vous visiter la page en&nbsp;français&nbsp;?',
					'acfe_textarea_code' => 0,
					'maxlength' => '',
					'rows' => 3,
					'placeholder' => '',
					'new_lines' => 'br',
				),
				array(
					'key' => 'field_667d685bdf3bd',
					'label' => 'Bonton oui',
					'name' => 'btn_yes',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '25',
						'class' => '',
						'id' => '',
					),
					'default_value' => 'Oui / Yes',
					'maxlength' => '',
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
				),
				array(
					'key' => 'field_667d687fdf3be',
					'label' => 'Bonton non',
					'name' => 'btn_no',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '25',
						'class' => '',
						'id' => '',
					),
					'default_value' => 'Non / No',
					'maxlength' => '',
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
				),
			),
			'acfe_group_modal_close' => 0,
			'acfe_group_modal_button' => '',
			'acfe_group_modal_size' => 'large',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'options_page',
				'operator' => '==',
				'value' => 'acf-options-options',
			),
		),
	),
	'menu_order' => 122,
	'position' => 'normal',
	'style' => 'seamless',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'acfe_autosync' => array(
		0 => 'php',
	),
	'acfe_form' => 0,
	'acfe_display_title' => 'Langues',
	'acfe_meta' => '',
	'acfe_note' => '',
	'acfe_categories' => array(
		'options' => 'Options',
	),
	'modified' => 1720685332,
));

endif;