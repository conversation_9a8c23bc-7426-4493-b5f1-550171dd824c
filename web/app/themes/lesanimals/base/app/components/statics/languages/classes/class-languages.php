<?php
/**
 * Languages
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Statics\Languages;

use Timber;

class Languages {

	/**
	 * Class construct
	 */
	public function __construct() {
		add_action( 'wp_footer', array( $this, 'insert_cookies_popup' ) );
	}

	/**
	 * Insert the cookie popup in the DOM
	 *
	 * @return void
	 */
	public function insert_cookies_popup() {
		Timber::render( 'languages-pop-in.twig', Timber::context() );
	}
}

new Languages();
