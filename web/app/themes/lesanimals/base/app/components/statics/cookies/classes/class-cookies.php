<?php
/**
 * Cookies
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Statics\Cookies;

use Timber;

class Cookies {

	/**
	 * Class construct
	 */
	public function __construct() {
		add_action( 'wp_footer', array( $this, 'insert_cookies_popup' ) );
	}

	/**
	 * Insert the cookie popup in the DOM
	 *
	 * @return void
	 */
	public function insert_cookies_popup() {
		Timber::render( 'cookies.twig', Timber::context() );
	}
}

new Cookies();
