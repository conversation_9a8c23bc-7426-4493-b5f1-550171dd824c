<?php
/**
 * Context for News Single
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\News;

use Timber;
use Timber\Post;

class Context_News_Single {

	private null|array $news_opt = null;

	/**
	 * Construct
	 */
	public function __construct() {
		add_filter( 'timber/context', array( $this, 'setup_context' ) );
	}

	/**
	 * Add variables to Timber Context
	 *
	 * @param array $context context
	 * @return array
	 */
	public function setup_context( array $context ): array {
		if ( ! is_singular( 'post' ) ) {
			return $context;
		}

		$post = Timber::get_post();
		$context[ 'post' ] = $post;

		$this->news_opt = get_fields( 'options' )[ 'news' ];

		$context[ 'news_push' ] = $this->get_news_push_context( $post );

		return $context;
	}

	/**
	 * Get the news push
	 *
	 * @param Post $post
	 * @return array
	 */
	private function get_news_push_context( Post $post ): array {
		$news_push = array();

		$news_push[ 'title' ] = $this->news_opt[ 'push' ][ 'title' ];
		$news_push[ 'btn' ] = array(
			'txt'  => $this->news_opt[ 'push' ][ 'btn_txt' ],
			'href' => get_permalink( $this->news_opt[ 'page_id' ] ),
		);

		$news = Timber::get_posts( array(
			'post_type'      => 'post',
			'posts_per_page' => 3,
			'cat'            => $this->get_categories(),
			'order'          => 'desc',
			'post__not_in'   => array( $post->id ),
		) );
		$news_push[ 'items' ] = $this->get_items( $news );

		return $news_push;
	}

	/**
	 * Get the categories of the current post
	 *
	 * @return string
	 */
	private function get_categories(): string {
		$categories = '';

		foreach ( get_the_category() as $key => $cat ) {
			$categories .= ( $key === 0 ? '' : ', ' ) . $cat->term_id;
		}

		return $categories;
	}

	/**
	 * Get items
	 *
	 * @param array $list_news List of news
	 *
	 * @return array
	 */
	private function get_items( array $list_news ): array {
		$items = array();

		$context = Timber::context();
		$context[ 'img_ratio' ] = 370 / 300;
		$context[ 'img_width' ] = array( 390, 520, 780, 920 );
		$context[ 'sizes' ] = '(max-width: 400px) 76vw, (max-width: 492px) 64vw, (max-width: 640px) 322px, (max-width: 960px) 45vw, (max-width: 1999px) 29vw, (min-width: 2000px) 520px';
		$context[ 'class' ] = 'slider--item';
		$context[ 'is_preview' ] = is_admin();

		foreach ( $list_news as $news ) {
			$context[ 'news_id' ] = $news->id;
			$news_item = Timber::compile( 'news-card.twig', $context );
			$items[] = $news_item;
		}

		return $items;
	}
}

new Context_News_Single();
