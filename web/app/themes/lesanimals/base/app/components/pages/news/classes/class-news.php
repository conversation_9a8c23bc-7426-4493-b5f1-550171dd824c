<?php
/**
 * Customize post type for News
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\News;

class News {
	/**
	 * Class construct
	 */
	public function __construct() {
		add_filter( 'lesanimals_allow_news', array( $this, 'allow_news' ) );
		add_filter( 'lesanimals_controller_directory', array( $this, 'rewrite_news_controller_directory' ), 10, 3 );

		add_action( 'init', array( $this, 'rename_default_post' ) );
	}

	/**
	 * Allow news
	 *
	 * @return bool
	 */
	public function allow_news(): bool {
		return true;
	}

	/**
	 * Rewrite news controller directory
	 *
	 * @param string $path     path
	 * @param string $dir_name directory name
	 * @param string $template template
	 * @return string
	 */
	public function rewrite_news_controller_directory( string $path, string $dir_name, string $template ): string {
		if ( $template === 'home.php' || $template === 'category.php' || $template === 'single-post.php' ) {
			$path = 'app/components/pages/news/controllers/' . $template;
		}

		return $path;
	}

	/**
	 * Rename default post
	 *
	 * @return void
	 */
	public function rename_default_post(): void {
		$get_post_type = get_post_type_object( 'post' );
		$labels = $get_post_type->labels;

		$labels->name = _x( 'Actualités', 'Post Type General Name', 'lesanimals' );
		$labels->singular_name = _x( 'Actualité', 'Post Type Singular Name', 'lesanimals' );
		$labels->menu_name = __( 'Actualités', 'lesanimals' );
		$labels->name_admin_bar = __( 'Actualité', 'lesanimals' );
		$labels->archives = __( 'Archives des actualités', 'lesanimals' );
		$labels->attributes = __( 'Attributes', 'lesanimals' );
		$labels->parent_item_colon = __( 'Parent Item:', 'lesanimals' );
		$labels->all_items = __( 'Toutes les actualités', 'lesanimals' );
		$labels->add_new_item = __( 'Ajouter une actualité', 'lesanimals' );
		$labels->add_new = __( 'Ajouter une actualité', 'lesanimals' );
		$labels->new_item = __( 'Nouvelle actualité', 'lesanimals' );
		$labels->edit_item = __( 'Éditer une actualité', 'lesanimals' );
		$labels->update_item = __( 'Mettre à jour', 'lesanimals' );
		$labels->view_item = __( 'Voir l\'actualité', 'lesanimals' );
		$labels->view_items = __( 'Voir les actualités', 'lesanimals' );
		$labels->search_items = __( 'Rechercher', 'lesanimals' );
		$labels->not_found = __( 'Aucun résultat', 'lesanimals' );
		$labels->not_found_in_trash = __( 'Rien dans la corbeille', 'lesanimals' );
		$labels->featured_image = __( 'Image principale', 'lesanimals' );
		$labels->set_featured_image = __( 'Choisir l\'image principale', 'lesanimals' );
		$labels->remove_featured_image = __( 'Supprimer l\'image', 'lesanimals' );
		$labels->use_featured_image = __( 'Utiliser comme image principale', 'lesanimals' );
		$labels->insert_into_item = __( 'Ajouter à l\'actualité', 'lesanimals' );
		$labels->uploaded_to_this_item = __( 'Ajouté sur cette actualité', 'lesanimals' );
		$labels->items_list = __( 'Items list', 'lesanimals' );
		$labels->items_list_navigation = __( 'Items list navigation', 'lesanimals' );
		$labels->filter_items_list = __( 'Filter items list', 'lesanimals' );

		$get_post_type->menu_icon = 'dashicons-media-spreadsheet';
	}
}

new News();
