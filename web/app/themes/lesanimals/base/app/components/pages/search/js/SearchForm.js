import Panel from 'components/partials/panel/js/Panel';

export default class SearchForm extends Panel {
	constructor( $wrapper ) {
		super( $wrapper );
		this.init();
	}

	initDOM() {
		super.initDOM();
		this.$form = this.$wrapper.find( '.search_form__form' );
		this.$input = this.$wrapper.find( '.search_form__input' );
	}

	bindEvents() {
		super.bindEvents();
	}

	afterOpen() {
		this.$input[ 0 ].focus();
	}
}

