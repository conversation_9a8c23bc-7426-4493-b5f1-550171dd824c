class EmailSignatureInc {
	/**
	 * @type {HTMLElement} $btnWrap
	 */
	#$btnWrap;

	/**
	 * @type {HTMLElement} $btnCopy
	 */
	#$btn;

	/**
	 * @type {setTimeout} stoCopied
	 */
	#stoCopied;

	constructor() {
		this.#init();
	}

	#init() {
		this.#initDOM();
		this.#bindEvents();
	}

	#initDOM() {
		this.#$btnWrap = document.querySelector( '.email_signature_single--btn_wrapper' );
		this.#$btn = this.#$btnWrap.querySelector( '.email_signature_single--btn' );
	}

	#bindEvents() {
		const ehClickBtnCopy = this.#copyHTML.bind( this );
		this.#$btn.addEventListener( 'click', ehClickBtnCopy );
	}

	#copyHTML() {
		const selection = window.getSelection(); // eslint-disable-line
		const range = document.createRange();

		range.selectNodeContents( document );
		selection.removeAllRanges();
		document.body.classList.add( 'copy' );
		selection.addRange( range );

		try {
			document.execCommand( 'copy' );
		} catch ( err ) {
			console.error( 'Unable to copy text: ', err ); // eslint-disable-line
		} finally {
			if ( selection ) {
				selection.removeAllRanges();
			}
			document.body.classList.remove( 'copy' );

			this.#$btnWrap.classList.add( 'copied' );
			clearTimeout( this.stoCopied );
			this.stoCopied = setTimeout( () => {
				this.#$btnWrap.classList.remove( 'copied' );
			}, 3000 );
		}
	}
}

new EmailSignatureInc();
