<?php
/**
 * Helpers for search
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\Search;

use LesAnimals\Core\Languages\Translation;

trait Aid {

	/**
	 * Get the terms to show in search
	 *
	 * @return array
	 */
	public static function get_default_search_terms(): array {

		$terms = array();

		if ( have_rows( 'search_terms', 'search_option' . Translation::get_lang_slug( '_' ) ) ) {
			while ( have_rows( 'search_terms', 'search_option' . Translation::get_lang_slug( '_' ) ) ) {
				the_row();
				$terms[] = get_sub_field( 'term' );
			}
		}

		return $terms;
	}
}
