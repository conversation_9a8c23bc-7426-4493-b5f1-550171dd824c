{% extends 'base.twig' %}

{% block content %}
	<section class="page_container" id="search" data-view-id="search" data-id="{{ post.ID }}">
		<h1 class="main_title">{{ __('Recherche', 'lesanimals') }}</h1>

		<form class="search__search_form cw--s" action="{{ SITE_URL }}" method="get">
			<div class="search_form__input_wrapper">
				<label class="sr_only" for="search_form_input">{{ __("Rechercher", 'lesanimals') }}</label>
				<input class="search_form__input" value="{{ search.query }}" type="search" id="search_form_input" name="s" placeholder="{{ _x("Votre recherche...", 'lesanimals', 'Search placeholder') }}">
				<button class="search_form__button" type="submit">
					<span class="loader"></span>
					{{ __("Recherche", 'lesanimals') }}
					{{ svg('icon-search') }}
				</button>
			</div>
		</form>

		<div class="page_content">

			<div class="search__title">
				{% if posts|length > 0 %}
					{{ _n("%d résultat pour %s", "%d résultats pour %s", posts|length, 'lesanimals')|format(posts|length, '<span class="search__title_query">' ~ search.query ~ '</span>') }}
				{% endif %}
			</div>

			<div class="search__grid_wrapper grid_inner">

				{% for search_post in posts %}
					{% include 'search/card.twig' %}
				{% endfor %}
			</div>

		</div>
	</section>
{% endblock %}
