<?php 

if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
	'key' => 'group_6672d7acdaaad',
	'title' => 'Signature email - Informations',
	'fields' => array(
		array(
			'key' => 'field_6672d7ad21c2c',
			'label' => 'Prénom',
			'name' => 'firstname',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'required_message' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_6672d86d7bbd8',
			'label' => 'Nom',
			'name' => 'lastname',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_6672d87c7bbd9',
			'label' => 'Poste',
			'name' => 'jobtitle',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '34',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_6672d8877bbda',
			'label' => 'Numéro de téléphone',
			'name' => 'phone',
			'aria-label' => '',
			'type' => 'acfe_phone_number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'countries' => array(
				0 => 'fr',
			),
			'preferred_countries' => array(
				0 => 'fr',
			),
			'default_country' => 'fr',
			'geolocation' => 0,
			'native' => 0,
			'national' => 1,
			'dropdown' => 0,
			'dial_code' => 0,
			'default_value' => '',
			'placeholder' => '',
			'return_format' => 'array',
			'geolocation_token' => '',
		),
		array(
			'key' => 'field_6672e207ab38c',
			'label' => 'Bureau',
			'name' => 'office_id',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'email_signature_office',
			'add_term' => 0,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'id',
			'field_type' => 'select',
			'max' => '',
			'allow_null' => 0,
			'acfe_bidirectional' => array(
				'acfe_bidirectional_enabled' => '0',
			),
			'required_message' => '',
			'bidirectional' => 0,
			'multiple' => 0,
			'bidirectional_target' => array(
			),
			'min' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'email_signature',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'featured_image',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'acfe_autosync' => array(
		0 => 'php',
	),
	'acfe_form' => 0,
	'acfe_display_title' => 'Informations',
	'acfe_meta' => '',
	'acfe_note' => '',
	'modified' => 1720433730,
));

endif;