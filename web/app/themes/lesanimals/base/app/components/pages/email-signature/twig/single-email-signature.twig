<!doctype html>
<html>

<head>
	<meta charset="UTF-8"/>
	<title>{{ post.post_title }} | Signature</title>
	<meta charset="UTF-8"/>
	<link rel="stylesheet" href="{{ fn( 'get_stylesheet_directory_uri' ) }}/app/assets/dist/css/styles.css">
</head>

<body id="email_signature_single" style="padding: 0; margin: 0; line-height: 1;">

{% set office = get_term( post.meta( 'office_id' ) ) %}

<table width="90%" border="0" cellspacing="0" cellpadding="0" style="width: 90%; max-width: 90%; padding: 0px; color: #000; font: 400 14px/16px Arial, sans-serif; border-spacing: 0; border-collapse: collapse; background-color: #fff;">
	<tr>
		<td height="48"></td>
	</tr>
	{% if office.meta( 'logo_id' ) %}
		{% set logo = get_image( office.meta( 'logo_id' ) ) %}
		{% set LOGO_MAX_W = 200 %}
		{% set LOGO_MAX_H = 150 %}
		{% if LOGO_MAX_W / LOGO_MAX_H < logo.width / logo.height %}
			{% set logo_w = LOGO_MAX_W %}
			{% set logo_h = LOGO_MAX_W * logo.height / logo.width %}
		{% else %}
			{% set logo_w = LOGO_MAX_H * logo.width / logo.height %}
			{% set logo_h = LOGO_MAX_H %}
		{% endif %}
		{% set logo = get_image( office.meta( 'logo_id' ) ) %}
		<tr>
			<td style="padding-bottom: 24px">
				<img src="{{ logo.src }}" width="{{ logo_w }}" height="{{ logo_h }}" alt="" style="display: block; width: {{ logo_w }}px;"/>
			</td>
		</tr>
	{% endif %}
	<tr>
		<td style="color: #2c2ce7; font: 700 18px/21px Arial, sans-serif;">
			{{ post.meta( 'firstname' ) }}
			{% if post.meta( 'lastname' ) %}
				{{ post.meta( 'lastname' ) }}
			{% endif %}
		</td>
	</tr>
	{% if post.meta( 'jobtitle' ) %}
		<tr>
			<td style="padding-top: 6px; font: 700 16px/18px Arial, sans-serif;">
				{{ post.meta( 'jobtitle' ) }}
			</td>
		</tr>
	{% endif %}
	{% if post.meta( 'phone' ) %}
		<tr>
			<td style="padding-top: 12px;">
				<a href="tel:{{ post.meta( 'phone' ).number }}" style="color: #000;">{{ post.meta( 'phone' ).international }}</a>
			</td>
		</tr>
	{% endif %}
	{% if office.meta( 'address' ) or office.meta( 'zip_code' ) or office.meta( 'city' ) %}
		<tr>
			{% set pt_address = post.meta( 'phone' ) ? 4 : 12 %}
			<td style="padding-top: {{ pt_address }}px;">
				{{ office.meta( 'address' ) }}
				{% if office.meta( 'address' ) and ( office.meta( 'zip_code' ) or office.meta( 'city' ) ) %}
					-
				{% endif %}
				{{ office.meta( 'zip_code' ) }} {{ office.meta( 'city' ) }}
			</td>
		</tr>
	{% endif %}
	{% if office.meta( 'url' ) %}
		<tr>
			{% set pt_address = post.meta( 'phone' ) or office.meta( 'address' ) or office.meta( 'zip_code' ) or office.meta( 'city' ) ? 4 : 12 %}
			<td style="padding-top: {{ pt_address }}px;">
				<a href="{{ office.meta( 'url' ) }}" target="_blank" style="color: #2c2ce7;">{{ office.meta( 'url' ) | replace( { 'https://': '', 'http://': '' } ) }}</a>
			</td>
		</tr>
	{% endif %}
	{% if office.meta( 'links_social' ) %}
		<tr>
			<td style="padding-top: 12px; font-weight: 700;">
				{% for item in office.meta( 'links_social' ) %}
					<a href="{{ item.link.url }}" target="_blank" style="color: #000;">{{ item.link.title }}</a>
					{% if not loop.last %}—{% endif %}
				{% endfor %}
			</td>
		</tr>
	{% endif %}
	<tr>
		<td height="48"></td>
	</tr>
</table>

<div class="email_signature_single--btn_wrapper">
	<button type="button" class="email_signature_single--btn">
		<span class="email_signature_single--btn-icon">{{ source( 'app/components/pages/email-signature/img/copy.svg' ) }}</span>
		<span class="email_signature_single--btn-txt">{{ i18n.email_signature.copy_signature }}</span>
	</button>
	<div class="email_signature_single--copied">{{ i18n.email_signature.signature_copied }}</div>
</div>

<script async defer src="{{ fn( 'get_stylesheet_directory_uri' ) }}/app/components/pages/email-signature/js/EmailSignature.inc.js" id="theme-scripts-js"></script>


</body>
</html>
