<?php 

if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
	'key' => 'group_6447eb2c78192',
	'title' => 'Page - Actualité - Détail - Remontée d\'actualités',
	'fields' => array(
		array(
			'key' => 'field_6447ebc667551',
			'label' => '',
			'name' => 'news_push',
			'aria-label' => '',
			'type' => 'group',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'layout' => 'block',
			'acfe_seamless_style' => 0,
			'acfe_group_modal' => 0,
			'acfe_group_modal_close' => 0,
			'acfe_group_modal_button' => '',
			'acfe_group_modal_size' => 'large',
			'sub_fields' => array(
				array(
					'key' => 'field_6447eb2c7be8d',
					'label' => 'Remontée d\'actualités',
					'name' => '',
					'aria-label' => '',
					'type' => 'message',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'message' => '<i>Éditer les contenus du module dans <a href="admin.php?page=acf-options-options">Options > Actualités > Remontée d\'actualités</a></i>',
					'new_lines' => 'wpautop',
					'esc_html' => 0,
				),
			),
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'post',
			),
		),
	),
	'menu_order' => 30,
	'position' => 'normal',
	'style' => 'seamless',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'acfe_autosync' => array(
		0 => 'php',
		1 => 'json',
	),
	'acfe_form' => 0,
	'acfe_display_title' => 'Remontée d\'actualités',
	'acfe_meta' => '',
	'acfe_note' => '',
	'acfe_categories' => array(
		'page' => 'Page',
	),
	'modified' => 1691492544,
));

endif;
