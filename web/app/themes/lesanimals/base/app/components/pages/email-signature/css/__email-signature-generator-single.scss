// ********************************************
// -------- Email Signature - Single ----------
// ********************************************


#email_signature_single {

	#svg {
		display: none;
	}

	a {
		text-decoration: underline;
	}

	&:not(.copy) {
		padding-top: 40px !important;
		background-color: #f5f5f5;

		table {
			max-width: 640px !important;
			margin: auto;
			box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

			td {
				padding-left: 80px;
				padding-right: 80px;
			}
		}
	}

	&.copy {
		background-color: #fff;

		.email_signature_single--btn_wrapper {
			display: none;
		}
	}
}

.email_signature_single--btn_wrapper {
	position: relative;
	width: fit-content;
	margin: 80px auto 0;

	&.copied {

		.email_signature_single--copied {
			opacity: 1;
		}
	}
}

.email_signature_single--btn {
	display: flex;
	align-items: center;
	width: fit-content;
	height: 48px;
	padding: 8px 29px;
	font: 700 15px/20px Arial, sans-serif;
	color: $color_highlight;
	border: 2px solid $color_highlight;
	border-radius: 24px;

	&:hover {
		color: $color_white;
		background-color: $color_highlight;

		.email_signature_single--btn-icon svg {
			fill: $color_white;
		}
	}
}

.email_signature_single--btn-icon {
	width: 20px;
	height: 20px;
	margin-top: -2px;

	svg {
		fill: $color_highlight;
		transition: all $ease_default;
	}
}

.email_signature_single--btn-txt {
	margin-left: 16px;
}

.email_signature_single--copied {
	position: absolute;
	left: 0;
	bottom: -24px;
	width: 100%;
	text-align: center;
	font: 700 12px/14px Arial, sans-serif;
	opacity: 0;
	transition: opacity $ease_default;
	pointer-events: none;
}

