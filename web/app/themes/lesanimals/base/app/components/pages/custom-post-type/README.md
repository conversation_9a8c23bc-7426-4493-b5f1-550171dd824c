# Register custom post type

### This folder is an example of how to register a custom post type with default files and folders.

- [Installation](#%EF%B8%8F-installation)
- [Renaming](#%EF%B8%8F-renaming)
- [Config](#%EF%B8%8F-config)

## ⚡️ Installation

1. Copy and past the folder `custom-post-type` in the folder `web/app/themes/lesanimals/app/components/pages` of your curent project.
2. Use the plugin PhpStorm [Regex Rename Files](https://plugins.jetbrains.com/plugin/17063-regex-rename-files) to rename files and folders `custom-post-type` to `example-name-cpt`. You need to use **singular name** and not **plural name**.
3. You also need to replace file name with capital letter for JS files. `CustomPostTypeArchive` to `ExampleNameCptArchive` and `CustomPostTypeSingle` to `ExampleNameCptSingle`.
4. You also need to replace file name for taxonomy `custom-post-type-taxonomy` to `example-name-cpt-example-name-taxo`.
5. Then, proceed to [renaming](#%EF%B8%8F-renaming) and [config](#%EF%B8%8F-config) steps.

## ✍️ Renaming

1. In PHP files, rename:
   1. `CustomPostType` to `ExampleNameCtp` (namespace, class name, comment)
   2. `custom_post_type` to `example_name_ctp` (post type key)
   3. `Custom_post_types` to `Example name ctps` (`class-register-custom-post-type.php`)
   4. `Custom_post_type` to `Example name ctp` (`class-register-custom-post-type.php`)
   5. `Custom_Post_Type` to `Example Name Ctp` (`class-register-custom-post-type.php`)
   6. `custom_post_types` to `example name ctps` (`class-register-custom-post-type.php`)
   7. `custom_post_type` to `example name ctp` (`class-register-custom-post-type.php`)
   8. `custom_post_type_taxo` to `example_name_ctp_example_name_taxo` (taxonomy key)
   9. `Custom_Post_Type_Taxonomy` to `Example Name Ctp Example Name Taxo` (`class-register-custom-post-type-taxonomy.php`)
   10. `Taxonomies` to `Example name taxos` (`class-register-custom-post-type-taxonomy.php`)
   11. `Taxonomy` to `Example name taxo` (`class-register-custom-post-type-taxonomy.php`)
   12. `taxonomies` to `example name taxos` (`class-register-custom-post-type-taxonomy.php`) 
   13. `taxonomy` to `example name taxo` (`class-register-custom-post-type-taxonomy.php`) 
   14. `taxonomy_slug` to `example_name_taxo` (`class-register-custom-post-type-taxonomy.php`)
2. In CSS files:
   1. `Custom_Post_Type` to `Example name ctp` (comment)
   2. `custom_post_type` to `example_name_ctp` (class name)
3. In JS files:
   1. `CustomPostType` to `ExampleNameCtp` (class name)
   2. `custom_post_type` to `example_name_ctp` (page id)
4. In Twig files:
   1. `custom_post_type` to `example_name_ctp` (page id)
5. Rename files:
   1. `class-register-custom-post-type.php` to `class-register-example-name-ctp.php` 
   2. `class-register-custom-post-type-taxonomy.php` to `class-register-example-name-ctp-example-name-taxo.php`
   3. `archive-custom-post-type.php` to `archive-example-name-ctp.php`
   4. `single-custom-post-type.php` to `single-example-name-ctp.php`
   5. `__custom-post-type-archive.scss` to `__example-name-ctp-archive.scss`
   6. `__custom-post-type-single.scss` to `__example-name-ctp-single.scss`
   7. `CustomPostTypeArchive.js` to `ExampleNameCtpArchive.js`
   8. `CustomPostTypeSingle.js` to `ExampleNameCtpSingle.js`
   9. `archive-custom-post-type.twig` to `archive-example-name-ctp.twig`
   10. `single-custom-post-type.twig` to `single-example-name-ctp.twig`

## ⚙️ Config

### `class-config.php`:

- `$post_type_key`: `{string}` custom post type id
- `$posts_per_page`: `{int}` number of posts displayed per page
- `$taxonomies_keys`: `{array}` list of taxonomy ids
- `$post_type_args`: `{array}` list of arguments to overwrite used for register post type
  - example:
    ```
    public array $post_type_args = array(
    	'menu_position' => 113,
    	'menu_icon'     => 'dashicons-buddicons-activity',
    );
    ```
- `$post_type_params`: `{array}` list of `$params` to overwrite used for post type
  - list of parameters:
    - `has_archive`: `{boolean}` does the post type have an archive?
    - `has_single`: `{boolean}` does the post type have a single?
    - `has_flexibles`: `{boolean}` does the post type have flexibles?
    - `flexible_title`: `{boolean|string}` overwrites the flexible title, uses the generic `FLEXIBLE_TITLE_DEFAULT` (can be set as wanted) if `true` or the specific string if sets as such
    - `has_menu_classes`: `{boolean}`  does the post type have flexibles?
    - `hide_seopress`: `{boolean}` is SEOPress boxes hidden?
  - example:
    ```
    public array $post_type_params = array(
    	'has_flexible'   => false,
    	'hide_seopress'  => false,
    	'flexible_title' => 'Custom title',
    );
    ```
- `$taxonomies_args`: `{array}` list of `$args` to overwrite used for taxonomies, array of array with taxonomy key as array key
  - example:
    ```
    public array $taxonomies_args = array(
    	'custom_post_type_taxo' => array(
    		'public'               => false,
    		'publicly_queryable'   => false,
    		'show_in_nav_menus'    => false,
    		'acfe_single_template' => false,
    	),
    );
    ```
- `$taxonomies_params`: `{array}` list of `$params` to overwrite used for taxonomies, array of array with taxonomy key as array key
  - list of parameters:
    - `has_rewrite`: `{boolean}` does the taxonomy slug is rewrite?
    - `hide_seopress`: `{boolean}` is SEOPress boxes hidden?
  - example:
    ```
    public array $taxonomies_params = array(
    	'custom_post_type_taxo' => array(
    		'has_rewrite'   => true,
    		'hide_seopress' => false,
    	),
    );
    ```
- `$template_path`: `{string}` path to the template, can be usefull to make condition on template path
- `get_post_type_slug()`: `{function}` if `has_archive` is set to `false`, you must define the post type slug with a translation string
  - example:
    ```
    /**
     * Get post type slug
     *
     * @return string
     */
    public function get_post_type_slug(): string {
    	return _x( 'realisation', 'Realisation slug for permalinks', 'lesanimals' );
    }
    ```

### `class-register-custom-post-type.php`:

- `get_labels()`: `{function}` use it to define the labels of the custom post type

### `class-register-custom-post-type-taxonomy.php`:

- `$taxonomy_key`: `{string}` taxonomy id
- `get_labels()`: `{function}` use it to define the labels of the taxonomy
- `get_tax_slug()`: `{function}` if `has_rewrite` is set to `true`, you must define the taxonomy slug with a translation string
  - example:
    ```
    /**
     * Get taxonomy slug
     *
     * @return string
     */
    final public function get_tax_slug(): string {
    	return _x( 'categorie', 'Realisation Category slug for permalinks', 'lesanimals' );
    }
    ```

## 🔖 Add an other taxonomy

_— To document —_
