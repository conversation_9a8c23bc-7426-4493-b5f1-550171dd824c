<?php
/**
 * Register CustomPostType_Taxonomy taxonomy
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\CustomPostType;

use LesAnimals\Core\Taxonomy\Register_Taxonomy;

class Register_Custom_Post_Type_Taxonomy extends Register_Taxonomy {
	use Config;
	use \LesAnimals\Core\Permalinks\Aid;

	public string $taxonomy_key = 'custom_post_type_taxo';

	/**
	 * Get labels
	 *
	 * @return array
	 */
	final public function get_labels(): array {
		return array(
			'name'          => _x( 'Taxonomies', 'Custom_Post_Type_Taxonomy label', 'lesanimals' ),
			'singular_name' => _x( 'Taxonomy', 'Custom_Post_Type_Taxonomy label', 'lesanimals' ),
			'search_items'  => _x( 'Search taxonomies', 'Custom_Post_Type_Taxonomy label', 'lesanimals' ),
			'edit_item'     => _x( 'Edit taxonomy', 'Custom_Post_Type_Taxonomy label', 'lesanimals' ),
			'add_new_item'  => _x( 'Add new taxonomy', 'Custom_Post_Type_Taxonomy label', 'lesanimals' ),
		);
	}

	/**
	 * Get taxonomy slug
	 *
	 * @return string
	 */
	// TODO: to remove if 'has_rewrite' is set to false
	final public function get_tax_slug(): string {
		return _x( 'taxonomy_slug', 'Custom_Post_Type_Taxonomy slug for permalinks', 'lesanimals' );
	}
}

new Register_Custom_Post_Type_Taxonomy();
