<?php
/**
 * Context for CustomPostType
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\CustomPostType;

use Timber;

class Context {
	use Config;
	use \LesAnimals\Core\PostType\Aid;
	use \LesAnimals\Core\Permalinks\Aid;

	/**
	 * Class construct
	 */
	public function __construct() {
		add_filter( 'timber/context', array( $this, 'setup_context' ) );
	}

	/**
	 * Add variables to Timber Context
	 *
	 * @param array $context Timber context
	 * @return array
	 */
	public function setup_context( array $context ): array {
		if ( ! $this->is_archive() && ! $this->is_tax() && ! $this->is_singular() ) {
			return $context;
		}

		if ( $this->is_archive() || $this->is_tax() ) { // Archive and taxonomy
			return $context; // TODO : to remove
		} elseif ( $this->is_singular() ) { // Singular
			$context[ 'post' ] = Timber::get_post();
			$context[ 'archive' ] = Timber::get_post( $this->get_post_archive( $this->post_type_key ) );
		}

		return $context;
	}
}

new Context();
