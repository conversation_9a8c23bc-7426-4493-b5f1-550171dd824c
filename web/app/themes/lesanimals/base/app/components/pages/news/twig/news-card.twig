{#
Fields:
	news_id (int) mandatory
	class (string) optional, additional class
	img_ratio (number) optional
	img_width (array) mandatory, array of image width
	img_sizes (string) mandatory, conditional CSS for image width
#}

{% set news      = Post( news_id ) %}
{% set img       = Image( news.meta( 'main_img' ) ) %}
{% set img_ratio = img_ratio ? img_ratio : img.width / img.height >= 1 ? img.width / img.height : 1 %}

<article class="news_card {{ class }}">
	<a href="{{ news.link }}" class="news_card--link">
		<div class="news_card--img_wrapper">
			{% include 'img.twig' with {
				id:    news.meta( 'main_img' ),
				ratio: img_ratio,
				width: img_width,
				sizes: img_sizes,
				class: 'news_card--img_cont'
			} %}
		</div>
		<div class="news_card--infos">
			<div class="news_card--infos-category">{{ news.category.name }}</div>
			<div class="news_card--infos-date">{{ news.post_date_gmt | date( 'F Y' ) }}</div>
		</div>
		<h3 class="news_card--title">{{ news.title }}</h3>
	</a>
</article>
