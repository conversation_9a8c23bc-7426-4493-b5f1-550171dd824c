{#
Fields:
	search_post (\Timber\Timber\Post) mandatory
#}
<a class="search_card search_card--link search_card--type-{{ search_post.post_type }}" href="{{ search_post.link }}" data-post-type="{{ search_post.post_type }}"{% if search_post.markets_slugs is not empty %} data-market="{{ search_post.markets_slugs }}"{% endif %}>
	<div class="search_card__content">
		<div class="search_card__categories">
			{% if search_post.post_type != "product" %}
				<span class="button button--small button--plain-lightgrey search_card__category--post-type">{{ search_post.post_type_name }}</span>
			{% endif %}
			{% for market in search_post.terms('market') %}
				<span class="button button--small button--plain-pink search_card__category--market">{{ market.title }}</span>
			{% endfor %}
			{% for category in search_post.categories %}
				<span class="button button--small button--plain-lightgrey search_card__category--other">{{ category.title }}</span>
			{% endfor %}
		</div>
		<h3 class="search_card__title">{{ search_post.title }}</h3>
		{% if search_post.description %}
			<div class="search_card__description">{{ search_post.description }}</div>
		{% endif %}
	</div>
	{{ svg('icon-arrow') }}
</a>
