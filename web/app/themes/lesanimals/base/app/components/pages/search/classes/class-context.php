<?php
/**
 * Context for Search
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\Search;

use Timber;

class Context {
	use Aid;

	/**
	 * Class construct
	 */
	public function __construct() {
		add_filter( 'timber/context', array( $this, 'setup_context' ), 9 );
	}

	/**
	 * Add variables to Timber Context
	 *
	 * @param array $context Timber context
	 * @return array
	 */
	public function setup_context( array $context ): array {
		$context[ 'search' ] = array();
		$context[ 'search_form' ] = array();

		if ( is_search() ) {
			$context[ 'posts' ] = Timber::get_posts( false, '\\LesAnimals\\Search\\Card_Post' );
			$post_types = array(
				'all' => array(
					'name'  => __( 'Tous', 'lesanimals' ),
					'count' => count( $context[ 'posts' ] ),
				),
			);
			foreach ( $context[ 'posts' ] as $post ) {
				if ( ! array_key_exists( $post->post_type, $post_types ) ) {
					$post_types[ $post->post_type ] = array(
						'name'  => get_post_type_object( $post->post_type )->labels->singular_name,
						'count' => 0,
					);
				}
				++$post_types[ $post->post_type ][ 'count' ];
			}
			$context[ 'post_types' ] = $post_types;
		}

		$context[ 'search' ][ 'url_slug' ] = _x( 'recherche', 'Search url slug', 'lesanimals' );
		$context[ 'search' ][ 'query' ] = is_search() ? get_query_var( 's' ) : '';
		$context[ 'search_form' ][ 'keywords' ] = $this->get_default_search_terms();

		return $context;
	}
}

new Context();
