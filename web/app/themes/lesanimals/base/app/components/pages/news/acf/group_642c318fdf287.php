<?php 

if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
	'key' => 'group_642c318fdf287',
	'title' => 'Page - Actualité - Détail - Intro',
	'fields' => array(
		array(
			'key' => 'field_6447ebb717183',
			'label' => '',
			'name' => 'intro',
			'aria-label' => '',
			'type' => 'group',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'layout' => 'block',
			'acfe_seamless_style' => 0,
			'acfe_group_modal' => 0,
			'acfe_group_modal_close' => 0,
			'acfe_group_modal_button' => '',
			'acfe_group_modal_size' => 'large',
			'sub_fields' => array(
				array(
					'key' => 'field_6447f38a564df',
					'label' => 'Image à la une',
					'name' => 'main_img_id',
					'aria-label' => '',
					'type' => 'image',
					'instructions' => '<i>Taille d\'image minimum recommandée&nbsp;: 2500&nbsp;x&nbsp;1285&nbsp;px.</i>',
					'required' => 1,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'acfe_save_meta' => 0,
					'uploader' => '',
					'return_format' => 'id',
					'upload_folder' => '',
					'acfe_thumbnail' => 0,
					'required_message' => '',
					'min_width' => '',
					'min_height' => '',
					'min_size' => '',
					'max_width' => '',
					'max_height' => '',
					'max_size' => '',
					'mime_types' => '',
					'preview_size' => 'medium',
					'library' => 'all',
				),
				array(
					'key' => 'field_64b003d74cf73',
					'label' => 'Catégories',
					'name' => 'category',
					'aria-label' => '',
					'type' => 'acfe_taxonomy_terms',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'acfe_save_meta' => 0,
					'taxonomy' => array(
						0 => 'category',
					),
					'allow_terms' => '',
					'allow_level' => '',
					'field_type' => 'checkbox',
					'min' => '',
					'max' => '',
					'default_value' => array(
					),
					'return_format' => 'id',
					'layout' => 'vertical',
					'toggle' => 0,
					'save_terms' => 1,
					'load_terms' => 0,
					'choices' => array(
					),
					'ui' => 0,
					'multiple' => 0,
					'allow_null' => 0,
					'ajax' => 0,
					'placeholder' => '',
					'search_placeholder' => '',
					'allow_custom' => 0,
					'other_choice' => 0,
				),
			),
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'post',
			),
		),
	),
	'menu_order' => 5,
	'position' => 'normal',
	'style' => 'seamless',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'acfe_autosync' => array(
		0 => 'php',
		1 => 'json',
	),
	'acfe_form' => 0,
	'acfe_display_title' => 'Intro',
	'acfe_meta' => '',
	'acfe_note' => '',
	'acfe_categories' => array(
		'page' => 'Page',
	),
	'modified' => 1691491153,
));

endif;