<?php
/**
 * Card Post
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\Search;

use Timber\Post;

class Card_Post extends Post {

	protected string $post_type_name;
	protected string $markets_slugs;
	protected string $description;

	/**
	 * Gets the post type name
	 *
	 * @return string
	 */
	public function post_type_name(): string {
		if ( ! isset( $this->post_type_name ) ) {
			$this->post_type_name = get_post_type_object( $this->post_type )->labels->singular_name;
		}
		return $this->post_type_name;
	}

	/**
	 * Gets the slugs of the markets
	 *
	 * @return string
	 */
	public function markets_slugs(): string {
		if ( ! isset( $this->markets_slugs ) ) {
			$markets = $this->terms( 'market' );
			$markets_slugs = '';
			if ( ! empty( $markets ) ) {
				foreach ( $markets as $market ) {
					$markets_slugs .= ' ' . $market->slug;
				}
			}
			$this->markets_slugs = trim( $markets_slugs );
		}
		return $this->markets_slugs;
	}

	/**
	 * Gets a short description,
	 * depending on the post type
	 *
	 * @return string
	 */
	public function description(): string {
		if ( ! isset( $this->description ) ) {
			switch ( $this->post_type ) {
				case 'product':
					$this->description = ! empty( $this->meta( 'header_short_description' ) ) ? $this->meta( 'header_short_description' ) : '';
					break;
				case 'page':
					$this->description = ! empty( $this->meta( 'excerpt' ) ) ? $this->meta( 'excerpt' ) : '';
					break;
				case 'post':
					$this->description = ! empty( $this->preview() ) ? $this->preview() : '';
					break;
				default:
					$this->description = '';
					break;
			}
		}
		return $this->description;
	}
}
