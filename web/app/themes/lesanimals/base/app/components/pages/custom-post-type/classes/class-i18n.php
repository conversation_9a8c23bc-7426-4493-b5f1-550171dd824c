<?php
/**
 * I18n for CustomPostType
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\CustomPostType;

class I18N {
	use Config;

	/**
	 * Class construct
	 */
	public function __construct() {
		add_filter( 'timber/context', array( $this, 'setup_context' ) );
	}

	/**
	 * Add the option to Timber Context
	 *
	 * @param array $context Timber context
	 * @return array
	 */
	public function setup_context( array $context ): array {
		$context[ 'i18n' ][ $this->post_type_key ] = array(
			'all'        => __( 'All custom post types', 'lesanimals' ),
			'no_results' => __( 'No custom post type available', 'lesanimals' ),
			'show_more'  => __( 'Show more custom post type', 'lesanimals' ),
			'show_less'  => __( 'Show less custom post type', 'lesanimals' ),
			'item'       => __( 'Custom post type', 'lesanimals' ),
			'items'      => __( 'Custom post types', 'lesanimals' ),
		);

		return $context;
	}
}

new I18N();
