<?php
/**
 * Context for News Archive
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\News;

class Context_News_Archive {

	/**
	 * Construct
	 */
	public function __construct() {
		add_filter( 'timber/context', array( $this, 'setup_context' ) );
	}

	/**
	 * Add variables to Timber Context
	 *
	 * @param array $context context
	 * @return array
	 */
	public function setup_context( array $context ): array {
		if ( ! is_home() && ! is_category() ) {
			return $context;
		}

		$context[ 'post' ] = Timber::get_post();
		$context[ 'news' ] = Timber::get_posts();

		$context[ 'news_filters' ] = $this->get_news_filters_context();

		return $context;
	}

	/**
	 * Get the news filters
	 *
	 * @return array
	 */
	private function get_news_filters_context(): array {
		$news_opt = get_fields( 'options' )[ 'news' ];
		$news_filters = array();

		// all
		$news_filters = array(
			array(
				'slug'   => 'tous',
				'name'   => __( 'Tous', 'lesanimals' ),
				'active' => ! is_category(),
				'link'   => get_permalink( $news_opt[ 'page_id' ] ),
			),
		);

		// categories
		$news_categories = get_terms( array(
			'taxonomy'   => 'category',
			'hide_empty' => true,
		) );
		foreach ( $news_categories as $category ) {
			$news_filters[] = array(
				'slug'   => $category->slug,
				'name'   => $category->name,
				'active' => is_category( $category->term_id ),
				'link'   => get_term_link( $category->term_id ),
			);
		}

		return $news_filters;
	}
}

new Context_News_Archive();
