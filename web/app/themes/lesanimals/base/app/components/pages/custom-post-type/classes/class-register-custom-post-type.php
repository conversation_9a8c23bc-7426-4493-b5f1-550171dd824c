<?php
/**
 * Register CustomPostType post-type
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\CustomPostType;

use LesAnimals\Core\PostType\Register_Post_Type;

class Register_Custom_Post_Type extends Register_Post_Type {
	use Config;

	/**
	 * Get labels
	 *
	 * @return array
	 */
	final public function get_labels(): array {
		return array(
			'name'          => _x( 'Custom_post_types', 'Custom_Post_Type label', 'lesanimals' ),
			'singular_name' => _x( 'Custom_post_type', 'Custom_Post_Type label', 'lesanimals' ),
			'menu_name'     => _x( 'Custom_post_types', 'Custom_Post_Type label', 'lesanimals' ),
			'search_items'  => _x( 'Search custom_post_types', 'Custom_Post_Type label', 'lesanimals' ),
			'all_items'     => _x( 'All custom_post_types', 'Custom_Post_Type label', 'lesanimals' ),
			'edit_item'     => _x( 'Edit custom_post_type', 'Custom_Post_Type label', 'lesanimals' ),
			'add_new_item'  => _x( 'Add a custom_post_type', 'Custom_Post_Type label', 'lesanimals' ),
			'add_new'       => _x( 'Add a custom_post_type', 'Custom_Post_Type label', 'lesanimals' ),
		);
	}
}

new Register_Custom_Post_Type();
