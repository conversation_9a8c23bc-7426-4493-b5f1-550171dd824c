<?php
/**
 * Register Email Signature post-type
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\RegisterEmailSignature;

use LesAnimals\Core\PostType\Register_Post_Type;

class Register_Email_Signature extends Register_Post_Type {
	use Config;

	/**
	 * Get labels
	 *
	 * @return array
	 */
	final public function get_labels(): array {
		return array(
			'name'          => _x( 'Signatures d\'email', 'Signatures d\'email label', 'lesanimals' ),
			'singular_name' => _x( 'Signature d\'email', 'Signatures d\'email label', 'lesanimals' ),
			'menu_name'     => _x( 'Signatures d\'email', 'Signatures d\'email label', 'lesanimals' ),
			'search_items'  => _x( 'Rechercher des signatures d\'email', 'Signatures d\'email label', 'lesanimals' ),
			'all_items'     => _x( 'Toutes les signatures d\'email', 'Signatures d\'email label', 'lesanimals' ),
			'edit_item'     => _x( 'Éditer une signature d\'email', 'Signatures d\'email label', 'lesanimals' ),
			'add_new_item'  => _x( 'Ajouter une signature d\'email', 'Signatures d\'email label', 'lesanimals' ),
			'add_new'       => _x( 'Ajouter une signature d\'email', 'Signatures d\'email label', 'lesanimals' ),
		);
	}
}

new Register_Email_Signature();
