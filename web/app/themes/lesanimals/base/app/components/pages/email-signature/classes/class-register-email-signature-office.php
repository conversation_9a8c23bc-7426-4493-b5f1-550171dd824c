<?php
/**
 * Register Email Signature Office taxonomy
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\RegisterEmailSignature;

use LesAnimals\Core\Taxonomy\Register_Taxonomy;

class Register_Email_Signature_Office extends Register_Taxonomy {
	use Config;
	use \LesAnimals\Core\Permalinks\Aid;

	public string $taxonomy_key = 'email_signature_office';

	/**
	 * Get labels
	 *
	 * @return array
	 */
	final public function get_labels(): array {
		return array(
			'name'          => _x( 'Bureaux', 'Signatures d\'email bureau label', 'lesanimals' ),
			'singular_name' => _x( 'Bureau', 'Signatures d\'email bureau label', 'lesanimals' ),
			'search_items'  => _x( 'Rechercher des bureaux', 'Signatures d\'email bureau label', 'lesanimals' ),
			'edit_item'     => _x( 'Éditer un bureau', 'Signatures d\'email bureau label', 'lesanimals' ),
			'add_new_item'  => _x( 'Ajouter un bureau', 'Signatures d\'email bureau label', 'lesanimals' ),
		);
	}
}

new Register_Email_Signature_Office();
