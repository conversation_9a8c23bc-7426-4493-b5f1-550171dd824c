<?php
/**
 * Config for Email Signature
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\RegisterEmailSignature;

trait Config {
	public string $post_type_key = 'email_signature';
	public int $posts_per_page = 12;
	public array $taxonomies_keys = array(
		'email_signature_office',
	);

	public array $post_type_args = array(
		'menu_position' => 35,
		'menu_icon'     => 'dashicons-email',
	);

	public array $post_type_params = array(
		'has_archive'      => false,
		'has_flexibles'    => false,
		'has_menu_classes' => false,
	);

	public array $taxonomies_args = array(
		'email_signature_office' => array(
			'public'             => false,
			'publicly_queryable' => false,
			'show_in_nav_menus'  => false,
		),
	);

	public array $taxonomies_params = array(
		'has_rewrite' => false,
	);

	/**
	 * Get post type slug
	 *
	 * @return string
	 */
	public function get_post_type_slug(): string {
		return _x( 'signature-email', 'Signatures d\'email slug for permalinks', 'lesanimals' );
	}
}
