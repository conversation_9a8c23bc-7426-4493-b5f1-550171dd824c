<?php
/**
 * I18n for Email Signature
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\RegisterEmailSignature;

class I18N {
	use Config;

	/**
	 * Class construct
	 */
	public function __construct() {
		add_filter( 'timber/context', array( $this, 'setup_context' ) );
	}

	/**
	 * Add the option to Timber Context
	 *
	 * @param array $context Timber context
	 * @return array
	 */
	public function setup_context( array $context ): array {
		$context[ 'i18n' ][ $this->post_type_key ] = array(
			'copy_signature'   => __( 'Copier la signature email', 'lesanimals' ),
			'signature_copied' => __( 'Signature email copiée', 'lesanimals' ),
		);

		return $context;
	}
}

new I18N();
