<?php
/**
 * Config for CustomPostType
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\CustomPostType;

trait Config {
	public string $post_type_key = 'custom_post_type';
	public int $posts_per_page = 12;
	public array $taxonomies_keys = array(
		'custom_post_type_taxo',
	);

	// public string $template_path = 'app/components/pages/custom-post-type/controllers/template-custom-post-type.php';

	/**
	 * Get post type slug
	 *
	 * @return string
	 */
	// TODO: to remove if 'has_archive' is set to true
	public function get_post_type_slug(): string {
		return _x( 'post_type_slug', 'Custom Post Type slug for permalinks', 'lesanimals' );
	}
}
