<?php
/**
 * Search query
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Pages\Search;

use <PERSON>ber\MenuItem;
use WP_Post;
use WP_Query;

class Query {

	/**
	 * The url slug of the search
	 *
	 * @var string
	 */
	protected string $url_slug = '';

	/**
	 * Class construct
	 */
	public function __construct() {

		// Get the search url
		$this->url_slug = _x( 'recherche', 'Search url slug', 'lesanimals' );

		// Nice url
		add_action( 'template_redirect', array( $this, 'template_redirect' ) );
		add_action( 'init', array( $this, 'rewrite_search_slug' ) );

		// Correct post types
		add_action( 'pre_get_posts', array( $this, 'pre_get_posts' ) );

		// Class for menu
		add_filter( 'nav_menu_css_class', array( $this, 'nav_menu_css_class' ), 10, 2 );

		// Filter the request to lower case and accent remove
		add_filter( 'request', array( $this, 'modify_search_term' ) );

		// Add ACF field to WP Search
		add_filter( 'posts_join', array( $this, 'search_join' ) );
		add_filter( 'posts_where', array( $this, 'search_where' ) );
		add_filter( 'posts_distinct', array( $this, 'search_distinct' ) );
	}

	/**
	 * Redirect to the dedicated search page
	 *
	 * @return void
	 */
	public function template_redirect(): void {
		if ( is_search() && array_key_exists( 's', $_GET ) && ! empty( $_GET[ 's' ] ) ) {
			wp_safe_redirect( home_url( '/' . ( $this->url_slug ) . '/' ) . rawurlencode( get_query_var( 's' ) ) );
			exit();
		}
	}

	/**
	 * Search rewrite rule
	 *
	 * @return void
	 */
	public function rewrite_search_slug(): void {
		add_rewrite_rule(
			$this->url_slug . '(/([^/]+))?(/([^/]+))?(/([^/]+))?/?',
			'index.php?s=$matches[2]&paged=$matches[6]',
			'top'
		);
	}

	/**
	 * Fiilter query
	 *
	 * @param WP_Query $query The WP Query to filter
	 * @return void
	 */
	public function pre_get_posts( WP_Query $query ): void {
		if ( ! is_admin() && $query->is_main_query() && $query->is_search() ) {
			$query->set( 'posts_per_page', 100 );
			$query->set( 'post_type', array( 'post', 'press', 'mediatheque', 'page', 'product' ) );
		}
	}

	/**
	 * Filter the nav menus CSS classes
	 *
	 * @param string[]         $classes   Array of the CSS classes that are applied to the menu item's `<li>` element.
	 * @param WP_Post|MenuItem $menu_item The current menu item object.
	 * @return string[] Menu css classes
	 */
	public function nav_menu_css_class( array $classes, WP_Post|MenuItem $menu_item ): array {
		if ( is_search() ) {
			if ( $menu_item->object_id === get_option( 'page_for_posts' ) ) {
				foreach ( $classes as $index => $class ) {
					if ( strpos( $class, 'current_page_parent' ) !== false ) {
						unset( $classes[ $index ] );
					}
				}
			}
		}
		return $classes;
	}

	/**
	 * Filter the query vars
	 *
	 * @param array $query_vars The array of requested query variables.
	 * @return array The vars
	 */
	public function modify_search_term( $query_vars ) {
		if ( ! empty( $query_vars[ 's' ] ) ) {
			$query_vars[ 's' ] = urldecode( $query_vars[ 's' ] );
		}
		return $query_vars;
	}

	/**
	 * Filter the query vars
	 *
	 * @param string $join The JOIN clause of the query.
	 * @return string The vars
	 */
	public function search_join( string $join ): string {
		global $wpdb;

		if ( is_search() ) {
			$join .= ' LEFT JOIN ' . $wpdb->postmeta . ' ON ' . $wpdb->posts . '.ID = ' . $wpdb->postmeta . '.post_id ';
		}

		return $join;
	}

	/**
	 * Modify the search query with posts_where
	 *
	 * @param string $where The WHERE clause of the query.
	 * @return string $where
	 */
	public function search_where( string $where ): string {
		global $wpdb;

		if ( is_search() ) {
			$where = preg_replace(
				'/\(\s*' . $wpdb->posts . ".post_title\s+LIKE\s*(\'[^\']+\')\s*\)/",
				'(' . $wpdb->posts . '.post_title LIKE $1) OR (' . $wpdb->postmeta . '.meta_value LIKE $1)',
				$where
			);
		}

		return $where;
	}

	/**
	 * Prevent duplicates
	 *
	 * @param string $where The WHERE clause of the query.
	 * @return string $where
	 */
	public function search_distinct( string $where ): string {

		if ( is_search() ) {
			return 'DISTINCT';
		}

		return $where;
	}
}

new Query();
