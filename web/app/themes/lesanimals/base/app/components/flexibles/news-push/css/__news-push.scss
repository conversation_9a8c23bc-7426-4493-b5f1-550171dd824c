// **************************************
// -------- Flexible - News push --------
// **************************************


.flx_news_push {
	padding-top: 100px;
	padding-bottom: 125px;
	background-color: $color_white;
	overflow: hidden;
}

.flx_news_push_inner {
	display: flex;
	flex-wrap: wrap;
	align-items: flex-end;
}


// -------- Header --------

.flx_news_sp--header {
	display: flex;
	column-gap: calc(var(--grid_column_gap) * 2);
	align-items: flex-end;
	margin-bottom: 38px;
}

.flx_news_sp--title {

	@extend .title_3;
	flex: 1 0 0;
}

.flx_news_sp--link {
	margin-left: auto;
	margin-bottom: 6px;
}


// -------- News --------

.flx_news_sp--slider {
	--slider_item_width: #{col_w(4)};

	width: 100%;
}


// -------- Responsive --------

@include query($until: 960px) {

	.flx_news_push {
		padding: 100px 0 120px 0;
	}

	.flx_news_sp--slider {
		--slider_item_width: calc(#{col_w(6)} - 15px);

		width: calc(100% - 30px);
	}

}

@include query($until: 640px) {

	.flx_news_push {
		padding: 80px 0;
	}

	.flx_news_push_inner {
		display: flex;
		flex-direction: column;
	}

	.flx_news_sp--header {
		display: contents;
	}

	.flx_news_sp--title {
		order: 1;
	}

	.flx_news_sp--link {
		order: 3;
		margin: 40px auto 0 auto;
	}

	.flx_news_sp--slider {
		--slider_item_width: 70%;

		order: 2;
		width: 100%;
	}

}

@include query($until: 400px) {

	.flx_news_sp--slider {
		--slider_item_width: 85%;
	}

}

