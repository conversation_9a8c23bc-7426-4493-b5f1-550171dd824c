<?php

if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
	'key' => 'group_642bf0de1d99e',
	'title' => 'Flexible - Remontée d\'actualités',
	'fields' => array(
		array(
			'key' => 'field_642bf0de657ba',
			'label' => 'Contenus',
			'name' => '',
			'aria-label' => '',
			'type' => 'message',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'message' => 'Éditer les contenus du module dans <a href="admin.php?page=acf-options-options">Options > Pages > Actualités > Remontée d\'actualités</a>',
			'new_lines' => 'wpautop',
			'esc_html' => 0,
		),
		array(
			'key' => 'field_642c3451f5be2',
			'label' => 'Type d\'actualités affichées',
			'name' => 'news_type',
			'aria-label' => '',
			'type' => 'radio',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'acfe_save_meta' => 0,
			'choices' => array(
			),
			'default_value' => '',
			'return_format' => 'array',
			'allow_null' => 0,
			'other_choice' => 0,
			'layout' => 'vertical',
			'save_other_choice' => 0,
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'all',
			),
			array(
				'param' => 'post_type',
				'operator' => '!=',
				'value' => 'post',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'seamless',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => false,
	'description' => '',
	'show_in_rest' => 0,
	'acfe_autosync' => array(
		0 => 'php',
	),
	'acfe_form' => 0,
	'acfe_display_title' => 'Remontée d\'actualités',
	'acfe_meta' => '',
	'acfe_note' => '',
	'lesanimals_save_as_layout' => 1,
	'lesanimals_layout_slug' => 'news-push',
	'lesanimals_layout_settings' => '',
	'lesanimals_layout_min' => '',
	'lesanimals_layout_max' => '',
	'acfe_categories' => array(
		'flexible' => 'Flexible',
	),
	'modified' => 1691492919,
));

endif;
