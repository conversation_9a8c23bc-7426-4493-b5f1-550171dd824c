<?php
/**
 * ACF for News Push flexible
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\NewsPush;

use Timber;

class ACF {

	/**
	 * Construct
	 */
	public function __construct() {
		if ( ! is_admin() ) {
			return;
		}

		add_filter( 'acf/load_field/key=field_642c3451f5be2', array( $this, 'custom_news_type_displayed' ) );
	}

	/**
	 * Custom news type
	 *
	 * @param array $field field
	 * @return array
	 */
	public function custom_news_type_displayed( array $field ): array {
		if ( acfe_is_admin_screen() ) {
			return $field;
		}

		$categories = Timber::get_terms( array(
			'taxonomy' => 'category',
		) );

		$field[ 'choices' ] = array(
			'all' => 'Toutes catégories',
		);

		foreach ( $categories as $category ) {
			$field[ 'choices' ][ $category->id ] = $category->name;
		}

		return $field;
	}
}

new ACF();
