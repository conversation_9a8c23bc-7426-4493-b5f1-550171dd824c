<?php
/**
 * Context for NewsPush flexible
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Flexibles\NewsPush;

use Timber;

class Context {

	/**
	 * Initialize the flexible
	 * Enables hooking the variables
	 *
	 * @param array $flx The flexible object
	 *
	 * @return array
	 */
	public function init( array $flx ): array {
		/*$news_opt = get_fields( 'options' )[ 'news' ];
		$news_opt_push = $news_opt[ 'push' ];

		$flx[ 'type' ] = 'default';

		$flx[ 'title' ] = $news_opt_push[ 'title' ];
		$flx[ 'btn' ] = array(
			'txt'  => $news_opt_push[ 'btn_txt' ],
			'href' => $this->get_btn_link( $flx[ 'news_type' ][ 'value' ], $news_opt[ 'page_id' ] ),
		);

		$news = $this->get_news( $flx[ 'news_type' ][ 'value' ] );
		$flx[ 'items' ] = $this->get_items( $news );*/

		return $flx;
	}

	/**
	 * Get the button link
	 *
	 * @param string|int $type         News type
	 * @param int        $news_page_id News page id
	 *
	 * @return string
	 */
	/*private function get_btn_link( string|int $type, int $news_page_id ): string {
		if ( $type === 'all' ) {
			$href = get_permalink( $news_page_id );
		} else {
			$href = get_category_link( $type );
		}

		return $href;
	}*/

	/**
	 * Get the last 3 news to display
	 *
	 * @param string|int $type News type
	 *
	 * @return array
	 */
	/*private function get_news( string|int $type ): array {
		if ( $type === 'all' ) {
			$news = Timber::get_posts( array(
				'post_type'      => 'post',
				'posts_per_page' => 3,
				'order'          => 'desc',
			) );
		} else {
			$news = Timber::get_posts( array(
				'post_type'      => 'post',
				'posts_per_page' => 3,
				'cat'            => $type,
				'order'          => 'desc',
			) );
		}

		return $news;
	}*/

	/**
	 * Get items
	 *
	 * @param array $list_news List of news
	 *
	 * @return array
	 */
	/*private function get_items( array $list_news ): array {
		$items = array();

		$context = Timber::context();
		$context[ 'img_ratio' ] = 410 / 250;
		$context[ 'img_width' ] = array( 400, 600, 750, 900, 1200 );
		$context[ 'sizes' ] = '(max-width: 500px) 91.5vw, (max-width: 640px) 460px, (max-width: 960px) 46vw, (max-width: 1999px) 30vw, (min-width: 2000px) 600px';
		$context[ 'class' ] = 'slider--item';
		$context[ 'is_preview' ] = is_admin();

		foreach ( $list_news as $news ) {
			$context[ 'news_id' ] = $news->id;
			$news_item = Timber::compile( 'news-card.twig', $context );
			$items[] = $news_item;
		}

		return $items;
	}*/
}
