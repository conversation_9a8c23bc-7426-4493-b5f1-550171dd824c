<?php
/**
 * Context for Breadcrumbs
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Partials\Breadcrumbs;

class Context {

	/**
	 * Class construct
	 */
	public function __construct() {
		add_filter( 'timber/context', array( $this, 'setup_context' ) );
	}

	/**
	 * Add variables to Timber Context
	 *
	 * @param array $context Timber context
	 * @return array
	 */
	public function setup_context( array $context ): array {
		if ( function_exists( 'seopress_display_breadcrumbs' ) ) {
			$context[ 'breadcrumbs' ] = seopress_display_breadcrumbs( false );
		} else {
			$context[ 'breadcrumbs' ] = false;
		}

		return $context;
	}
}

new Context();
