import AbstractView from 'abstracts/AbstractView';
import Screen from 'controllers/Screen';
import Main from 'controllers/Main';

export default class Submenu extends AbstractView {
	/**
	 * @type {Cash} $items
	 */
	#$items;

	/**
	 * @type {Cash} $linkActive
	 */
	#$linkActive;

	/**
	 * @type {number} Scroll x position
	 */
	#scrollX = 0;

	/**
	 * @type {number} Scroll step
	 */
	#scrollStep = 0;

	/**
	 * @type {boolean} Does the menu have a scroll?
	 */
	#hasScroll = false;

	/**
	 * @type {boolean} Can scroll to the left?
	 */
	#canScrollLeft = false;

	/**
	 * @type {boolean} Can scroll to the right?
	 */
	#canScrollRight = false;

	constructor( $wrapper ) {
		super( $wrapper );

		this.init();
	}

	initDOM() {
		this.#$items = this.$wrapper.find( '.submenu--items' );
		this.#$linkActive = this.#$items.find( '.submenu--link.active' );
	}

	bindEvents() {
		Screen.on( Screen.E.RESIZE, this.resize, this );
		Main.on( Main.E.RAF, this.raf, this );
	}

	unbindEvents() {
		Screen.off( Screen.E.RESIZE, this.resize, this );
		Main.off( Main.E.RAF, this.raf, this );
	}

	resize() {
		this.#scrollStep = Math.ceil( this.#$items[ 0 ].scrollWidth - this.#$items.width() );

		if ( this.#scrollStep > 0 ) {
			this.#hasScroll = true;

			this.positionActiveItem();
		} else {
			this.#hasScroll = false;
		}
	}

	raf() {
		if ( ! this.#hasScroll && ! this.#canScrollLeft && ! this.#canScrollRight ) {
			return;
		}

		this.#scrollX = Math.ceil( this.#$items[ 0 ].scrollLeft );

		if ( this.#canScrollLeft && this.#scrollX === 0 ) {
			this.#canScrollLeft = false;
			this.$wrapper[ 0 ].classList.remove( 'scroll_left' );
		} else if ( ! this.#canScrollLeft && this.#scrollX > 0 ) {
			this.#canScrollLeft = true;
			this.$wrapper[ 0 ].classList.add( 'scroll_left' );
		}

		if ( this.#canScrollRight && this.#scrollX >= this.#scrollStep ) {
			this.#canScrollRight = false;
			this.$wrapper[ 0 ].classList.remove( 'scroll_right' );
		} else if ( ! this.#canScrollRight && this.#scrollX < this.#scrollStep ) {
			this.#canScrollRight = true;
			this.$wrapper[ 0 ].classList.add( 'scroll_right' );
		}
	}

	positionActiveItem( $linkActive = this.#$linkActive ) {
		if ( ! this.#hasScroll || this.#$linkActive.length === 0 ) {
			return;
		}

		this.#$items[ 0 ].scrollLeft = this.#scrollX + $linkActive.position().left;
	}
}

