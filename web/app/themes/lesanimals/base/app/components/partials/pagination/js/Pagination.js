import AbstractView from 'abstracts/AbstractView';
import Main from 'controllers/Main';
import String_ from 'utils/String';
import Config from 'configs/Config';
import MainLoader from 'components/statics/main-loader/js/MainLoader';

export default class Pagination extends AbstractView {
	/**
	 * @type {HTMLElement} $breadcrumbs
	 */
	#$breadcrumbs;

	/**
	 * @type {HTMLElement} $items
	 */
	#$items;

	/**
	 * @type {NodeList} $pagination
	 */
	#$$pagination;

	/**
	 * @type {NodeList} $btn
	 */
	#$$btn;

	/**
	 * @type {boolean} Has breadcrumbs?
	 */
	#hasBreadcrumbs = false;

	/**
	 * @type {boolean} Are the prev or next items loading ?
	 */
	#isLoading = false;

	/**
	 * @type {boolean} Is the start reached?
	 */
	#startReached = false;

	/**
	 * @type {boolean} Is the end reached?
	 */
	#endReached = false;

	/**
	 * @param {HTMLElement} $wrapper
	 */
	constructor( $wrapper ) {
		super( $wrapper );

		this.#$items = this.$wrapper.querySelector( '[ data-pagination-items ]' );

		if ( this.#$items !== null ) {
			this.init();
		}
	}

	initDOM() {
		this.#$breadcrumbs = this.$wrapper.querySelector( '.breadcrumbs' );
		this.#$$pagination = this.$wrapper.querySelectorAll( '.pagination' );
		this.#$$btn = this.$wrapper.querySelectorAll( '.pagination--cta' );
	}

	initEl() {
		this.#hasBreadcrumbs = this.#$breadcrumbs !== null;
	}

	bindEvents() {
		this.eh.clickBtnLoad = this.#loadItems.bind( this );
		this.#$$btn.forEach( ( $el ) => $el.addEventListener( 'click', this.eh.clickBtnLoad ) );
	}

	unbindEvents() {
		this.#$$btn.forEach( ( $el ) => $el.removeEventListener( 'click', this.eh.clickBtnLoad ) );
	}

	#loadItems( e ) {
		e.preventDefault();

		if ( this.#isLoading ) {
			return;
		}

		const $btnClicked = e.currentTarget;
		const action = $btnClicked.getAttribute( 'data-action' );

		if ( ( action === 'prev' && this.#startReached ) || ( action === 'next' && this.#endReached ) ) {
			return;
		}

		this.#isLoading = true;

		$btnClicked.classList.add( 'loading' );

		const url = $btnClicked.href;

		fetch( url )
			.then( ( response ) => response.text() )
			.then( ( data ) => this.#onItemsLoad( data, $btnClicked, action, url ) )
			.catch( ( error ) => this.#onItemsError( error, url ) );
	}

	#onItemsLoad( data, $btnClicked, action, url ) {
		const $data = Config.rewriteUrls( String_.stringToHTML( data ) );

		this.#updateTitle( $data );
		this.#updateBtnPagination( $data, $btnClicked, action );
		this.#updateBreadcrumbs( $data );
		this.#addNewItems( $data, action );

		history.replaceState( {}, null, url );
		// insert tracking here if needed

		$btnClicked.classList.remove( 'loading' );

		this.#isLoading = false;
	}

	#updateTitle( $data ) {
		Main.$title.innerHTML = $data.title;
	}

	#updateBtnPagination( $data, $btnClicked, action ) {
		const $newBtn = $data.querySelector( `.pagination--cta[ data-action="${ action }" ]` );

		if ( $newBtn !== null ) {
			$btnClicked.href = $newBtn.href;
		} else if ( $newBtn === null && action === 'prev' ) {
			this.#startReached = true;

			this.#$$pagination[ 0 ].classList.add( 'hide' );
		} else if ( $newBtn === null && action === 'next' ) {
			this.#endReached = true;

			this.#$$pagination[ 1 ].classList.add( 'hide' );
		}
	}

	#updateBreadcrumbs( $data ) {
		if ( ! this.#hasBreadcrumbs ) {
			return;
		}

		this.#$breadcrumbs.innerHTML = $data.querySelector( '.breadcrumbs' ).innerHTML;
	}

	#addNewItems( $data, action ) {
		const $$newItem = $data.querySelectorAll( '[ data-pagination-item ]' );

		$$newItem.forEach( ( $el ) => {
			$el.classList.add( 'init' );
		} );

		if ( action === 'prev' ) {
			const indexMax = $$newItem.length - 1;
			$$newItem.forEach( ( $el, i ) => this.#$items.prepend( $$newItem[ indexMax - i ] ) );
		} else {
			$$newItem.forEach( ( $el ) => this.#$items.append( $el ) );
		}

		MainLoader.lazyloader.update();

		$$newItem.forEach( ( $el ) => {
			$el.offsetHeight; // eslint-disable-line no-unused-expressions
			$el.classList.remove( 'init' );
		} );
	}

	#onItemsError( error, url ) {
		console.error( 'Ajax pagination items load error — ', error ); // eslint-disable-line no-console
		window.location.href = url;
	}
}

