{#
Fields:
	data (object) mandatory
	txt (string) optional, default: 'Charger plus', text button
#}

{% set txt = txt ?? 'Charger plus' %}
{% set action = 'prev' in data.class ? 'prev' : 'next' %}

<div class="pagination pagination__{{ action }} cw{% if not data %} hide{% endif %}">
	{% if data %}
		<a href="{{ data.link }}" class="pagination--cta" data-no-ajax data-action="{{ action }}">{{ txt }}</a>
	{% endif %}
	{% include 'loader.twig' with {
		class: 'pagination--loader',
	} %}
</div>
