<!doctype html>
<html>

<head>
	<meta charset="UTF-8"/>
</head>

<body style="background-color: {{ colors.bg }}; padding: 16px 0;">

<center>
	<table width="600" border="0" cellspacing="0" cellpadding="0" style="border-spacing: 0; padding: 0px; border-radius: 8px; background-color: {{ colors.content_bg }}; overflow: hidden;">
		<tr>
			{% set img = get_image( datas.header.id ) %}
			<td colspan="3"><img src="{{ img.src | resize( 600, 225 ) }}" width="600" height="225" alt="" style="display: block;"/></td>
		</tr>
		<tr>
			<td width="50"></td>
			<td style="padding: 88px 16px 128px 16px; font-family: Arial, sans-serif; font-style: normal; font-size: 18px; line-height: 28px; text-align: left; color: {{ colors.txt }};">
				{{ datas.content }}
			</td>
			<td width="50"></td>
		</tr>
		<tr>
			<td colspan="3" style="padding: 16px 16px 16px 16px; font-family: Arial, sans-serif; font-style: normal; font-size: 12px; line-height: 24px; text-align: center; background-color: {{ colors.footer_bg }};">
				<a href="{{ SITE_URL }}" style="color: {{ colors.footer }}; font-weight:600;">{{ SITE_URL | replace( { 'https://': '', 'http://': '' } ) }}</a>
			</td>
		</tr>
	</table>
</center>

</body>
</html>
