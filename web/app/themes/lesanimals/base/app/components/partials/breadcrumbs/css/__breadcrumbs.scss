// *****************************
// -------- Breadcrumbs --------
// *****************************

.breadcrumbs {
	grid-column: 1/8 span;

	.breadcrumb {
		display: flex;
		flex-wrap: wrap;
		gap: 10px;
	}

	.breadcrumb-item {

		span {
			display: block;
		}
	}

	.breadcrumb-item:not(:last-child) {

		&::after {
			display: none;
		}

		a {
			display: block;
			text-decoration: none;
			transition: color $ease_default, background-color $ease_default;

			@include hover {
				color: $color_highlight;
			}
		}
	}

	.breadcrumb-item:last-child {

		span {
			background-color: rgba($color_highlight, 0.5);
		}
	}
}

/* -------- Responsive -------- */

@include query($until: 1280px) {

	.breadcrumbs {
		grid-column: 1/7 span;
	}
}

@include query($until: 800px) {

	.breadcrumbs {
		grid-column: 1/12 span;
	}

}
