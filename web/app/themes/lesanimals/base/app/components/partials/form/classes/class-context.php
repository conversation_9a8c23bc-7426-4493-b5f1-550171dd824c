<?php
/**
 * Context for Form
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Partials\Form;

class Context {

	private array $params;
	private Utils $utils;

	/**
	 * Constructor
	 *
	 * @param array $params The form params
	 */
	public function __construct( array $params ) {
		$this->params = $params;
		$this->utils = Utils::get_instance();

		add_filter( 'timber/context', array( $this, 'set_timber_context' ) );
	}

	/**
	 * Add variables to Timber Context
	 *
	 * @param array $context Timber context
	 * @return array
	 */
	public function set_timber_context( array $context ): array {
		$config = $this->utils->get_config( true, $this->params );
		$context[ $this->params[ 'id' ] . '_form' ] = $config;

		return $context;
	}
}
