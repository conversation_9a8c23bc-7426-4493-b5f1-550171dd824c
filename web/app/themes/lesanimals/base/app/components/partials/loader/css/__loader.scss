// ************************
// -------- Loader --------
// ************************


.loader {
	display: flex;
	justify-content: center;
	align-items: center;
	column-gap: 10px;
	position: absolute;
	left: calc(50% - 50px);
	top: 0;
	width: 100px;
	height: 48px;
	opacity: 0;
	transition: opacity 0.3s $ease_out_quad;
	pointer-events: none;
	z-index: 1;
}

.loader--dot {
	width: 5px;
	height: 5px;
	margin-top: 4px;
	border-radius: 50%;
	background-color: $color_black;
	animation: loader--dot 1s infinite $ease_in_out_quad;
	will-change: transform;

	&:nth-child(1) {
		animation-delay: 0s;
	}

	&:nth-child(2) {
		animation-delay: 0.15s;
	}

	&:nth-child(3) {
		animation-delay: 0.3s;
	}
}

@keyframes loader--dot {

	0% {
		transform: translateY(0);
	}

	50% {
		transform: translateY(-4px);
	}

	100% {
		transform: translateY(0);
	}
}

.loader_loading {
	opacity: 1;
	transition: opacity 0.3s $ease_out_quad 0.2s;
}

