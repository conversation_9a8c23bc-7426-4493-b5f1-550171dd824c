// *************************
// -------- Submenu --------
// *************************


.submenu {
}

.submenu__nav {
}

.submenu--items {
}

.submenu--item {
}

.submenu--link {

	&.active {
		color: $color_black;
		cursor: default;
	}

	@include hover {

		&:not(.active) {
			opacity: 0.5;
		}
	}
}


// -------- Responsive --------

@include query($until: 960px) {

	.submenu {
		width: 100%;
		background-color: $color_grey;

		&::before,
		&::after {
			content: "";
			position: absolute;
			top: 0;
			width: calc(var(--padding) * 3);
			height: 100%;
			border-radius: 0;
			opacity: 0;
			transition: opacity $ease_default;
			pointer-events: none;
			z-index: 1;
		}

		&::before {
			left: 0;
			background: linear-gradient(to left, rgba($color_white, 0) 0%, rgba($color_white, 0.8) 100%);
		}

		&::after {
			right: 0;
			background: linear-gradient(to right, rgba($color_white, 0) 0%, rgba($color_white, 0.8) 100%);
		}

		&.scroll_left::before {
			opacity: 1;
		}

		&.scroll_right::after {
			opacity: 1;
		}
	}

	.submenu__nav {
		width: calc(100% + var(--spacing) * 2);
		margin-left: calc(var(--spacing) * -1);
	}

	.submenu--items {
		flex-direction: row;
		flex-wrap: nowrap;
		justify-content: space-between;
		padding-bottom: 9px;
		scroll-snap-type: x mandatory;
		overflow-y: auto;
	}

	.submenu--item {
		flex: 0 0 1;
		display: block;
		padding: 0 var(--padding);
		scroll-snap-align: start;
	}

	.submenu--link {
		white-space: nowrap;
	}

}

