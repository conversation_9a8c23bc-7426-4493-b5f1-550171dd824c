<?php
/**
 * I18n for Error 404
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Partials\Form;

class I18N {

	/**
	 * Class construct
	 */
	public function __construct() {
		add_filter( 'timber/context', array( $this, 'setup_context' ) );
	}

	/**
	 * Add content to Timber Context
	 *
	 * @param array $context Timber context
	 * @return array
	 */
	public function setup_context( array $context ): array {
		$context[ 'i18n' ][ 'form' ] = array(
			'download' => __( 'Télécharger', 'lesanimals' ),
		);

		return $context;
	}
}

new I18N();
