// ***************************
// -------- Accordion --------
// ***************************


.accordion {
}

.accordion--item {

	&.open {

		.accordion--item-btn {

			&::before,
			&::after {
				transform: rotate(180deg);
				transition: transform 0.5s $ease_in_out_quart;
			}
		}

		.accordion--item-btn-txt {
			color: $color_highlight;
		}

		.accordion--item-content {
			grid-template-rows: 1fr;
			transition: grid-template-rows 0.5s $ease_in_out_quart;
		}
	}
}

.accordion--item-title {
}

.accordion--item-btn {
	position: relative;
	width: 100%;

	&::before,
	&::after {
		content: "";
		position: absolute;
		right: 4px;
		top: 24px;
		width: 16px;
		height: 2px;
		border-radius: 2px;
		background-color: $color_black;
		transition: transform 0.3s $ease_out_quart;
	}

	&::after {
		transform: rotate(90deg);
	}

	@include hover {

		.accordion--item-btn-txt {
			opacity: 0.5;
		}
	}
}

.accordion--item-btn-txt {
	transition: opacity $ease_default;
}

.accordion--item-content {
	display: grid;
	grid-template-rows: 0fr;
	transition: grid-template-rows 0.3s $ease_out_quart;
}

.accordion--item-content_inner {

	@extend .rich_text;
	overflow: hidden;
}

.accordion--item-content-txt {
}

