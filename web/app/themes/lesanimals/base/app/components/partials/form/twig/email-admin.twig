<!doctype html>
<html>

<head>
	<meta charset="UTF-8"/>
	<style type="text/css">
		a {
			color: {{ colors.data }};
		}
	</style>
</head>

<body style="background-color: {{ colors.bg }}; padding: 16px 0;">

<center>
	<table width="600" border="0" cellspacing="0" cellpadding="0" style="border-spacing: 0; padding: 0px; border-radius: 8px; background-color: {{ colors.content_bg }}; overflow: hidden;">
		<tr>
			{% set img = get_image( datas.header.id ) %}
			<td colspan="3"><img src="{{ img.src | resize( 600, 225 ) }}" width="600" height="225" alt="" style="display: block;"/></td>
		</tr>
		<tr>
			<td width="50"></td>
			<td style="padding: 88px 16px 56px 16px; font-family: Arial, sans-serif; font-style: normal; font-size: 18px; line-height: 28px; text-align: left; color: {{ colors.txt }};">
				{{ datas.content }}
			</td>
			<td width="50"></td>
		</tr>
		<tr>
			<td width="50"></td>
			<td>
				<table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-spacing: 0; border-collapse: collapse; margin: 0px; padding: 0px; font-family: Arial, sans-serif; font-style: normal; font-size: 16px; line-height: 24px; text-align: left; color: {{ colors.txt }};">
					{% for field_name, field in content.fields | filter( ( field, field_name ) => field_name != 'message' ) %}
						<tr style="{% if not loop.first %}border-top: 1px solid {{ colors.data_separator }};{% endif %}">
							<td style="padding:8px 0; padding-left: 16px;">{{ field.label | striptags }}</td>
							<td style="padding:8px 0; padding-right: 16px; color: {{ colors.data }}; text-align: right; font-weight: 700;">
								{% if ( field.type == 'file' ) and field.value is not empty %}
									<a href="{{ field.value }}" style="color: {{ colors.data }};">Télécharger</a>
								{% else %}
									{{ field.value }}
								{% endif %}
							</td>
						</tr>
					{% endfor %}
					{% if content.fields.message %}
						<tr>
							<td colspan="2" style="padding: 56px 16px 0 16px; line-height: 28px;">{{ content.fields.message.label }}</td>
						</tr>
						<tr>
							<td colspan="2" style="padding: 0 16px 128px 16px; line-height: 28px; color: {{ colors.data }}; font-weight: 700;">{{ content.fields.message.value }}</td>
						</tr>
					{% endif %}
				</table>
			</td>
			<td width="50"></td>
		</tr>
		<tr>
			<td colspan="3" style="padding: 16px 16px 16px 16px; font-family: Arial, sans-serif; font-style: normal; font-size: 12px; line-height: 24px; text-align: center; background-color: {{ colors.footer_bg }};">
				<a href="{{ SITE_URL }}" style="color: {{ colors.footer }}; font-weight:600;">{{ SITE_URL | replace( { 'https://': '', 'http://': '' } ) }}</a>
			</td>
		</tr>
	</table>
</center>

</body>
</html>
