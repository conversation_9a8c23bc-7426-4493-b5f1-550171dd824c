{#
Fields:
	class (string) optional, additional class
	items (object) mandatory
#}

{% if items %}
	<div class="accordion {{ class }}" data-component="accordion">
		{% for item in items %}
			{% set item_index = ( item_index | default(-1) ) + 1 %}
			<div class="accordion--item" data-accordion-item>
				<h2 class="accordion--item-title">
					<button type="button" class="accordion--item-btn" data-accordion-btn data-accordion-index="{{ item_index }}">
						<span class="accordion--item-btn-txt">{{ item.title }}</span>
					</button>
				</h2>
				<div class="accordion--item-content" data-accordion-content>
					<div class="accordion--item-content_inner">
						<div class="accordion--item-content-txt">{{ item.content }}</div>
					</div>
				</div>
			</div>
		{% endfor %}
	</div>
{% endif %}
