// *****************************
// -------- Form - File --------
// *****************************


.form_field_input_file__wrapper {
	position: relative;
	z-index: 1;
	overflow: hidden;
}

.form--input__file {
	cursor: pointer;
	display: block;
	appearance: none;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border: none;
	opacity: 0;
	z-index: 5;

	&::-webkit-file-upload-button {
		cursor: pointer;
	}

	@include hover {

		+ .form--field-input__file-name {
			border-color: rgba($color_black, 0.5);
		}
	}

	&:focus-visible:not(:hover) + .form--field-input__file-name {
		border-color: $color_black;
	}
}

.form--field-input__file-name {
	white-space: nowrap;
	overflow: hidden;
	padding-right: 48px;
	text-overflow: ellipsis;
}

.form--field-input__file-icon {
	position: absolute;
	right: 15px;
	top: 50%;
	width: 20px;
	height: 20px;
	margin-top: -10px;
}
