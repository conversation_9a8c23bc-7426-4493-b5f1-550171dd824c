{#
Fields:
	submenu (object) mandatory, list of items
	class (string) optional, additional class
#}

{% if submenu %}
	<div class="submenu submenu__nav {{ class }}" data-component="submenu">
		<nav>
			<ul class="submenu--items">
				{% for item in submenu.items %}
					<li class="submenu--item">
						<a class="submenu--link{% if item.is_active %} active{% endif %}" href="{{ item.link }}">{{ item.title }}</a>
					</li>
				{% endfor %}
			</ul>
		</nav>
	</div>
{% endif %}
