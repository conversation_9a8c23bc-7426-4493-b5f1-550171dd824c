# 📑 Pagination

## 📦️ Dependency

* [loader `app/components/partials/loader`](../loader)

## ⚙️ Configuration

To make pagination work, follow the steps below:

- add `data-component="pagination"` to the wrapper of the component
- add `data-pagination-items` to the direct parent of the items that will be dynamically loaded, they will be added into it
- add `data-pagination-item` to the items that will be dynamically loaded
- include the previous and next pagination buttons with `pagination-btn.twig`

## 🥖 Breadcrumbs

The pagination handles the breadcrumbs update. To make it work, the breadcrumbs must be contained within the component. As it is often within the header, you may have to use the following code to add the pagination component to the `.page_container` :

```php
{% block page_component %}pagination{% endblock %}
```

## 🧑‍💻 Examples

### Basic example

```php
{% include 'pagination-btn.twig' with {
	data: posts.pagination.prev,
	action: 'previous',
	txt: 'Load previous items',
} %}

<div class="post_type_list--items" data-pagination-items>
	{% for post_type in posts %}
		{% include 'post_type-card.twig' with {
			post_type_id: post_type.id,
			data_attr: 'data-pagination-item',
		} %}
	{% endfor %}
</div>

{% include 'pagination-btn.twig' with {
	data: posts.pagination.next,
	action: 'next',
	txt: 'Load more items'
} %}
</div>
```

### Advanced example

```php
{% block page_component %}pagination{% endblock %}

{% block page_content %}

	<div class="post_type_list">

		{% if posts is not empty %}

			{% include 'pagination-btn.twig' with {
				data: posts.pagination.prev,
				action: 'previous',
				txt: i18n.post_type.previous,
			} %}

			<div class="post_type_list--items" data-pagination-items>
				{% for post_type in posts %}
					{% include 'post_type-card.twig' with {
						post_type_id: post_type.id,
						data_attr: 'data-pagination-item',
					} %}
				{% endfor %}
			</div>

			{% include 'pagination-btn.twig' with {
				data: posts.pagination.next,
				action: 'next',
				txt: i18n.post_type.next
			} %}

		{% else %}

			<div class="post_type_list--no_result">{{ i18n.post_type.no_results }}</div>

		{% endif %}

	</div>

{% block page_content %}
```
