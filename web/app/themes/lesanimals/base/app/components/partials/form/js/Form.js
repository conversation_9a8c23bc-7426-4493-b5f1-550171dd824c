import AbstractView from 'abstracts/AbstractView';
import Config from 'configs/Config';
import Dom from 'utils/Dom';

export default class Form extends AbstractView {
	/**
	 * @type {string} Form id
	 */
	#id;

	/**
	 * @type {boolean} Is form loading?
	 */
	#isLoading = false;

	/**
	 * @type {boolean} Is the form sent?
	 */
	#isFormSent = false;

	/**
	 * @type {HTMLElement} $form
	 */
	#$form;

	/**
	 * @type {HTMLElement} $fields
	 */
	#$fields;

	/**
	 * @type {NodeList} $field
	 */
	#$$field;

	/**
	 * @type {NodeList} $input
	 */
	#$$input;

	/**
	 * @type {NodeList} $errors
	 */
	#$$errors;

	/**
	 * @type {NodeList} $inputFile
	 */
	#$$inputFile;

	/**
	 * @type {NodeList} $inputFileName
	 */
	#$$inputFileName;

	/**
	 * @type {HTMLElement} $nonce
	 */
	#$nonce;

	/**
	 * @type {HTMLElement} $feedback
	 */
	#$feedback;

	constructor( $wrapper ) {
		super( $wrapper );

		this.#id = this.$wrapper.getAttribute( 'data-form-id' );

		this.init();
	}

	initDOM() {
		this.#$form = this.$wrapper.tagName === 'FORM' ? this.$wrapper : this.$wrapper.querySelector( 'form.form' );
		this.#$fields = this.#$form.querySelector( '.form--fields' );
		this.#$$field = this.#$fields.querySelectorAll( '.form--field' );
		this.#$$input = this.#$fields.querySelectorAll( '.form--input' );
		this.#$$errors = this.#$fields.querySelectorAll( '.form--field-error' );
		this.#$$inputFile = this.#$fields.querySelectorAll( '.form--input__file' );
		this.#$$inputFileName = this.#$fields.querySelectorAll( '.form--field-input__file-name' );
		this.#$nonce = this.#$fields.querySelector( `#form__${ this.#id }--security` );
		this.#$feedback = this.#$form.querySelector( '.form--feedback' );
	}

	bindEvents() {
		// File change
		this.eh.fileChange = this.#onFileChange.bind( this );
		this.#$$inputFile.forEach( ( $el ) => $el.addEventListener( 'change', this.eh.fileChange ) );

		// Form submit
		this.eh.formSubmit = this.#onFormSubmit.bind( this );
		this.#$form.addEventListener( 'submit', this.eh.formSubmit );
	}

	unbindEvents() {
		this.#$$inputFile.forEach( ( $el ) => $el.removeEventListener( 'change', this.eh.fileChange ) );
		this.#$form.removeEventListener( 'submit', this.eh.formSubmit );
	}

	#onFileChange( e ) {
		const $inputFileName = Dom.next( e.currentTarget, '.form--field-input__file-name' );
		$inputFileName.innerHTML = e.currentTarget.files[ 0 ].name;
	}

	#onFormSubmit( e ) {
		e.preventDefault();

		if ( this.#isLoading || this.#isFormSent ) {
			return false;
		}

		this.#isLoading = true;

		this.#$form.classList.add( 'is_loading' );

		const data = new FormData();

		// Some data to transmit
		data.append( 'action', `${ this.#id }_form_handler` );
		data.append( 'form--security', this.#$nonce.value );
		data.append( 'data-post-title', this.#$form.getAttribute( 'data-post-title' ) );

		// Populate data with the fields values
		this.#$$input.forEach( ( $el ) => {
			if ( ( $el.type !== 'radio' && $el.type !== 'checkbox' && $el.type !== 'file' ) ||
				( $el.type === 'radio' && $el.checked ) ||
				( $el.type === 'checkbox' && $el.checked ) ) {
				data.append( $el.getAttribute( 'name' ), $el.value );
			} else if ( $el.type === 'file' && $el.files.length ) {
				data.append( $el.getAttribute( 'name' ), $el.files[ 0 ] );
			}
		} );

		// Request
		fetch( Config.AJAX_URL, { method: 'POST', body: data } )
			.then( ( response ) => response.json() )
			.then( ( returnedData ) => this.#onFormLoad( returnedData ) )
			.catch( ( error ) => this.#onFormLoadError( error ) );
	}

	#onFormLoad( data ) {
		this.#showFeedback( data );
	}

	#onFormLoadError( error ) {
		this.#showFeedback( {
			status: 'error',
			fields: {},
			messages: [ 'Unknown error...' ],
		} );

		console.error( 'Form load error — ', error ); // eslint-disable-line no-console
	}

	#showFeedback( response ) {
		this.#isLoading = false;

		this.#$$errors.forEach( ( $el ) => $el.innerHTML = '' );

		this.gsap.form = gsap.timeline()
			.to( this.#$feedback, { duration: 0.3, opacity: 0, ease: 'power3.inOut' } )
			.add( () => {
				this.#$feedback.innerHTML = response.data.messages;

				if ( response.success === true ) {
					this.#isFormSent = true;
					this.#$feedback.classList.remove( 'error' );
					this.#$form.classList.add( 'success' );
					this.#$form.reset();
					this.#$$inputFileName.forEach( ( $el ) => {
						$el.innerHTML = $el.getAttribute( 'data-placeholder' );
					} );
					this.#$form.inert = true;
				} else {
					this.#$feedback.classList.add( 'error' );
					this.#$form.classList.remove( 'success' );

					if ( response.data.fields ) {
						for ( const fieldName in response.data.fields ) {
							this.#$fields.querySelector( `.form--field-${ fieldName } .form--field-error` ).innerHTML = response.data.fields[ fieldName ];
						}
					}
				}
				this.#$form.classList.remove( 'is_loading' );
			} )
			.to( this.#$feedback, { duration: 0.3, opacity: 1, clearProps: 'all', ease: 'power3.inOut' } );
	}

	reset() {
		if ( ! this.#isLoading ) {
			this.#isFormSent = false;
			this.#$form.inert = false;
			this.#$form.reset();
			this.#$form.classList.remove( 'success' );
			this.#$form.classList.remove( 'is_loading' );
			this.#$$field.forEach( ( $el ) => $el.classList.remove( 'form--field__focus' ) );
			this.#$$inputFileName.forEach( ( $el ) => $el.innerHTML = '' );
			this.#$feedback.innerHTML = '';
			this.#$$errors.forEach( ( $el ) => $el.innerHTML = '' );
		}
	}
}
