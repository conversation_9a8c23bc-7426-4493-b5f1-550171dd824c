<?php
/**
 * Context for Submenu
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Partials\Submenu;

class Context {

	/**
	 * Class construct
	 */
	public function __construct() {
		add_filter( 'timber/context', array( $this, 'setup_context' ) );
	}

	/**
	 * Add variables to Timber Context
	 *
	 * @param array $context Timber context
	 * @return array
	 */
	public function setup_context( array $context ): array {
		$context[ 'submenu' ] = $this->get_submenu();

		return $context;
	}

	/**
	 * Get the submenu
	 *
	 * @return array|bool
	 */
	private function get_submenu(): array|bool {
		global $post;

		if ( empty( $post ) ) {
			return array();
		}

		$parent = $post;

		if ( $post->post_parent !== 0 ) {
			$parent = get_post( $post->post_parent );
		}

		$children = get_pages( array(
			'parent'      => $parent->ID,
			'sort_column' => 'menu_order',
			'sort_order'  => 'ASC',
		) );

		$items = array();
		foreach ( $children as $child ) {
			if ( get_the_ID() === $child->ID ) {
				$child->is_active = true;
			}

			// $child_submenu = get_field( 'submenu', $child->ID );

			array_push( $items, array(
				'title'     => $child->post_title,
				'link'      => get_permalink( $child->ID ),
				'is_active' => $child->is_active,
			) );
		}

		if ( count( $items ) > 1 ) {
			$submenu = array(
				'items' => $items,
			);
		} else {
			$submenu = false;
		}

		return $submenu;
	}
}

new Context();
