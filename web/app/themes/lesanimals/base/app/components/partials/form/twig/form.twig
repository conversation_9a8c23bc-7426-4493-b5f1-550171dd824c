{% set form = _context[ id ~ '_form' ] %}

<form action="{{ site.url }}" enctype="multipart/form-data" method="post" novalidate class="form {{ class }}"{% if not disableComponent %} data-component="form"{% endif %} data-form-id="{{ id }}" id="{{ id }}_form" data-post-title="{{ form.params.post_title }}">

	<div class="form--fields">
		{% for field_name, field in form.fields | filter( field => field.active ) %}
			<div class="form--field form--field-{{ field_name }} form--field-type-{{ field.type }}">

				{% if field.type != 'hidden' and field.type != 'checkbox' and field.type != 'radio' %}
					<label class="form--label"
						   for="form__{{ id }}--field-{{ field_name }}">{{ field.label }}{% if field.required %} <span class="required">*</span>{% endif %}</label>
				{% endif %}

				{% if field.type == 'text' or field.type == 'tel' or field.type == 'email' %}
					<input class="form--input form--input__text"
						   id="form__{{ id }}--field-{{ field_name }}" name="{{ field_name }}"
						   type="{{ field.type }}"
						   placeholder="{{ field.placeholder }}">

				{% elseif field.type == 'textarea' %}
					<textarea class="form--input form--input__textarea" id="form__{{ id }}--field-{{ field_name }}" name="{{ field_name }}" placeholder="{{ field.placeholder }}"></textarea>

				{% elseif field.type == 'checkbox' %}
					<input class="form--input form--input__checkbox" id="form__{{ id }}--field-{{ field_name }}" name="{{ field_name }}"
						   type="{{ field.type }}" value="1">
					<label class="form--label"
						   for="form__{{ id }}--field-{{ field_name }}">{{ field.label }}{% if field.required %} <span class="required">*</span>{% endif %}</label>

				{% elseif field.type == 'select' %}
					<select class="form--input form--input__select"
							id="form__{{ id }}--field-{{ field_name }}" name="{{ field_name }}">
						{% for value, option in field.options %}
							<option value="{{ value }}">{{ option }}</option>
						{% endfor %}
					</select>

				{% elseif field.type == 'radio' %}
					<div class="form--label">{{ field.label }}{% if field.required %} <span class="required">*</span>{% endif %}</div>
					<div class="form--field-type-radio_wrapper">
						{% for value, option in field.options %}
							<div class="form--input__radio_wrapper">
								<input class="form--input form--input__radio"
									   id="form__{{ id }}--field-{{ field_name }}__{{ value }}" name="{{ field_name }}"
									   type="{{ field.type }}" value="{{ value }}">
								<label class="form--label"
									   for="form__{{ id }}--field-{{ field_name }}__{{ value }}">{{ option }}</label>
							</div>
						{% endfor %}
					</div>

				{% elseif field.type == 'file' %}
					<div class="form_field_input_file__wrapper">
						<input class="form--input form--input__file" type="{{ field.type }}"
							   id="form__{{ id }}--field-{{ field_name }}" name="{{ field_name }}">
						<div class="form--field-input__file-name" data-placeholder="{{ field.placeholder }}">{{ field.placeholder }}</div>
						<div class="form--field-input__file-icon">{{ svg( 'file' ) }}</div>
					</div>

				{% elseif field.type == 'hidden' %}
					<input class="form--input form--input__hidden"
						   id="form__{{ id }}--field-{{ field_name }}" name="{{ field_name }}"
						   type="{{ field.type }}"
						   {% if field.value_id %}value="{{ field.value_id }}"{% endif %}>
				{% endif %}

				<span class="form--field-error"></span>
			</div>
		{% endfor %}

		{{ function( 'wp_nonce_field', 'form--nonce', 'form__' ~ id ~ '--security', false, false ) }}

	</div>

	<div class="form--submit_wrapper">
		<button class="form--submit button" type="submit">{{ form.texts.submit }}</button>
		{% include 'loader.twig' with {
			class: 'form--loader',
		} %}
	</div>
	<div class="form--feedback"></div>

</form>
