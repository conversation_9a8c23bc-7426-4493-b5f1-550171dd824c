<?php
/**
 * Initialize form
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Partials\Form;

class Init {

	/**
	 * Constructor
	 */
	public function __construct() {
		add_action( 'init', array( $this, 'init_form' ) );
	}

	/**
	 * Initialize the forms
	 *
	 * @return void
	 */
	public function init_form(): void {
		$utils = Utils::get_instance();
		$form_params = $utils->get_form_params();

		foreach ( $form_params as $params ) {
			$params[ 'prefix' ] = $params[ 'id' ] . '_form_';
			new Post_Type( $params );
			new ACF( $params );
			new Context( $params );
			new Ajax( $params );
		}
	}
}

new Init();
