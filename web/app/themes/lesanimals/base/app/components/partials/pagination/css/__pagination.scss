// ****************************
// -------- Pagination --------
// ****************************


.pagination {
	display: flex;
	justify-content: center;
	position: relative;

	&.hide {
		display: none;
	}
}

.pagination__prev {
	margin-top: 100px;
	margin-bottom: 150px;
}

.pagination__next {
	margin-top: 150px;
}


.pagination--cta {

	&::before {
		transition: opacity $ease_default;
	}

	&.loading {
		cursor: default;
		pointer-events: none;
		color: $color_highlight;
		background-color: $color_highlight !important;
		transition:
			color 0.3s $ease_out_quad,
			border-color 0.3s $ease_out_quad,
			background 0.3s $ease_out_quad !important;

		&::before,
		&::after {
			opacity: 0;
			transition: opacity 0.2s $ease_out_quad;
		}

		+ .pagination--loader {

			@extend .loader_loading;
		}
	}
}


// -------- Items --------

[data-pagination-item] {
	transition: opacity $ease_default;

	&.init {
		opacity: 0;
	}
}


// -------- Responsive --------

@include query($until: 960px) {

	.pagination__prev {
		margin-bottom: 100px;
	}

	.pagination__next {
		margin-top: 100px;
	}

}

@include query($until: 640px) {

	.pagination__prev {
		margin-bottom: 50px;
	}

	.pagination__next {
		margin-top: 50px;
	}

}

