<?php
/**
 * ACF for Form
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Partials\Form;

use Timber;

class ACF {

	private array $params;
	private array $admin_fields;
	private Utils $utils;

	/**
	 * Constructor
	 *
	 * @param array $params Form parameters
	 */
	public function __construct( array $params ) {
		$this->params = $params;
		$this->utils = Utils::get_instance();

		$this->init_post_type();

		if ( is_admin() && ! LESANIMALS_IS_ADMINISTRATOR ) {
			return;
		}

		$this->init_option_page();
	}

	/**
	 * Initialize ACF for this form
	 *
	 * @return void
	 */
	public function init_option_page(): void {
		$config = $this->utils->get_config( false, $this->params );

		$this->utils->ensure_has_key( $config, 'texts' );
		$this->utils->ensure_has_key( $config, 'fields' );
		$this->utils->ensure_has_key( $config, 'email_admin' );
		$this->utils->ensure_has_key( $config, 'email_user' );
		$this->utils->ensure_has_key( $config, 'email_colors' );

		$parent_slug = ! empty( $this->params[ 'parent_slug' ] ) ? $this->params[ 'parent_slug' ] : ( 'edit.php?post_type=' . $this->params[ 'id' ] . '_form' );

		// Add an option page
		acf_add_options_page( array(
			'page_title'  => 'Paramètres',
			'parent_slug' => $parent_slug,
			'post_id'     => $this->params[ 'prefix' ],
			'menu_slug'   => $this->params[ 'prefix' ] . 'parameters',
			'autoload'    => true,
		) );

		// Create a group for ACF
		$this->admin_fields = array();

		// File
		if ( $this->params[ 'file_to_download' ] ) {
			$this->admin_fields[] = array(
				'key'   => 'field_' . $this->params[ 'id' ] . '-form-file-tab',
				'label' => $this->params[ 'file_to_download' ][ 'tab_title' ],
				'type'  => 'tab',
			);
			$this->admin_fields[] = array(
				'key'           => 'field_' . $this->params[ 'prefix' ] . 'message_file',
				'label'         => 'Fichier joint :',
				'name'          => $this->params[ 'prefix' ] . 'message_file',
				'type'          => 'file',
				'return_format' => 'id',
			);
		}

		// Tab fields
		$this->set_texts_tab_fields( $config );
		$this->set_fields_tab_fields( $config );

		$email_admin_params = array(
			'id'      => 'admin',
			'label'   => 'Email administrateur',
			'content' => "Bonjour,\n\nUn internaute a laissé un message sur le formulaire de contact de votre site internet.\nVoici son contenu :",
		);
		$this->set_email_tab_fields( $config, $email_admin_params );
		$email_user_params = array(
			'id'      => 'user',
			'label'   => 'Email utilisateur',
			'content' => "Bonjour,\n\nMerci pour votre message.\n\nNous en accusons bonne réception et vous recontacterons dans les plus brefs délais.\n\nL'équipe Les Animals.",
		);
		$this->set_email_tab_fields( $config, $email_user_params );
		$this->set_email_colors_tab_fields( $config );

		acf_add_local_field_group(
			array(
				'key'      => 'group_' . $this->params[ 'id' ] . '_form',
				'title'    => $this->params[ 'title' ],
				'fields'   => $this->admin_fields,
				'location' => array(
					array(
						array(
							'param'    => 'options_page',
							'operator' => '==',
							'value'    => $this->params[ 'prefix' ] . 'parameters',
						),
					),
				),
			)
		);
	}

	/**
	 * Set all the fields for the texts tab
	 *
	 * @param array $config The form config
	 * @return void
	 */
	public function set_texts_tab_fields( array $config ): void {
		$this->admin_fields[] = array(
			'key'   => 'field_' . $this->params[ 'id' ] . '-form-texts-tab',
			'label' => 'Textes',
			'type'  => 'tab',
		);
		foreach ( $config[ 'texts' ] as $text_slug => $text ) {
			$this->admin_fields[] = array(
				'key'           => 'field_' . $this->params[ 'prefix' ] . 'texts_' . $text_slug,
				'label'         => $text[ 'label' ],
				'name'          => $this->params[ 'prefix' ] . 'texts_' . $text_slug,
				'type'          => 'text',
				'required'      => $text[ 'required' ] ?? false,
				'default_value' => array_key_exists( 'default', $text ) ? $text[ 'default' ] : '',
			);
		}
	}

	/**
	 * Set all the fields for the fields config tab
	 *
	 * @param array $config The form config
	 * @return void
	 */
	public function set_fields_tab_fields( array $config ): void {
		$this->admin_fields[] = array(
			'key'   => 'field_' . $this->params[ 'id' ] . '-form-fields-tab',
			'label' => 'Champs',
			'type'  => 'tab',
		);

		// Loop through each fields in the config
		foreach ( $config[ 'fields' ] as $field_name => $field ) {

			if ( $field[ 'type' ] === 'hidden' ) {
				continue;
			}

			$subfields = array();
			$mandatory = isset( $field[ 'mandatory' ] ) ?? false;
			$placeholder = isset( $field[ 'placeholder' ] ) ?? false;

			if ( ! $mandatory ) {
				$subfields[] = array(
					'key'           => 'field_' . $field_name . '_active',
					'label'         => 'Activer le champ',
					'name'          => 'active',
					'type'          => 'true_false',
					'default_value' => 1,
				);
			}

			if ( $field[ 'type' ] === 'text' || $field[ 'type' ] === 'tel' || $field[ 'type' ] === 'email' || $field[ 'type' ] === 'textarea' || $field[ 'type' ] === 'checkbox' || $field[ 'type' ] === 'file' ) {
				$subfield = array(
					'key'           => 'field_' . $field_name . '_label',
					'label'         => 'Label',
					'name'          => 'label',
					'type'          => 'text',
					'default_value' => $field[ 'label' ],
				);
				if ( ! $mandatory ) {
					$subfield[ 'conditional_logic' ] = array(
						array(
							array(
								'field'    => 'field_' . $field_name . '_active',
								'operator' => '==',
								'value'    => '1',
							),
						),
					);
				}
				$subfields[] = $subfield;
			} elseif ( $field[ 'type' ] === 'select' || $field[ 'type' ] === 'radio' ) {
				$subfield = array(
					'key'           => 'field_' . $field_name . '_label',
					'label'         => 'Label',
					'name'          => 'label',
					'type'          => 'text',
					'default_value' => $field[ 'label' ],
				);
				if ( ! $mandatory ) {
					$subfield[ 'conditional_logic' ] = array(
						array(
							array(
								'field'    => 'field_' . $field_name . '_active',
								'operator' => '==',
								'value'    => '1',
							),
						),
					);
				}
				$subfields[] = $subfield;

				if ( empty( $field[ 'options' ] ) ) {
					$subfield = array(
						'key'       => 'field_' . $field_name . '_options',
						'label'     => 'Options',
						'name'      => 'options',
						'type'      => 'textarea',
						'new_lines' => '',
					);
					if ( ! $mandatory ) {
						$subfield[ 'conditional_logic' ] = array(
							array(
								array(
									'field'    => 'field_' . $field_name . '_active',
									'operator' => '==',
									'value'    => '1',
								),
							),
						);
					}
				} else {
					$options_message = '<ul>';
					foreach ( $field[ 'options' ] as $value ) {
						$options_message .= '<li>' . $value . '</li>';
					}
					$options_message .= '</ul>';
					$subfield = array(
						'key'     => 'field_' . $field_name . '_options_message',
						'label'   => 'Options',
						'name'    => '',
						'type'    => 'message',
						'message' => $options_message,
					);
				}
				$subfields[] = $subfield;
			}

			$subfield = array(
				'key'   => 'field_' . $field_name . '_required',
				'label' => 'Champ obligatoire',
				'name'  => 'required',
				'type'  => 'true_false',
			);
			if ( ! $mandatory ) {
				$subfield[ 'conditional_logic' ] = array(
					array(
						array(
							'field'    => 'field_' . $field_name . '_active',
							'operator' => '==',
							'value'    => '1',
						),
					),
				);
			}
			$subfields[] = $subfield;

			if ( $placeholder ) {
				$subfields[] = array(
					'key'   => 'field_' . $field_name . '_placeholder',
					'label' => 'Placeholder',
					'name'  => 'placeholder',
					'type'  => 'text',
				);
			}

			foreach ( $field[ 'errors' ] as $error_slug => $error_description ) {
				$subfield = array(
					'key'           => 'field_' . $field_name . '_error_' . $error_slug,
					'label'         => 'Erreur : <br>' . $error_description,
					'name'          => 'error_' . $error_slug,
					'type'          => 'text',
					'default_value' => $error_description,
				);
				if ( $error_slug === 'empty' ) {
					$subfield[ 'conditional_logic' ] = array(
						array(
							array(
								'field'    => 'field_' . $field_name . '_required',
								'operator' => '==',
								'value'    => '1',
							),
						),
					);
				}
				$subfields[] = $subfield;
			}

			// Append the field to the array of admin fields
			// Add a hook
			$this->admin_fields[] = apply_filters( 'lesanimals_form_admin_field_admin_email', array(
				'key'        => 'field_' . $this->params[ 'prefix' ] . 'field_' . $field_name,
				'label'      => $field[ 'label' ],
				'name'       => $this->params[ 'prefix' ] . 'field_' . $field_name,
				'type'       => 'group',
				'layout'     => 'row',
				'sub_fields' => $subfields,
			), $this->params, $config );
		}
	}

	/**
	 * Set all the fields for the email tab
	 *
	 * @param array $config     The form config
	 * @param array $tab_params The tab params
	 * @return void
	 */
	public function set_email_tab_fields( array $config, array $tab_params ): void {
		$tab_id = $tab_params[ 'id' ];

		$this->admin_fields[] = array(
			'key'   => 'field_' . $this->params[ 'id' ] . '-form-email-' . $tab_id . '-tab',
			'label' => $tab_params[ 'label' ],
			'type'  => 'tab',
		);

		foreach ( $config[ 'email_' . $tab_id ] as $email_slug => $email ) {

			if ( $email_slug === 'active' ) {
				$params = array(
					'key'     => 'field_' . $this->params[ 'prefix' ] . 'email_' . $tab_id . '_' . $email_slug,
					'label'   => '',
					'name'    => $this->params[ 'prefix' ] . 'email_' . $tab_id . '_active',
					'type'    => 'checkbox',
					'choices' => array(
						'active' => $email[ 'label' ],
					),
				);

				// Filter before setting the first field, after the tab and the active checkbox
				$this->admin_fields = apply_filters( 'lesanimals_form_admin_fields_before_' . $tab_id . '_email', $this->admin_fields, $this->params );
			} elseif ( $email_slug === 'header' ) {
				$params = array(
					'key'               => 'field_' . $this->params[ 'prefix' ] . 'email_' . $tab_id . '_' . $email_slug,
					'label'             => $email[ 'label' ],
					'name'              => $this->params[ 'prefix' ] . 'email_' . $tab_id . '_' . $email_slug,
					'type'              => 'image',
					'instructions'      => '<i>Taille d\'image minimum recommandée&nbsp;: 1200&nbsp;x&nbsp;450&nbsp;px.</i>',
					'required'          => $email[ 'required' ] ?? false,
					'conditional_logic' => array(
						array(
							array(
								'field'    => 'field_' . $this->params[ 'prefix' ] . 'email_' . $tab_id . '_active',
								'operator' => '==',
								'value'    => 'active',
							),
						),
					),
				);
			} elseif ( $email_slug === 'content' ) {
				$params = array(
					'key'               => 'field_' . $this->params[ 'prefix' ] . 'email_' . $tab_id . '_' . $email_slug,
					'label'             => $email[ 'label' ],
					'name'              => $this->params[ 'prefix' ] . 'email_' . $tab_id . '_' . $email_slug,
					'type'              => 'textarea',
					'default_value'     => $tab_params[ 'content' ],
					'new_lines'         => 'br',
					'required'          => $email[ 'required' ] ?? false,
					'conditional_logic' => array(
						array(
							array(
								'field'    => 'field_' . $this->params[ 'prefix' ] . 'email_' . $tab_id . '_active',
								'operator' => '==',
								'value'    => 'active',
							),
						),
					),
				);
			} else {
				$params = array(
					'key'               => 'field_' . $this->params[ 'prefix' ] . 'email_' . $tab_id . '_' . $email_slug,
					'label'             => $email[ 'label' ],
					'name'              => $this->params[ 'prefix' ] . 'email_' . $tab_id . '_' . $email_slug,
					'type'              => 'text',
					'required'          => $email[ 'required' ] ?? false,
					'conditional_logic' => array(
						array(
							array(
								'field'    => 'field_' . $this->params[ 'prefix' ] . 'email_' . $tab_id . '_active',
								'operator' => '==',
								'value'    => 'active',
							),
						),
					),
				);
			}

			// Hook the checkbox field param
			$this->admin_fields[] = apply_filters( 'lesanimals_form_admin_field_' . $tab_id . '_email', $params, $this->params );
		}

		$this->admin_fields = apply_filters( 'lesanimals_form_admin_fields_after_' . $tab_id . '_email', $this->admin_fields, $this->params );
	}

	/**
	 * Set all the fields for the email colors tab
	 *
	 * @param array $config The form config
	 * @return void
	 */
	public function set_email_colors_tab_fields( array $config ): void {
		$this->admin_fields[] = array(
			'key'   => 'field_' . $this->params[ 'id' ] . '-form-colors-tab',
			'label' => 'Couleurs emails',
			'type'  => 'tab',
		);

		$this->admin_fields = apply_filters( 'lesanimals_form_admin_fields_before_email_colors', $this->admin_fields, $this->params );

		$default_values = array(
			'bg'             => '#000000',
			'content_bg'     => '#ffffff',
			'txt'            => '#000000',
			'data'           => '#2c2ce7',
			'data_separator' => '#cccccc',
			'footer_bg'      => '#2c2ce7',
			'footer'         => '#ffffff',
		);
		foreach ( $config[ 'email_colors' ] as $email_slug => $email ) {
			$params = array(
				'key'           => 'field_' . $this->params[ 'prefix' ] . 'email_colors_' . $email_slug,
				'label'         => $email[ 'label' ],
				'name'          => $this->params[ 'prefix' ] . 'email_colors_' . $email_slug,
				'type'          => 'color_picker',
				'default_value' => $default_values[ $email_slug ],
				'required'      => true,
			);
			$this->admin_fields[] = apply_filters( 'lesanimals_form_admin_field_email_colors', $params, $this->params );
		}

		$this->admin_fields = apply_filters( 'lesanimals_form_admin_fields_after_email_colors', $this->admin_fields, $this->params );
	}

	/**
	 * Creates the associated post type
	 *
	 * @return void
	 */
	public function init_post_type(): void {
		$config_with_texts = $this->utils->get_config( true, $this->params );

		// Adds to the post type
		$post_type_locations = array(
			array(
				array(
					'param'    => 'post_type',
					'operator' => '==',
					'value'    => $this->params[ 'id' ] . '_form',
				),
			),
		);

		$post_type_fields = array();

		foreach ( $config_with_texts[ 'fields' ] as $field_name => $field ) {
			if ( ! $field[ 'active' ] ) {
				continue;
			}

			if ( $field[ 'type' ] === 'hidden' || $field[ 'type' ] === 'tel' ) {
				$post_type_fields[] = array(
					'key'   => 'field_' . $field_name,
					'label' => $field[ 'label' ],
					'name'  => $field_name,
					'type'  => 'text',
				);
			} elseif ( $field[ 'type' ] === 'select' || $field[ 'type' ] === 'radio' ) {
				$field_options = ! empty( $config_with_texts[ 'fields' ][ $field_name ][ 'options' ] ) ? $config_with_texts[ 'fields' ][ $field_name ][ 'options' ] : $field[ 'options' ];
				$post_type_fields[] = array(
					'key'     => 'field_' . $field_name,
					'label'   => $field[ 'label' ],
					'name'    => $field_name,
					'type'    => $field[ 'type' ],
					'choices' => $field_options,
				);
			} elseif ( $field[ 'type' ] === 'checkbox' ) {
				$post_type_fields[] = array(
					'key'     => 'field_' . $field_name,
					'label'   => false,
					'name'    => $field_name,
					'type'    => $field[ 'type' ],
					'choices' => array(
						1 => $field[ 'label' ],
					),
				);
			} elseif ( $field[ 'type' ] === 'file' ) {
				$post_type_fields[] = array(
					'key'     => 'field_' . $field_name,
					'label'   => $field[ 'label' ],
					'name'    => $field_name,
					'type'    => 'message',
					'message' => '<a href="' . wp_get_attachment_url( get_field( $field_name, acfe_get_post_id() ) ) . '" target="_blank">Voir le fichier</a>',
				);
			} else {
				$post_type_fields[] = array(
					'disabled' => array(
						1 => true,
					),
					'key'      => 'field_' . $field_name,
					'label'    => $field[ 'label' ],
					'name'     => $field_name,
					'type'     => $field[ 'type' ],
				);
			}
		}

		// Read-only everything
		foreach ( $post_type_fields as $slug => $post_type_field ) {
			if ( ! array_key_exists( 'disabled', $post_type_field ) ) {
				$post_type_fields[ $slug ][ 'disabled' ] = array(
					1 => true,
				);
			}
			if ( ! array_key_exists( 'readonly', $post_type_field ) ) {
				$post_type_fields[ $slug ][ 'readonly' ] = true;
			}
		}

		acf_add_local_field_group(
			array(
				'key'            => 'group_' . $this->params[ 'id' ] . '_post_type',
				'title'          => 'Messages',
				'fields'         => $post_type_fields,
				'location'       => $post_type_locations,
				'hide_on_screen' => array( 'the_content' ),
			)
		);
	}
}
