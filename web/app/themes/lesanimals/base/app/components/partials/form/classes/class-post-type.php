<?php
/**
 * Post type form
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Partials\Form;

class Post_Type {

	private array $params;

	/**
	 * Constructor
	 *
	 * @param array $params Form params
	 */
	public function __construct( array $params ) {
		$this->params = $params;

		$this->init_post_type();
	}

	/**
	 * Create the form post type
	 *
	 * @return void
	 */
	public function init_post_type(): void {
		// Sets the parent slug automatically or from the config
		$parent_slug = ! empty( $this->params[ 'parent_slug' ] ) ? $this->params[ 'parent_slug' ] : true;

		$labels = array_merge( array(
			'name'                  => _x( 'Contacts', 'Post Type General Name', 'lesanimals' ),
			'singular_name'         => _x( 'Contact', 'Post Type Singular Name', 'lesanimals' ),
			'menu_name'             => __( 'Contact', 'lesanimals' ),
			'name_admin_bar'        => __( 'Contacts', 'lesanimals' ),
			'archives'              => __( 'Contacts', 'lesanimals' ),
			'attributes'            => __( 'Attributs', 'lesanimals' ),
			'parent_item_colon'     => __( 'Parent :', 'lesanimals' ),
			'all_items'             => __( 'Tous les contacts', 'lesanimals' ),
			'add_new_item'          => __( 'Ajouter', 'lesanimals' ),
			'add_new'               => __( 'Ajouter', 'lesanimals' ),
			'new_item'              => __( 'Nouveau', 'lesanimals' ),
			'edit_item'             => __( 'Éditer', 'lesanimals' ),
			'update_item'           => __( 'Mettre à jour', 'lesanimals' ),
			'view_item'             => __( 'Voir', 'lesanimals' ),
			'view_items'            => __( 'Voir tous', 'lesanimals' ),
			'search_items'          => __( 'Rechercher', 'lesanimals' ),
			'not_found'             => __( 'Aucun résultat', 'lesanimals' ),
			'not_found_in_trash'    => __( 'Aucun résultat dans la corbeille', 'lesanimals' ),
			'featured_image'        => __( 'Portrait', 'lesanimals' ),
			'set_featured_image'    => __( 'Ajouter le portrait', 'lesanimals' ),
			'remove_featured_image' => __( 'Supprimer le portrait', 'lesanimals' ),
			'use_featured_image'    => __( 'Utiliser comme portrait', 'lesanimals' ),
			'insert_into_item'      => __( 'Associer', 'lesanimals' ),
			'uploaded_to_this_item' => __( 'Mis en ligne pour ce contenu', 'lesanimals' ),
			'items_list'            => __( 'Liste', 'lesanimals' ),
			'items_list_navigation' => __( 'Liste de navigation', 'lesanimals' ),
			'filter_items_list'     => __( 'Filtrer la liste', 'lesanimals' ),
		), $this->params[ 'labels' ] );
		$args = array(
			'label'               => $this->params[ 'title' ],
			'description'         => $this->params[ 'title' ],
			'labels'              => $labels,
			'supports'            => array( 'title', 'editor', 'revisions', 'custom-fields' ),
			'taxonomies'          => array(),
			'hierarchical'        => false,
			'public'              => true,
			'show_ui'             => true,
			'show_in_menu'        => $parent_slug,
			'show_in_rest'        => false,
			'menu_position'       => $this->params[ 'menu_position' ],
			'menu_icon'           => $this->params[ 'menu_icon' ],
			'show_in_admin_bar'   => false,
			'show_in_nav_menus'   => false,
			'can_export'          => true,
			'has_archive'         => false,
			'exclude_from_search' => true,
			'publicly_queryable'  => false,
			'rewrite'             => false,
			'capabilities'        => array(
				'edit_post'              => 'edit_lesanimals_form',
				'read_post'              => 'read_lesanimals_form',
				'delete_post'            => 'delete_lesanimals_form',
				'edit_posts'             => 'edit_lesanimals_forms',
				'edit_others_posts'      => 'edit_others_lesanimals_forms',
				'publish_posts'          => 'publish_lesanimals_forms',
				'read_private_posts'     => 'read_private_lesanimals_forms',
				'read'                   => 'read_lesanimals_forms',
				'delete_posts'           => 'delete_lesanimals_forms',
				'delete_private_posts'   => 'delete_private_lesanimals_forms',
				'delete_published_posts' => 'delete_published_lesanimals_forms',
				'delete_others_posts'    => 'delete_others_lesanimals_forms,',
				'edit_private_posts'     => 'edit_private_lesanimals_forms',
				'edit_published_posts'   => 'edit_published_lesanimals_forms',
				'create_posts'           => 'create_lesanimals_forms',
			),
		);
		register_post_type( $this->params[ 'id' ] . '_form', $args );
	}
}
