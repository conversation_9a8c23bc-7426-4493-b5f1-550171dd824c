<?php
/**
 * Utils for form
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Partials\Form;

class Utils {

	protected static Utils $instance;

	private static string $config_files_path;

	/**
	 * Constructor (singleton)
	 */
	protected function __construct() {
		$this->set_config_files_path();
	}

	/**
	 * Get instance for singleton
	 *
	 * @return Utils
	 */
	public static function get_instance(): Utils {
		if ( ! isset( self::$instance ) ) {
			self::$instance = new self();
		}

		return self::$instance;
	}

	/**
	 * Sets the config files path
	 *
	 * @return void
	 */
	private function set_config_files_path(): void {
		self::$config_files_path = get_stylesheet_directory() . '/app/components/partials/form/configs/';
	}

	/**
	 * Get the forms params
	 *
	 * @return array
	 */
	public function get_form_params(): array {
		$file_path_list = $this->get_file_path_list();
		$params = array();

		foreach ( $file_path_list as $file_path ) {
			$config = json_decode( file_get_contents( self::$config_files_path . '/' . $file_path ), true );
			$params[] = array_merge( $config[ 'params' ], array( 'current_file_name' => $file_path ) ); // 'current_file_name' is used for debug
		}

		return $params;
	}

	/**
	 * Get file path list
	 *
	 * @return array
	 */
	private function get_file_path_list(): array {
		$file_path_list = array();
		if ( is_dir( self::$config_files_path ) ) {
			foreach ( scandir( self::$config_files_path ) as $path ) {
				if ( stristr( $path, '.json' ) ) {
					$file_path_list[] = $path;
				}
			}
		}

		return $file_path_list;
	}

	/**
	 * Test if the param has a key
	 *
	 * @param array  $config The config params
	 * @param string $key    The key to test
	 */
	public function ensure_has_key( array $config, string $key ): bool {
		if ( empty( $config[ $key ] ) ) {
			?>
			<div class="notice notice-error">
				<p>
					<?php
					// translators: First string is the key, second is the file name
					wp_sprintf( esc_html__( 'Erreur: La  clé <b>%1$s</b> est vide dans <b>%2$s</b>.', 'lesanimals' ), array( $key, $config[ 'params' ][ 'current_file_name' ] ) );
					?>
				</p>
			</div>;
			<?php

			return false;
		}
		return true;
	}

	/**
	 * Get the config
	 *
	 * @param bool  $with_texts Get the texts from database, or only the raw config
	 * @param array $params     The form params
	 * @return array|mixed|object
	 */
	public function get_config( bool $with_texts, array $params ): mixed {

		$prefix = $params[ 'id' ] . '_form_';

		// Get the contact form config
		$config = json_decode( file_get_contents( self::$config_files_path . 'config-' . $params[ 'id' ] . '.json' ), true );
		$config[ 'params' ] = $params;

		if ( $with_texts === false ) {

			apply_filters( 'lesanimals_form_config_raw', $config );
			apply_filters( 'lesanimals_form_' . $params[ 'id' ] . '_config_raw', $config );
			return $config;
		}

		$options = get_fields( $prefix );

		// The fields
		foreach ( $config[ 'fields' ] as $field_name => $field ) {
			// Active
			$config[ 'fields' ][ $field_name ][ 'active' ] = isset( $options[ $prefix . 'field_' . $field_name ][ 'active' ] ) ? $options[ $prefix . 'field_' . $field_name ][ 'active' ] : true;
			// The label
			$config[ 'fields' ][ $field_name ][ 'label' ] = ! empty( $options[ $prefix . 'field_' . $field_name ][ 'label' ] ) ? $options[ $prefix . 'field_' . $field_name ][ 'label' ] : $config[ 'fields' ][ $field_name ][ 'label' ];
			// Required
			$config[ 'fields' ][ $field_name ][ 'required' ] = isset( $options[ $prefix . 'field_' . $field_name ][ 'required' ] ) ? $options[ $prefix . 'field_' . $field_name ][ 'required' ] : false;
			// Placeholder
			$config[ 'fields' ][ $field_name ][ 'placeholder' ] = isset( $options[ $prefix . 'field_' . $field_name ][ 'placeholder' ] ) ? $options[ $prefix . 'field_' . $field_name ][ 'placeholder' ] : '';

			// The errors
			foreach ( $config[ 'fields' ][ $field_name ][ 'errors' ] as $error_name => $error ) {
				$config[ 'fields' ][ $field_name ][ 'errors' ][ $error_name ] = ! empty( $options[ $prefix . 'field_' . $field_name ][ 'error_' . $error_name ] ) ? $options[ $prefix . 'field_' . $field_name ][ 'error_' . $error_name ] : __( 'Erreur', 'lesanimals' );
			}

			// Select & radio
			if ( $field[ 'type' ] === 'select' || $field[ 'type' ] === 'radio' ) {
				if ( empty( $field[ 'options' ] ) ) {
					$options_options = $options[ $prefix . 'field_' . $field_name ][ 'options' ] ?? '';
					$options_fields = explode( PHP_EOL, $options_options );
					$config[ 'fields' ][ $field_name ][ 'options' ] = array();

					$options_fields_count = count( $options_fields );
					foreach ( $options_fields as $option ) {
						// Use the : separator as ACF if detected
						if ( str_contains( $option, ':' ) ) {
							$parts = explode( ':', $option );
							$new_value = sanitize_key( trim( $parts[ 0 ] ) );
							$new_label = trim( $parts[ 1 ] );
							$config[ 'fields' ][ $field_name ][ 'options' ][ $new_value ] = $new_label;
						} else {
							$config[ 'fields' ][ $field_name ][ 'options' ][ sanitize_title( trim( $option ) ) ] = trim( $option );
						}
					}
				}
			}

			// Hidden
			if ( $field[ 'type' ] === 'hidden' && $config[ 'params' ][ 'file_to_download' ] && $config[ 'params' ][ 'file_to_download' ][ 'targeted_field' ] === $field_name ) {
				$options_msg_file = $options[ $prefix . 'message_file' ] ?? '';
				$file_to_download_id = $options_msg_file;
				$file_to_download_link = empty( $file_to_download_id ) ? '' : wp_get_attachment_url( $file_to_download_id );

				$config[ 'fields' ][ $field_name ][ 'value_id' ] = $file_to_download_id;
				$config[ 'fields' ][ $field_name ][ 'value' ] = $file_to_download_link;
				$config[ 'file_to_download' ] = $file_to_download_link;
			}

			// Hidden
			if ( $field[ 'type' ] === 'hidden' ) {
				$config[ 'fields' ][ $field_name ][ 'value' ] = apply_filters( 'lesanimals_form_input_hidden_value', '', $field_name, $config );
			}
		}

		// The texts
		foreach ( $config[ 'texts' ] as $text_name => $text ) {
			$config[ 'texts' ][ $text_name ] = ! empty( $options[ $prefix . 'texts_' . $text_name ] ) ? $options[ $prefix . 'texts_' . $text_name ] : '';
		}

		// The email admin
		foreach ( $config[ 'email_admin' ] as $email_param_name => $email_param ) {
			$config[ 'email_admin' ][ $email_param_name ] = ! empty( $options[ $prefix . 'email_admin_' . $email_param_name ] ) ? $options[ $prefix . 'email_admin_' . $email_param_name ] : '';
		}

		// The email user
		foreach ( $config[ 'email_user' ] as $email_param_name => $email_param ) {
			$config[ 'email_user' ][ $email_param_name ] = ! empty( $options[ $prefix . 'email_user_' . $email_param_name ] ) ? $options[ $prefix . 'email_user_' . $email_param_name ] : '';
		}

		// The email colors
		foreach ( $config[ 'email_colors' ] as $email_param_name => $email_param ) {
			$config[ 'email_colors' ][ $email_param_name ] = ! empty( $options[ $prefix . 'email_colors_' . $email_param_name ] ) ? $options[ $prefix . 'email_colors_' . $email_param_name ] : '';
		}

		$config = apply_filters( 'lesanimals_form_config', $config );
		$config = apply_filters( 'lesanimals_form_' . $params[ 'id' ] . '_config', $config );

		return $config;
	}
}
