// **********************
// -------- Form --------
// **********************

@import "textarea";
@import "select";
@import "file";
@import "buttons";
@import "submit";
@import "feedback";
@import "error";
@import "loader";


.form {
	display: flex;
	flex-direction: column;
	align-items: stretch;
	justify-content: flex-start;
	position: relative;
	margin: auto;

	&.is_loading {

		.form--field {
			pointer-events: none;
		}
	}


	// -------- Success / Not success

	&.success {

		.form--field,
		.form--submit_wrapper {
			opacity: 0.05;
			transition: opacity $ease_default;
		}

		.form--submit_wrapper {
			opacity: 0;
		}

		.form--feedback {
			position: absolute;
			bottom: 8px;
			left: 0;
			width: 100%;
			z-index: 1;
		}
	}
}


/* -------- Field -------- */

.form--fields {
}

.form--field {
	position: relative;

	&:not(.form--field-type-hidden):not(.form--field-type-checkbox) {
		margin-top: 16px;
	}
}


/* -------- Label -------- */

.form--label {

	&:not(div) {
		cursor: pointer;
	}

	display: block;
	padding: 0 16px;
	margin-bottom: 8px;
	font: var(--font_s);

	a {
		text-decoration: underline;
		transition: color $ease_default;

		@include hover {
			color: rgba($color_black, 0.5);
		}
	}
}


/* -------- Inputs - Commons -------- */

.form--input,
.form--field-input__file-name {
	background-color: $color_white;

	&:not(.form--input__radio, .form--input__checkbox) {
		display: block;
		font: var(--font_s);
		min-height: 48px;
		width: 100%;
		padding: 14px 16px 12px 16px;
		border-radius: 8px;
	}
}

.form--input__text,
.form--input__textarea {

	&::placeholder {
		color: rgba($color_highlight, 0.5);
	}
}

.form--input__text,
.form--input__select,
.form--input__textarea,
.form--field-input__file-name {
	font: var(--font_s);
	border: 1px solid rgba($color_black, 0.2);
	transition: border-color $ease_default;

	@include hover {
		border-color: rgba($color_black, 0.5);
	}

	&:focus-visible:not(:hover) {
		border-color: $color_black;
	}
}

