# 🪗 Accordion

### ⚙️ Configuration

To make accordion work, there are two options.

#### Simple option

- include `{% include 'accordion.twig' with { items: accordion_items } %}`
- populate the items in the `class-context.php` file of your page with the following model
	```php
	public function __construct() {
		add_filter( 'timber/context', array( $this, 'setup_context' ) );
	}
	
	public function setup_context( array $context ): array {
		$context[ 'accordion_items' ] = array(
			array(
				'title'    => 'Item title',
				'content'  => 'Item content',
 	        )
		);
	
		return $context;
	}
	```

#### Advanced option

- add `data-component="accordion"` to the wrapper of the component
- add `data-accordion-item` to each item that contains button and content
- add `data-accordion-btn data-accordion-index="{{ $ }}"` to each button that will open the content, $ is the index of the item
- add `data-accordion-content` to the content to be opened

This option can be usefull if you want to customize the DOM for a specific use.

### 🧑‍💻 Advanced example

The HTML in your Twig file will look like this:

```html
<div class="faq--list" data-component="accordion">
	{% for item in faq %}
	{% set item_index = ( item_index | default(-1) ) + 1 %}
	<div class="faq--item" data-accordion-item>
		<h2 class="faq--item-question">
			<button type="button" class="faq--item-btn" data-accordion-btn data-accordion-index="{{ item_index }}">
				<span class="faq--item-btn-txt">{{ item.title }}</span>
			</button>
		</h2>
		<div class="faq--item-answser" data-accordion-content>
			<div class="faq--item-answser_inner">
				<div class="faq--item-answser-content">{{ item.content }}</div>
			</div>
		</div>
	</div>
	{% endfor %}
</div>
```

The CSS in your SCSS file will look like this:

```css
.faq--item {

	@extend .accordion--item;
}

.faq--item-btn {

	@extend .accordion--item-btn;
}

.faq--item-btn-txt {

	@extend .accordion--item-btn-txt;
}

.faq--item-answser {

	@extend .accordion--item-content;
}

.faq--item-answser_inner {

	@extend .accordion--item-content_inner;
}
```
