import AbstractView from 'abstracts/AbstractView';
import <PERSON> from 'utils/Dom';

export default class Accordion extends AbstractView {
	/**
	 * @type {NodeList} $item
	 */
	#$$item;

	/**
	 * @type {NodeList} $btn
	 */
	#$$btn;

	/**
	 * @type {NodeList} $content
	 */
	#$$content;

	/**
	 * @type {number|null} Index of the current opened item
	 */
	#currentIndex = null;

	/**
	 * @param {HTMLElement} $wrapper
	 */
	constructor( $wrapper ) {
		super( $wrapper );

		this.init();
	}

	initDOM() {
		this.#$$item = this.$wrapper.querySelectorAll( '[ data-accordion-item ]' );
		this.#$$btn = this.$wrapper.querySelectorAll( '[ data-accordion-btn ]' );
		this.#$$content = this.$wrapper.querySelectorAll( '[ data-accordion-content ]' );
	}

	initEl() {
		Dom.inert( this.#$$content );
	}

	bindEvents() {
		this.eh.clickBtn = this.#handle.bind( this );
		this.#$$btn.forEach( ( $el ) => $el.addEventListener( 'click', this.eh.clickBtn ) );
	}

	unbindEvents() {
		this.#$$btn.forEach( ( $el ) => $el.removeEventListener( 'click', this.eh.clickBtn ) );
	}

	#handle( e ) {
		let index = parseInt( e.currentTarget.getAttribute( 'data-accordion-index' ) );

		if ( this.#currentIndex === null ) { // open
			this.#openItem( index );
		} else if ( this.#currentIndex === index ) { // close
			this.#closeItem( index );
			index = null;
		} else { // change
			this.#closeItem( this.#currentIndex );
			this.#openItem( index );
		}

		this.#currentIndex = index;
	}

	#openItem( index ) {
		this.#$$item[ index ].classList.add( 'open' );
		Dom.inert( this.#$$content[ index ], false );
	}

	#closeItem( index ) {
		this.#$$item[ index ].classList.remove( 'open' );
		Dom.inert( this.#$$content[ index ] );
	}
}

