<?php
/**
 * Ajax form
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\Partials\Form;

use Timber;

class Ajax {

	private array $params;
	private Utils $utils;

	/**
	 * Constructor
	 *
	 * @param array $params Form params
	 */
	public function __construct( array $params ) {
		$this->params = $params;
		$this->utils = Utils::get_instance();

		add_action( 'wp_ajax_' . $this->params[ 'id' ] . '_form_handler', array( $this, 'form_handler' ) );
		add_action( 'wp_ajax_nopriv_' . $this->params[ 'id' ] . '_form_handler', array( $this, 'form_handler' ) );
	}

	/**
	 * Handle Ajax form
	 *
	 * @return void
	 */
	public function form_handler(): void {
		// Defaults to error
		$return = array(
			'status'   => 'error',
			'fields'   => array(),
			'messages' => array(),
		);

		// Check we have a post object
		if ( empty( $_POST ) ) {
			$return[ 'messages' ][] = __( 'Aucune donnée.', 'lesanimals' );
			wp_send_json_error( $return );
		}

		// Security check
		if ( check_ajax_referer( 'form--nonce', 'form__' . $this->params[ 'id' ] . '--security', false ) ) {
			$return[ 'messages' ][] = __( 'Erreur de sécurité. Veuillez recharger la page.', 'lesanimals' );
			wp_send_json_error( $return );
		}

		// Get the config
		$config = $this->utils->get_config( true, $this->params );

		// Assume it is valid
		$valid = true;

		$files = sanitize_post( $_FILES );

		// Validation
		$sent_values = array();
		foreach ( $config[ 'fields' ] as $field_name => $field ) {

			$sent_values[ $field_name ] = array_key_exists( $field_name, $_POST ) ? filter_var( wp_unslash( $_POST[ $field_name ] ) ) : false;

			if ( $field[ 'active' ] && $field[ 'required' ] && empty( $sent_values[ $field_name ] ) && $field[ 'type' ] !== 'file' ) {
				// Generic not empty validation
				$valid = false;
				$return[ 'fields' ][ $field_name ] = $field[ 'errors' ][ 'empty' ];
			} elseif ( $field[ 'type' ] === 'file' && $field[ 'active' ] && $field[ 'required' ] && empty( $files[ $field_name ] ) ) {
				// File validation
				$valid = false;
				$return[ 'fields' ][ $field_name ] = $field[ 'errors' ][ 'empty' ];
			} elseif ( $field[ 'type' ] === 'file' && $field[ 'active' ] && ! empty( $files[ $field_name ] ) && mime_content_type( $files[ $field_name ][ 'tmp_name' ] ) !== 'application/pdf' ) {
				// File format validation
				$valid = false;
				$return[ 'fields' ][ $field_name ] = $field[ 'errors' ][ 'file_format' ];
			} elseif ( $field[ 'type' ] === 'file' && $field[ 'active' ] && ! empty( $files[ $field_name ] ) && $files[ $field_name ][ 'size' ] / ( 1024 * 1024 ) > 20 ) {
				// File size validation
				$valid = false;
				$return[ 'fields' ][ $field_name ] = $field[ 'errors' ][ 'file_size' ];
			} elseif ( $field[ 'type' ] === 'email' && $field[ 'active' ] && ! filter_var( wp_unslash( $sent_values[ $field_name ] ), FILTER_VALIDATE_EMAIL ) ) {
				// Email formatting validation
				$valid = false;
				$return[ 'fields' ][ $field_name ] = $field[ 'errors' ][ 'email' ];
			}
		}

		if ( $valid !== false ) {

			// Files handling
			if ( ! empty( $files ) ) {
				foreach ( $files as $key => $value ) {
					$upload_overrides = array( 'test_form' => false );

					add_filter( 'upload_dir', array( $this, 'custom_upload_dir' ) );
					$moved_file_id = media_handle_upload( $key, 0, array(), $upload_overrides );
					remove_filter( 'upload_dir', array( $this, 'custom_upload_dir' ) );

					if ( $moved_file_id ) {
						$_POST[ $key ] = $moved_file_id;
						$sent_values[ $key ] = wp_get_attachment_url( $moved_file_id );
					}
				}
			}

			// Send the mails
			$send_mail_admin = ! empty( $config[ 'email_admin' ][ 'active' ] );
			$send_mail_user = ! empty( $config[ 'email_user' ][ 'active' ] );

			if ( $send_mail_admin ) {
				$mail_admin = $this->send_mail_admin( $sent_values, $config );
			}
			if ( $send_mail_user ) {
				$mail_user = $this->send_mail_user( $sent_values, $config );
			}

			if ( $send_mail_admin && ! $mail_admin ) {
				$return[ 'messages' ][] = $config[ 'texts' ][ 'error' ] . ' | Admin email error.';
			} elseif ( $send_mail_user && ! $mail_user ) {
				$return[ 'messages' ][] = $config[ 'texts' ][ 'error' ] . ' | User email error.';
			} else {
				$return[ 'status' ] = 'ok';
				$return[ 'messages' ][] = $config[ 'texts' ][ 'sent' ];
				$this->save_message( $config );
			}
		} else {
			$return[ 'messages' ][] = $config[ 'texts' ][ 'error' ];
		}

		if ( $return[ 'status' ] === 'ok' ) {
			wp_send_json_success( $return );
		}
		wp_send_json_error( $return );
	}

	/**
	 * Send an email to the admin
	 *
	 * @param array $sent_values All values sent
	 * @param array $config      Email config
	 * @return bool|mixed
	 */
	private function send_mail_admin( array $sent_values, array $config ): mixed {

		// To please PHPCS
		check_ajax_referer( 'form--nonce', 'form--security' );

		$content = array(
			'fields' => array(),
		);

		foreach ( $config[ 'fields' ] as $field_name => $field ) {

			if ( ! $field[ 'active' ] ) {
				continue;
			}
			if ( $field[ 'type' ] === 'textarea' ) {
				$value = array_key_exists( $field_name, $sent_values ) ? nl2br( $sent_values[ $field_name ] ) : '';
			} elseif ( ( $field[ 'type' ] === 'select' || $field[ 'type' ] === 'radio' ) && array_key_exists( $field_name, $sent_values ) ) {
				$post_value = sanitize_key( wp_unslash( $sent_values[ $field_name ] ) );
				$value = preg_replace( '/\r|\n/', '', $field[ 'options' ][ $post_value ] );
			} elseif ( $field[ 'type' ] === 'checkbox' ) {
				$value = array_key_exists( $field_name, $sent_values ) ? __( 'Oui', 'lesanimals' ) : __( 'Non', 'lesanimals' );
			} else {
				$value = array_key_exists( $field_name, $sent_values ) ? wp_kses_post( wp_unslash( $sent_values[ $field_name ] ) ) : '';
			}

			$content[ 'fields' ][ $field_name ] = array(
				'label' => $field[ 'label' ],
				'value' => $value,
				'type'  => $field[ 'type' ],
			);
		}

		$context = Timber::context();
		$context[ 'datas' ] = $config[ 'email_admin' ];
		$context[ 'colors' ] = $config[ 'email_colors' ];
		$context[ 'content' ] = $content;
		$mail_message = Timber::compile( 'email-admin.twig', $context );

		$mail_headers = array();
		$mail_headers[] = 'Content-Type: text/html; charset=UTF-8';
		$mail_headers[] = "From: {$config['email_admin']['sender_name']} <{$config['email_admin']['sender_email']}>";

		// CC
		if ( $config[ 'email_admin' ][ 'recipient_email_cc' ] ) {
			foreach ( explode( ',', $config[ 'email_admin' ][ 'recipient_email_cc' ] ) as $email ) {
				$email = sanitize_email( trim( $email ) );
				$mail_headers[] = "CC: $email";
			}
		}

		// CCI
		if ( $config[ 'email_admin' ][ 'recipient_email_cci' ] ) {
			foreach ( explode( ',', $config[ 'email_admin' ][ 'recipient_email_cci' ] ) as $email ) {
				$email = sanitize_email( trim( $email ) );
				$mail_headers[] = "BCC: $email";
			}
		}

		$mail_recipient = apply_filters( 'lesanimals_form_email_admin_recipient', "{$config['email_admin']['recipient_name']} <{$config['email_admin']['recipient_email']}>", $config, $sent_values );
		$mail_subject = apply_filters( 'lesanimals_form_email_admin_subject', $config[ 'email_admin' ][ 'subject' ], $config, $sent_values );
		$mail_headers = apply_filters( 'lesanimals_form_email_admin_headers', $mail_headers, $config, $sent_values );

		return wp_mail( $mail_recipient, $mail_subject, $mail_message, $mail_headers );
	}

	/**
	 * Send an email to the user
	 *
	 * @param array $sent_values All values sent
	 * @param array $config      The form config
	 * @return bool|mixed
	 */
	private function send_mail_user( array $sent_values, array $config ): mixed {

		// To please PHPCS
		check_ajax_referer( 'form--nonce', 'form--security' );

		$context = Timber::context();
		$context[ 'datas' ] = $config[ 'email_user' ];
		$context[ 'colors' ] = $config[ 'email_colors' ];
		$context[ 'content' ] = $content;
		$mail_message = Timber::compile( 'email-user.twig', $context );

		$mail_headers = array();
		$mail_headers[] = 'Content-Type: text/html; charset = UTF-8';
		$mail_headers[] = "From: {$config['email_user']['sender_name']} <{$config['email_user']['sender_email']}>";

		// CC
		if ( $config[ 'email_user' ][ 'recipient_email_cc' ] ) {
			foreach ( explode( ',', $config[ 'email_user' ][ 'recipient_email_cc' ] ) as $email ) {
				$email = sanitize_email( trim( $email ) );
				$mail_headers[] = "CC: $email";
			}
		}

		// CCI
		if ( $config[ 'email_user' ][ 'recipient_email_cci' ] ) {
			foreach ( explode( ',', $config[ 'email_user' ][ 'recipient_email_cci' ] ) as $email ) {
				$email = sanitize_email( trim( $email ) );
				$mail_headers[] = "BCC: $email";
			}
		}

		$first_name = array_key_exists( 'firstname', $_POST ) ? sanitize_text_field( wp_unslash( $_POST[ 'firstname' ] ) ) : '';
		$last_name = array_key_exists( 'lastname', $_POST ) ? sanitize_text_field( wp_unslash( $_POST[ 'lastname' ] ) ) : '';
		$email = array_key_exists( 'email', $_POST ) ? sanitize_email( wp_unslash( $_POST[ 'email' ] ) ) : false;

		if ( ! $email ) {
			return false;
		}

		return wp_mail( "$first_name $last_name <$email>", $config[ 'email_user' ][ 'subject' ], $mail_message, $mail_headers );
	}

	/**
	 * Save the message
	 *
	 * @param array $config The form config
	 * @return void
	 */
	private function save_message( array $config ): void {

		// To please PHPCS
		check_ajax_referer( 'form--nonce', 'form--security' );

		$post_title = '';
		$raw_data_post_title = array_key_exists( 'data-post-title', $_POST ) ? wp_kses_post( wp_unslash( $_POST[ 'data-post-title' ] ) ) : '';
		$data_post_title = explode( '|', $raw_data_post_title );

		foreach ( $data_post_title as $name ) {
			if ( array_key_exists( $name, $_POST ) ) {
				$post_title .= sanitize_text_field( wp_unslash( $_POST[ $name ] ) );
			} elseif ( $name === 'DATETIME' ) {
				$post_title .= current_time( 'Y\/m\/d H\hi' );
			} elseif ( ! isset( $config[ 'fields' ][ $name ][ 'active' ] ) || ( isset( $config[ 'fields' ][ $name ][ 'active' ] ) && $config[ 'fields' ][ $name ][ 'active' ] ) ) {
				$post_title .= $name;
			}
		}
		$post_title = rtrim( $post_title );

		$post_id = wp_insert_post( array(
			'post_date'   => current_time( 'mysql' ),
			'post_title'  => $post_title,
			'post_type'   => $this->params[ 'id' ] . '_form',
			'post_status' => 'publish',
		) );

		foreach ( $config[ 'fields' ] as $field_name => $field ) {
			if ( ! $field[ 'active' ] ) {
				continue;
			}

			$value = '';
			if ( array_key_exists( $field_name, $_POST ) ) {
				$value = wp_kses_post( wp_unslash( $_POST[ $field_name ] ) );
			}

			update_post_meta( $post_id, $field_name, $value );
		}
	}

	/**
	 * Get the upload dir for the current form
	 *
	 * @param array $dir The directory path
	 * @return string[]
	 */
	public function custom_upload_dir( array $dir ): array {
		return array_merge( array(
			'path'   => $dir[ 'basedir' ] . '/' . $this->params[ 'id' ] . '-form',
			'url'    => $dir[ 'baseurl' ] . '/' . $this->params[ 'id' ] . '-form',
			'subdir' => '/' . $this->params[ 'id' ] . '-form',
		), $dir );
	}
}
