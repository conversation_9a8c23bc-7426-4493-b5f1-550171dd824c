// ********************************
// -------- Form - Buttons --------
// ********************************


.form--field-type-radio {
}

.form--field-type-checkbox {
	padding-top: 24px;
}

.form--field-type-radio,
.form--field-type-checkbox {

	.form--field-error {
		bottom: -16px;
	}
}

.form--field-type-radio_wrapper {
}

.form--field-type-checkbox,
.form--input__radio_wrapper {
	display: flex;
	align-items: baseline;
	flex-wrap: wrap;
}

.form--input__radio_wrapper {

	&:not(:nth-child(1)) {
		padding-top: 4px;
	}
}

.form--input__checkbox,
.form--input__radio {
	appearance: none;
	position: relative;
	width: 24px;
	height: 24px;
	margin-top: -1px;
	border: 2px solid rgba($color_black, 0.2);
	background-color: $color_white;
	transition: border-color $ease_default;

	+ .form--label {
		flex: 1;
		align-self: flex-start;
		padding-left: 8px;
	}

	@include hover {
		border-color: rgba($color_black, 0.5);

		&::after {
			opacity: 0.25;
		}
	}

	&:focus-visible:not(:hover) {
		border-color: $color_black;

		&::after {
			opacity: 0.25;
		}
	}

	&::after {
		display: block;
		position: absolute;
		transform: scale(0);
		transition:
			transform 0.3s $ease_out_quad,
			opacity $ease_default;
	}

	&:checked::after {
		transform: scale(1);
	}
}

.form--input__checkbox {
	border-radius: 4px;

	&::after {

		@include check($color_black);
		top: 6px;
		left: 5px;
		width: 11px;
		height: 8px;
		line-height: 0;
		transform: scale(0);
	}
}

.form--input__radio {
	border-radius: 50%;

	&::after {
		content: "";
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		width: 8px;
		height: 8px;
		margin: auto;
		border-radius: 100%;
		background-color: $color_black;
	}
}
