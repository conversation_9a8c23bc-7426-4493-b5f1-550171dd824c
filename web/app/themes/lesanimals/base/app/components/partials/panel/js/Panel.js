import AbstractView from 'abstracts/AbstractView';
import Main from 'controllers/Main';

export default class Panel extends AbstractView {
	/**
	 * @type {Cash} The scrollable content
	 */
	#$inner = null;

	/**
	 * @type {boolean} Is panel open?
	 */
	#isOpen = false;

	/**
	 * @type {string} Panel id
	 */
	#panelId;

	/**
	 * @type {string} The hash
	 */
	#hash;

	init() {
		super.init();

		this.#checkHash();
	}

	initDOM() {
		this.#$inner = this.$wrapper.find( '.panel__wrapper' );
	}

	initEl() {
		this.$wrapper[ 0 ].inert = true;

		this.#panelId = this.$wrapper.attr( 'data-panel-id' );
		this.#hash = this.$wrapper.attr( 'data-url-slug' ) || 'panel';
	}

	bindEvents() {
		// Any link with the associated data
		this.eh.toggle = this.#toggle.bind( this );
		Main.$body.on( 'click', '[data-panel-toggle="' + this.#panelId + '"]', this.eh.toggle );

		// Keyboard press
		this.eh.onKeyPress = this.#onKeyPress.bind( this );
		Main.$body.on( 'keydown', this.eh.onKeyPress );
	}

	async #toggle( event = null ) {
		if ( this.#isOpen ) {
			this.killGSAP( 'openScrollDelay' );
			this.killGSAP( 'closeScrollDelay' );
			await this.beforeClose( event );
			window.location.hash = '/';
			this.#isOpen = false;
			this.$wrapper.removeClass( 'panel__open' );
			Main.$html.removeClass( 'panel__open ' + this.#panelId + '__open' );
			this.$wrapper[ 0 ].inert = true;
			this.gsap.closeScrollDelay = gsap.delayedCall( 0.5, () => {
				this.#$inner[ 0 ].scroll( 0, 0 );
			} );
			this.afterClose( event );
		} else {
			await this.beforeOpen( event );
			window.location.hash = '/' + this.#hash;
			this.killGSAP( 'openScrollDelay' );
			this.killGSAP( 'closeScrollDelay' );
			this.#isOpen = true;
			this.$wrapper[ 0 ].inert = false;
			this.$wrapper.addClass( 'panel__open' );
			this.gsap.openScrollDelay = gsap.delayedCall( 0.5, () => {
				Main.$html.addClass( 'panel__open ' + this.#panelId + '__open' );
			} );
			this.afterOpen( event );
		}
	}

	/**
	 * To be extended
	 * Called before closing
	 *
	 * @return {Promise<void>}
	 */
	async beforeClose() {
	}

	/**
	 * To be extended
	 * Called after closing
	 *
	 * @return {void}
	 */
	afterClose() {
	}

	/**
	 * To be extended
	 * Called before opening
	 *
	 * @return {Promise<void>}
	 */
	async beforeOpen() {
	}

	/**
	 * To be extended
	 * Called after opening
	 *
	 * @return {void}
	 */
	afterOpen() {
	}

	/**
	 * To be extended
	 * Called before opening automatically
	 *
	 * @return {Promise<boolean>} Returns true or false
	 */
	async beforeAutoOpen() {
		return true;
	}

	/**
	 * @param {KeyboardEvent} event
	 */
	#onKeyPress( event ) {
		if ( event.key === 'Escape' && this.#isOpen === true ) {
			this.#toggle();
		}
	}

	async #checkHash() {
		if ( ( window.location.hash === '#/' + this.#hash || window.location.hash.startsWith( '#/' + this.#hash + '/' ) ) && ! this.#isOpen ) {
			const autoOpenResult = await this.beforeAutoOpen();
			if ( autoOpenResult === true ) {
				this.#toggle();
			}
		}
	}
}

