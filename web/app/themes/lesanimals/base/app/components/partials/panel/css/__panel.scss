html.panel__open {
	overflow-y: hidden;
	min-height: 100vh;
}

.panel {
	margin: auto;
	overflow: hidden;
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: $depth_panel_default;

	&:not(.panel__open) {
		pointer-events: none;

		.panel--overlay {
			opacity: 0;
		}

		.panel--wrapper {
			transform: translateX(100%);
		}

		.panel--inner {
			transform: translateX(-100%);
		}
	}
}

.panel--overlay {
	background-color: rgba($color_highlight, 0.7);
	margin: auto;
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1;
	transition: opacity $ease_default;
}

.panel--wrapper {
	background-color: $color_grey;
	margin: auto;
	overscroll-behavior: contain;
	overflow-x: hidden;
	overflow-y: scroll;
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 5;
	transition: transform $ease_default;
}

.panel--inner {
	position: relative;
	transition: transform $ease_default;
	height: 100%;
	min-height: calc(100% + 1px);
}
