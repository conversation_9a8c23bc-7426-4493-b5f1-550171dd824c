# 📬 Forms

## 📦️ Dependency

* [loader `app/components/partials/loader`](../loader)

## ✏️ Forms configuration file

Use one of those two files:

* [Contact form example `app/components/partials/form/config-contact.json`](./configs/config-contact.json)
* [Download form example `app/components/partials/form/config-download.json`](./configs/config-download.json)

## ⚙️️ Config

### Parameters examples

```js
"post_title": "DATETIME| — |lastname| |firstname|, |company"
```

```js
"parent_slug": "edit.php?post_type=page"
```

### 🔖 Labels

Labels of the post type can be overwritten:

```js
"labels": {
	"name": "Téléchargements",
	"singular_name": "Téléchargement",
	"menu_name": "Téléchargements",
	"name_admin_bar": "Téléchargements",
	"archives": "Téléchargements",
	"all_items": "Toutes les demandes"
},
```

### 📥 Fields

Each field can have several options:

* `label`: `{string}` Name of the field that appear in the BO
* `type`: `{string}` Type of the field, can be `text`, `tel`, `email`, `textarea`, `radio`, `select`, `checkbox` or `file`
* `mandatory`: `{boolean}` If `true`, disable the possibility to disable the field from BO
* `placeholder`: `{boolean}` Placeholder of the field, if needed
* `errors`: `{object}` A list of errors `"type": "value"`
  * `type`: `{string}` Type of error, can be `empty` or `email`
  * `value`: `{string}` Name of the error field that appear in the BO

## 💌 Emails

Emails are in the folder [`/app/components/partials/form/twig/`](./twig/). There are two templates:
* `email-admin.twig`: email sent to the admin
* `email-user.twig`: email sent to the user

The email templates can be fully customized using the color fields in the `Couleurs emails` tab and the two last fields (image and content) in the `Email administrateur` and `Email utilisateur` tabs.

## ⬇️ Include the form

```php
{% include 'form.twig' with { id: 'contact' } %}
```

```php
{% include 'form.twig' with { id: 'download' } %}
```
