<?php
/**
 * Registration Email
 *
 * @package lesanimals
 */

namespace LesAnimals\Core\AdminEmail;

use Timber;

class User_Registration {

	/**
	 * Class construct
	 */
	public function __construct() {
		add_filter( 'wp_new_user_notification_email', array( $this, 'new_user_notification_email_callback' ), 10, 3 );
	}

	/**
	 * New user notification email callback
	 *
	 * @param array    $email     Email
	 * @param \WP_User $user      User
	 * @param string   $site_name Site name
	 *
	 * @return array
	 */
	public function new_user_notification_email_callback( array $email, \WP_User $user, string $site_name ): array {

		ray()->showWordPressErrors();

		// Is there options saved ?
		$options = get_fields( 'options' );
		if ( empty( $options ) || empty( $options[ 'email_first_login' ] ) ) {
			return $email;
		}

		// Only get the needed options
		$options = $options[ 'email_first_login' ];

		if ( empty( $options[ 'message' ] ) ) {
			return $email;
		}

		// Sets the subject
		$email[ 'subject' ] = '[' . $site_name . '] ' . $options[ 'subject' ];

		// Sets the mail from
		add_filter( 'wp_mail_from', function () use ( $options ) {
			return $options[ 'sender_email' ];
		} );

		// Get the login url
		$key = get_password_reset_key( $user );
		if ( is_wp_error( $key ) ) {
			return $email;
		}
		$first_login_url = network_site_url( "wp-login.php?action=rp&key=$key&login=" . rawurlencode( $user->user_login ), 'login' );
		$first_login_url = '<a href="' . $first_login_url . '" target="_blank">' . __( 'Générer un mot de passe', 'lesanimals' ) . '</a>';

		// Prepare the content
		$message_content = $options[ 'message' ];
		$message_content = str_replace( '{site_name}', $site_name, $message_content );
		$message_content = str_replace( '{login_link}', $first_login_url, $message_content );
		$message_content = str_replace( '{user_login}', $user->user_login, $message_content );
		$message_content = str_replace( '{user_email}', $user->user_email, $message_content );

		// Timber context to compile the html content from twig
		$context = Timber::context();
		$context[ 'message_content' ] = $message_content;
		$mail_message = Timber::compile( 'core/admin-emails/twig/registration-email.twig', $context );

		// Sets the message
		$email[ 'message' ] = $mail_message;

		return $email;
	}
}

new User_Registration();
