<?php
/**
 * Context for __View Name__
 *
 * @package lesanimals
 */

namespace LesAnimals\Components\__ViewTypes__\__ViewNameClass__;

class Context {

	/**
	 * Class construct
	 */
	public function __construct() {
		add_filter( 'timber/context', array( $this, 'setup_context' ) );
	}

	/**
	 * Add variables to Timber context
	 *
	 * @param array $context Timber context
	 * @return array
	 */
	public function setup_context( array $context ): array {
		return $context;
	}
}

new Context();
