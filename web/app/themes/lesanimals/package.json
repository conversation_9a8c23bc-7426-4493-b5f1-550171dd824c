{"name": "lesanimals", "description": "Les Animals Wordpress theme", "version": "4.0.0", "type": "module", "author": {"name": "Les Animals", "url": "https://lesanimals.digital"}, "homepage": "https://github.com/lesanimals/lesanimals.eu", "repository": {"type": "git", "url": "**************:lesanimals/lesanimals.eu.git"}, "license": "GPL-2.0", "dependencies": {"eventemitter3": "5.0.1", "fetch-loader": "github:LilGast8/fetch-loader", "gsap": "npm:@gsap/shockingly@3.12.5", "js-cookie": "3.0.5", "masonry-layout": "^4.2.2", "photoswipe": "5.4.3", "sortablejs": "1.15.6", "tus-js-client": "4.3.1", "vanilla-lazyload": "18.0.0"}, "devDependencies": {"@csstools/postcss-sass": "5.1.1", "@wordpress/eslint-plugin": "17.11.0", "@wordpress/stylelint-config": "21.37.0", "autoprefixer": "10.4.19", "browser-sync": "3.0.2", "chalk": "5.3.0", "chokidar": "3.6.0", "cssnano": "7.0.5", "dotenv": "16.4.5", "esbuild": "0.20.2", "esbuild-plugin-glob-import": "github:lesanimals/esbuild-plugin-glob-import", "eslint": "8.57.0", "fast-glob": "3.3.2", "husky": "9.0.11", "imagemin": "8.0.1", "imagemin-jpegtran": "7.0.0", "imagemin-pngquant": "9.0.2", "imagemin-svgo": "10.0.1", "import-glob": "1.5.0", "inquirer": "9.2.17", "postcss": "8.4.38", "postcss-import": "16.1.0", "postcss-import-ext-glob": "2.1.1", "postcss-scss": "4.0.9", "stylelint": "14.16.1", "stylelint-config-standard-scss": "6.0.0", "svg-sprite": "2.0.4", "yesno": "0.4.0"}, "scripts": {"builddev": "node build/build.js development", "createview": "node build/create-view.js", "deploydev": "npm run buildprod && node build/deploy.js dev", "deploypreprod": "npm run buildprod && node build/deploy.js staging", "deployprod": "npm run buildprod && node build/deploy.js production", "pulldev": "wp migratedb profile pulldev", "pullpreprod": "wp migratedb profile pullpreprod", "pullprod": "wp migratedb profile pullprod", "buildprod": "node build/build.js production", "lint": "node build/lint.js", "prepare": "cd ../../../../ && husky"}}