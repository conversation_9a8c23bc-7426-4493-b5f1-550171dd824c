# WordPress MySQL database migration
#
# Generated: Tuesday 8. October 2024 08:54 UTC
# Hostname: localhost:/Users/<USER>/Library/Application Support/Local/run/3t_QEVsTi/mysql/mysqld.sock
# Database: `local`
# URL: //numeridanse.local
# Path: /Users/<USER>/www/numeridanse.tv/app/public/web
# Tables: nd_commentmeta, nd_comments, nd_links, nd_options, nd_postmeta, nd_posts, nd_pp_activity_logs, nd_seopress_content_analysis, nd_seopress_seo_issues, nd_seopress_significant_keywords, nd_term_relationships, nd_term_taxonomy, nd_termmeta, nd_terms, nd_usermeta, nd_users
# Table Prefix: nd_
# Post Types: revision, acf-field, attachment, collection, nav_menu_item, page, playlist, profile, publication
# Protocol: https
# Multisite: false
# Subsite Export: false
# --------------------------------------------------------

/*!40101 SET NAMES utf8mb4 */;

SET sql_mode='NO_AUTO_VALUE_ON_ZERO';



#
# Delete any existing table `nd_commentmeta`
#

DROP TABLE IF EXISTS `nd_commentmeta`;


#
# Table structure of table `nd_commentmeta`
#

CREATE TABLE `nd_commentmeta` (
  `meta_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `comment_id` bigint(20) unsigned NOT NULL DEFAULT '0',
  `meta_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`meta_id`),
  KEY `comment_id` (`comment_id`),
  KEY `meta_key` (`meta_key`(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_commentmeta`
#

#
# End of data contents of table `nd_commentmeta`
# --------------------------------------------------------



#
# Delete any existing table `nd_comments`
#

DROP TABLE IF EXISTS `nd_comments`;


#
# Table structure of table `nd_comments`
#

CREATE TABLE `nd_comments` (
  `comment_ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `comment_post_ID` bigint(20) unsigned NOT NULL DEFAULT '0',
  `comment_author` tinytext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `comment_author_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_author_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_author_IP` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `comment_date_gmt` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `comment_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `comment_karma` int(11) NOT NULL DEFAULT '0',
  `comment_approved` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '1',
  `comment_agent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'comment',
  `comment_parent` bigint(20) unsigned NOT NULL DEFAULT '0',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`comment_ID`),
  KEY `comment_post_ID` (`comment_post_ID`),
  KEY `comment_approved_date_gmt` (`comment_approved`,`comment_date_gmt`),
  KEY `comment_date_gmt` (`comment_date_gmt`),
  KEY `comment_parent` (`comment_parent`),
  KEY `comment_author_email` (`comment_author_email`(10))
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_comments`
#

#
# End of data contents of table `nd_comments`
# --------------------------------------------------------



#
# Delete any existing table `nd_links`
#

DROP TABLE IF EXISTS `nd_links`;


#
# Table structure of table `nd_links`
#

CREATE TABLE `nd_links` (
  `link_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `link_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_target` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_visible` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'Y',
  `link_owner` bigint(20) unsigned NOT NULL DEFAULT '1',
  `link_rating` int(11) NOT NULL DEFAULT '0',
  `link_updated` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `link_rel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_notes` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `link_rss` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`link_id`),
  KEY `link_visible` (`link_visible`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_links`
#

#
# End of data contents of table `nd_links`
# --------------------------------------------------------



#
# Delete any existing table `nd_options`
#

DROP TABLE IF EXISTS `nd_options`;


#
# Table structure of table `nd_options`
#

CREATE TABLE `nd_options` (
  `option_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `option_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `option_value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `autoload` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'yes',
  PRIMARY KEY (`option_id`),
  UNIQUE KEY `option_name` (`option_name`),
  KEY `autoload` (`autoload`)
) ENGINE=InnoDB AUTO_INCREMENT=3196 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_options`
#
INSERT INTO `nd_options` ( `option_id`, `option_name`, `option_value`, `autoload`) VALUES
(1, 'siteurl', 'https://numeridanse.local/wp', 'yes'),
(2, 'home', 'https://numeridanse.local/wp', 'yes'),
(3, 'blogname', 'numeridanse.tv', 'yes'),
(4, 'blogdescription', 'numeridanse.tv', 'yes'),
(5, 'users_can_register', '0', 'yes'),
(6, 'admin_email', '<EMAIL>', 'yes'),
(7, 'start_of_week', '1', 'yes'),
(8, 'use_balanceTags', '0', 'yes'),
(9, 'use_smilies', '1', 'yes'),
(10, 'require_name_email', '1', 'yes'),
(11, 'comments_notify', '1', 'yes'),
(12, 'posts_per_rss', '10', 'yes'),
(13, 'rss_use_excerpt', '0', 'yes'),
(14, 'mailserver_url', 'mail.example.com', 'yes'),
(15, 'mailserver_login', '<EMAIL>', 'yes'),
(16, 'mailserver_pass', 'password', 'yes'),
(17, 'mailserver_port', '110', 'yes'),
(18, 'default_category', '1', 'yes'),
(19, 'default_comment_status', 'open', 'yes'),
(20, 'default_ping_status', 'open', 'yes'),
(21, 'default_pingback_flag', '1', 'yes'),
(22, 'posts_per_page', '10', 'yes'),
(23, 'date_format', 'j F Y', 'yes'),
(24, 'time_format', 'H:i:s', 'yes'),
(25, 'links_updated_date_format', 'F j, Y g:i a', 'yes'),
(26, 'comment_moderation', '0', 'yes'),
(27, 'moderation_notify', '1', 'yes'),
(28, 'permalink_structure', '/%postname%/', 'yes'),
(29, 'rewrite_rules', 'a:183:{s:11:"^wp-json/?$";s:22:"index.php?rest_route=/";s:14:"^wp-json/(.*)?";s:33:"index.php?rest_route=/$matches[1]";s:21:"^index.php/wp-json/?$";s:22:"index.php?rest_route=/";s:24:"^index.php/wp-json/(.*)?";s:33:"index.php?rest_route=/$matches[1]";s:14:"^sitemaps.xml$";s:28:"index.php?seopress_sitemap=1";s:18:"^sitemaps_xsl.xsl$";s:32:"index.php?seopress_sitemap_xsl=1";s:24:"^sitemaps_video_xsl.xsl$";s:38:"index.php?seopress_sitemap_video_xsl=1";s:31:"([^/]+?)-sitemap([0-9]+)?\\.xml$";s:61:"index.php?seopress_cpt=$matches[1]&seopress_paged=$matches[2]";s:47:"category/(.+?)/feed/(feed|rdf|rss|rss2|atom)/?$";s:52:"index.php?category_name=$matches[1]&feed=$matches[2]";s:42:"category/(.+?)/(feed|rdf|rss|rss2|atom)/?$";s:52:"index.php?category_name=$matches[1]&feed=$matches[2]";s:23:"category/(.+?)/embed/?$";s:46:"index.php?category_name=$matches[1]&embed=true";s:35:"category/(.+?)/page/?([0-9]{1,})/?$";s:53:"index.php?category_name=$matches[1]&paged=$matches[2]";s:17:"category/(.+?)/?$";s:35:"index.php?category_name=$matches[1]";s:44:"tag/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:42:"index.php?tag=$matches[1]&feed=$matches[2]";s:39:"tag/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:42:"index.php?tag=$matches[1]&feed=$matches[2]";s:20:"tag/([^/]+)/embed/?$";s:36:"index.php?tag=$matches[1]&embed=true";s:32:"tag/([^/]+)/page/?([0-9]{1,})/?$";s:43:"index.php?tag=$matches[1]&paged=$matches[2]";s:14:"tag/([^/]+)/?$";s:25:"index.php?tag=$matches[1]";s:45:"type/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:50:"index.php?post_format=$matches[1]&feed=$matches[2]";s:40:"type/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:50:"index.php?post_format=$matches[1]&feed=$matches[2]";s:21:"type/([^/]+)/embed/?$";s:44:"index.php?post_format=$matches[1]&embed=true";s:33:"type/([^/]+)/page/?([0-9]{1,})/?$";s:51:"index.php?post_format=$matches[1]&paged=$matches[2]";s:15:"type/([^/]+)/?$";s:33:"index.php?post_format=$matches[1]";s:62:"publication/categorie/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:51:"index.php?pub_category=$matches[1]&feed=$matches[2]";s:57:"publication/categorie/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:51:"index.php?pub_category=$matches[1]&feed=$matches[2]";s:38:"publication/categorie/([^/]+)/embed/?$";s:45:"index.php?pub_category=$matches[1]&embed=true";s:50:"publication/categorie/([^/]+)/page/?([0-9]{1,})/?$";s:52:"index.php?pub_category=$matches[1]&paged=$matches[2]";s:32:"publication/categorie/([^/]+)/?$";s:34:"index.php?pub_category=$matches[1]";s:38:"collection/[^/]+/attachment/([^/]+)/?$";s:32:"index.php?attachment=$matches[1]";s:48:"collection/[^/]+/attachment/([^/]+)/trackback/?$";s:37:"index.php?attachment=$matches[1]&tb=1";s:68:"collection/[^/]+/attachment/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:63:"collection/[^/]+/attachment/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:63:"collection/[^/]+/attachment/([^/]+)/comment-page-([0-9]{1,})/?$";s:50:"index.php?attachment=$matches[1]&cpage=$matches[2]";s:44:"collection/[^/]+/attachment/([^/]+)/embed/?$";s:43:"index.php?attachment=$matches[1]&embed=true";s:27:"collection/([^/]+)/embed/?$";s:43:"index.php?collection=$matches[1]&embed=true";s:31:"collection/([^/]+)/trackback/?$";s:37:"index.php?collection=$matches[1]&tb=1";s:39:"collection/([^/]+)/page/?([0-9]{1,})/?$";s:50:"index.php?collection=$matches[1]&paged=$matches[2]";s:46:"collection/([^/]+)/comment-page-([0-9]{1,})/?$";s:50:"index.php?collection=$matches[1]&cpage=$matches[2]";s:35:"collection/([^/]+)(?:/([0-9]+))?/?$";s:49:"index.php?collection=$matches[1]&page=$matches[2]";s:27:"collection/[^/]+/([^/]+)/?$";s:32:"index.php?attachment=$matches[1]";s:37:"collection/[^/]+/([^/]+)/trackback/?$";s:37:"index.php?attachment=$matches[1]&tb=1";s:57:"collection/[^/]+/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:52:"collection/[^/]+/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:52:"collection/[^/]+/([^/]+)/comment-page-([0-9]{1,})/?$";s:50:"index.php?attachment=$matches[1]&cpage=$matches[2]";s:33:"collection/[^/]+/([^/]+)/embed/?$";s:43:"index.php?attachment=$matches[1]&embed=true";s:32:"demo/[^/]+/attachment/([^/]+)/?$";s:32:"index.php?attachment=$matches[1]";s:42:"demo/[^/]+/attachment/([^/]+)/trackback/?$";s:37:"index.php?attachment=$matches[1]&tb=1";s:62:"demo/[^/]+/attachment/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:57:"demo/[^/]+/attachment/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:57:"demo/[^/]+/attachment/([^/]+)/comment-page-([0-9]{1,})/?$";s:50:"index.php?attachment=$matches[1]&cpage=$matches[2]";s:38:"demo/[^/]+/attachment/([^/]+)/embed/?$";s:43:"index.php?attachment=$matches[1]&embed=true";s:21:"demo/([^/]+)/embed/?$";s:37:"index.php?demo=$matches[1]&embed=true";s:25:"demo/([^/]+)/trackback/?$";s:31:"index.php?demo=$matches[1]&tb=1";s:33:"demo/([^/]+)/page/?([0-9]{1,})/?$";s:44:"index.php?demo=$matches[1]&paged=$matches[2]";s:40:"demo/([^/]+)/comment-page-([0-9]{1,})/?$";s:44:"index.php?demo=$matches[1]&cpage=$matches[2]";s:29:"demo/([^/]+)(?:/([0-9]+))?/?$";s:43:"index.php?demo=$matches[1]&page=$matches[2]";s:21:"demo/[^/]+/([^/]+)/?$";s:32:"index.php?attachment=$matches[1]";s:31:"demo/[^/]+/([^/]+)/trackback/?$";s:37:"index.php?attachment=$matches[1]&tb=1";s:51:"demo/[^/]+/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:46:"demo/[^/]+/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:46:"demo/[^/]+/([^/]+)/comment-page-([0-9]{1,})/?$";s:50:"index.php?attachment=$matches[1]&cpage=$matches[2]";s:27:"demo/[^/]+/([^/]+)/embed/?$";s:43:"index.php?attachment=$matches[1]&embed=true";s:36:"playlist/[^/]+/attachment/([^/]+)/?$";s:32:"index.php?attachment=$matches[1]";s:46:"playlist/[^/]+/attachment/([^/]+)/trackback/?$";s:37:"index.php?attachment=$matches[1]&tb=1";s:66:"playlist/[^/]+/attachment/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:61:"playlist/[^/]+/attachment/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:61:"playlist/[^/]+/attachment/([^/]+)/comment-page-([0-9]{1,})/?$";s:50:"index.php?attachment=$matches[1]&cpage=$matches[2]";s:42:"playlist/[^/]+/attachment/([^/]+)/embed/?$";s:43:"index.php?attachment=$matches[1]&embed=true";s:25:"playlist/([^/]+)/embed/?$";s:41:"index.php?playlist=$matches[1]&embed=true";s:29:"playlist/([^/]+)/trackback/?$";s:35:"index.php?playlist=$matches[1]&tb=1";s:37:"playlist/([^/]+)/page/?([0-9]{1,})/?$";s:48:"index.php?playlist=$matches[1]&paged=$matches[2]";s:44:"playlist/([^/]+)/comment-page-([0-9]{1,})/?$";s:48:"index.php?playlist=$matches[1]&cpage=$matches[2]";s:33:"playlist/([^/]+)(?:/([0-9]+))?/?$";s:47:"index.php?playlist=$matches[1]&page=$matches[2]";s:25:"playlist/[^/]+/([^/]+)/?$";s:32:"index.php?attachment=$matches[1]";s:35:"playlist/[^/]+/([^/]+)/trackback/?$";s:37:"index.php?attachment=$matches[1]&tb=1";s:55:"playlist/[^/]+/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:50:"playlist/[^/]+/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:50:"playlist/[^/]+/([^/]+)/comment-page-([0-9]{1,})/?$";s:50:"index.php?attachment=$matches[1]&cpage=$matches[2]";s:31:"playlist/[^/]+/([^/]+)/embed/?$";s:43:"index.php?attachment=$matches[1]&embed=true";s:34:"profil/[^/]+/attachment/([^/]+)/?$";s:32:"index.php?attachment=$matches[1]";s:44:"profil/[^/]+/attachment/([^/]+)/trackback/?$";s:37:"index.php?attachment=$matches[1]&tb=1";s:64:"profil/[^/]+/attachment/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:59:"profil/[^/]+/attachment/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:59:"profil/[^/]+/attachment/([^/]+)/comment-page-([0-9]{1,})/?$";s:50:"index.php?attachment=$matches[1]&cpage=$matches[2]";s:40:"profil/[^/]+/attachment/([^/]+)/embed/?$";s:43:"index.php?attachment=$matches[1]&embed=true";s:23:"profil/([^/]+)/embed/?$";s:40:"index.php?profile=$matches[1]&embed=true";s:27:"profil/([^/]+)/trackback/?$";s:34:"index.php?profile=$matches[1]&tb=1";s:35:"profil/([^/]+)/page/?([0-9]{1,})/?$";s:47:"index.php?profile=$matches[1]&paged=$matches[2]";s:42:"profil/([^/]+)/comment-page-([0-9]{1,})/?$";s:47:"index.php?profile=$matches[1]&cpage=$matches[2]";s:31:"profil/([^/]+)(?:/([0-9]+))?/?$";s:46:"index.php?profile=$matches[1]&page=$matches[2]";s:23:"profil/[^/]+/([^/]+)/?$";s:32:"index.php?attachment=$matches[1]";s:33:"profil/[^/]+/([^/]+)/trackback/?$";s:37:"index.php?attachment=$matches[1]&tb=1";s:53:"profil/[^/]+/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:48:"profil/[^/]+/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:48:"profil/[^/]+/([^/]+)/comment-page-([0-9]{1,})/?$";s:50:"index.php?attachment=$matches[1]&cpage=$matches[2]";s:29:"profil/[^/]+/([^/]+)/embed/?$";s:43:"index.php?attachment=$matches[1]&embed=true";s:39:"publication/[^/]+/attachment/([^/]+)/?$";s:32:"index.php?attachment=$matches[1]";s:49:"publication/[^/]+/attachment/([^/]+)/trackback/?$";s:37:"index.php?attachment=$matches[1]&tb=1";s:69:"publication/[^/]+/attachment/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:64:"publication/[^/]+/attachment/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:64:"publication/[^/]+/attachment/([^/]+)/comment-page-([0-9]{1,})/?$";s:50:"index.php?attachment=$matches[1]&cpage=$matches[2]";s:45:"publication/[^/]+/attachment/([^/]+)/embed/?$";s:43:"index.php?attachment=$matches[1]&embed=true";s:28:"publication/([^/]+)/embed/?$";s:44:"index.php?publication=$matches[1]&embed=true";s:32:"publication/([^/]+)/trackback/?$";s:38:"index.php?publication=$matches[1]&tb=1";s:40:"publication/([^/]+)/page/?([0-9]{1,})/?$";s:51:"index.php?publication=$matches[1]&paged=$matches[2]";s:47:"publication/([^/]+)/comment-page-([0-9]{1,})/?$";s:51:"index.php?publication=$matches[1]&cpage=$matches[2]";s:36:"publication/([^/]+)(?:/([0-9]+))?/?$";s:50:"index.php?publication=$matches[1]&page=$matches[2]";s:28:"publication/[^/]+/([^/]+)/?$";s:32:"index.php?attachment=$matches[1]";s:38:"publication/[^/]+/([^/]+)/trackback/?$";s:37:"index.php?attachment=$matches[1]&tb=1";s:58:"publication/[^/]+/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:53:"publication/[^/]+/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:53:"publication/[^/]+/([^/]+)/comment-page-([0-9]{1,})/?$";s:50:"index.php?attachment=$matches[1]&cpage=$matches[2]";s:34:"publication/[^/]+/([^/]+)/embed/?$";s:43:"index.php?attachment=$matches[1]&embed=true";s:12:"robots\\.txt$";s:18:"index.php?robots=1";s:13:"favicon\\.ico$";s:19:"index.php?favicon=1";s:48:".*wp-(atom|rdf|rss|rss2|feed|commentsrss2)\\.php$";s:18:"index.php?feed=old";s:20:".*wp-app\\.php(/.*)?$";s:19:"index.php?error=403";s:18:".*wp-register.php$";s:23:"index.php?register=true";s:32:"feed/(feed|rdf|rss|rss2|atom)/?$";s:27:"index.php?&feed=$matches[1]";s:27:"(feed|rdf|rss|rss2|atom)/?$";s:27:"index.php?&feed=$matches[1]";s:8:"embed/?$";s:21:"index.php?&embed=true";s:20:"page/?([0-9]{1,})/?$";s:28:"index.php?&paged=$matches[1]";s:27:"comment-page-([0-9]{1,})/?$";s:38:"index.php?&page_id=2&cpage=$matches[1]";s:41:"comments/feed/(feed|rdf|rss|rss2|atom)/?$";s:42:"index.php?&feed=$matches[1]&withcomments=1";s:36:"comments/(feed|rdf|rss|rss2|atom)/?$";s:42:"index.php?&feed=$matches[1]&withcomments=1";s:17:"comments/embed/?$";s:21:"index.php?&embed=true";s:44:"search/(.+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:40:"index.php?s=$matches[1]&feed=$matches[2]";s:39:"search/(.+)/(feed|rdf|rss|rss2|atom)/?$";s:40:"index.php?s=$matches[1]&feed=$matches[2]";s:20:"search/(.+)/embed/?$";s:34:"index.php?s=$matches[1]&embed=true";s:32:"search/(.+)/page/?([0-9]{1,})/?$";s:41:"index.php?s=$matches[1]&paged=$matches[2]";s:14:"search/(.+)/?$";s:23:"index.php?s=$matches[1]";s:47:"author/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:50:"index.php?author_name=$matches[1]&feed=$matches[2]";s:42:"author/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:50:"index.php?author_name=$matches[1]&feed=$matches[2]";s:23:"author/([^/]+)/embed/?$";s:44:"index.php?author_name=$matches[1]&embed=true";s:35:"author/([^/]+)/page/?([0-9]{1,})/?$";s:51:"index.php?author_name=$matches[1]&paged=$matches[2]";s:17:"author/([^/]+)/?$";s:33:"index.php?author_name=$matches[1]";s:69:"([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/feed/(feed|rdf|rss|rss2|atom)/?$";s:80:"index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]&feed=$matches[4]";s:64:"([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/(feed|rdf|rss|rss2|atom)/?$";s:80:"index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]&feed=$matches[4]";s:45:"([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/embed/?$";s:74:"index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]&embed=true";s:57:"([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/page/?([0-9]{1,})/?$";s:81:"index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]&paged=$matches[4]";s:39:"([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/?$";s:63:"index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]";s:56:"([0-9]{4})/([0-9]{1,2})/feed/(feed|rdf|rss|rss2|atom)/?$";s:64:"index.php?year=$matches[1]&monthnum=$matches[2]&feed=$matches[3]";s:51:"([0-9]{4})/([0-9]{1,2})/(feed|rdf|rss|rss2|atom)/?$";s:64:"index.php?year=$matches[1]&monthnum=$matches[2]&feed=$matches[3]";s:32:"([0-9]{4})/([0-9]{1,2})/embed/?$";s:58:"index.php?year=$matches[1]&monthnum=$matches[2]&embed=true";s:44:"([0-9]{4})/([0-9]{1,2})/page/?([0-9]{1,})/?$";s:65:"index.php?year=$matches[1]&monthnum=$matches[2]&paged=$matches[3]";s:26:"([0-9]{4})/([0-9]{1,2})/?$";s:47:"index.php?year=$matches[1]&monthnum=$matches[2]";s:43:"([0-9]{4})/feed/(feed|rdf|rss|rss2|atom)/?$";s:43:"index.php?year=$matches[1]&feed=$matches[2]";s:38:"([0-9]{4})/(feed|rdf|rss|rss2|atom)/?$";s:43:"index.php?year=$matches[1]&feed=$matches[2]";s:19:"([0-9]{4})/embed/?$";s:37:"index.php?year=$matches[1]&embed=true";s:31:"([0-9]{4})/page/?([0-9]{1,})/?$";s:44:"index.php?year=$matches[1]&paged=$matches[2]";s:13:"([0-9]{4})/?$";s:26:"index.php?year=$matches[1]";s:27:".?.+?/attachment/([^/]+)/?$";s:32:"index.php?attachment=$matches[1]";s:37:".?.+?/attachment/([^/]+)/trackback/?$";s:37:"index.php?attachment=$matches[1]&tb=1";s:57:".?.+?/attachment/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:52:".?.+?/attachment/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:52:".?.+?/attachment/([^/]+)/comment-page-([0-9]{1,})/?$";s:50:"index.php?attachment=$matches[1]&cpage=$matches[2]";s:33:".?.+?/attachment/([^/]+)/embed/?$";s:43:"index.php?attachment=$matches[1]&embed=true";s:16:"(.?.+?)/embed/?$";s:41:"index.php?pagename=$matches[1]&embed=true";s:20:"(.?.+?)/trackback/?$";s:35:"index.php?pagename=$matches[1]&tb=1";s:40:"(.?.+?)/feed/(feed|rdf|rss|rss2|atom)/?$";s:47:"index.php?pagename=$matches[1]&feed=$matches[2]";s:35:"(.?.+?)/(feed|rdf|rss|rss2|atom)/?$";s:47:"index.php?pagename=$matches[1]&feed=$matches[2]";s:28:"(.?.+?)/page/?([0-9]{1,})/?$";s:48:"index.php?pagename=$matches[1]&paged=$matches[2]";s:35:"(.?.+?)/comment-page-([0-9]{1,})/?$";s:48:"index.php?pagename=$matches[1]&cpage=$matches[2]";s:24:"(.?.+?)(?:/([0-9]+))?/?$";s:47:"index.php?pagename=$matches[1]&page=$matches[2]";s:27:"[^/]+/attachment/([^/]+)/?$";s:32:"index.php?attachment=$matches[1]";s:37:"[^/]+/attachment/([^/]+)/trackback/?$";s:37:"index.php?attachment=$matches[1]&tb=1";s:57:"[^/]+/attachment/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:52:"[^/]+/attachment/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:52:"[^/]+/attachment/([^/]+)/comment-page-([0-9]{1,})/?$";s:50:"index.php?attachment=$matches[1]&cpage=$matches[2]";s:33:"[^/]+/attachment/([^/]+)/embed/?$";s:43:"index.php?attachment=$matches[1]&embed=true";s:16:"([^/]+)/embed/?$";s:37:"index.php?name=$matches[1]&embed=true";s:20:"([^/]+)/trackback/?$";s:31:"index.php?name=$matches[1]&tb=1";s:40:"([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:43:"index.php?name=$matches[1]&feed=$matches[2]";s:35:"([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:43:"index.php?name=$matches[1]&feed=$matches[2]";s:28:"([^/]+)/page/?([0-9]{1,})/?$";s:44:"index.php?name=$matches[1]&paged=$matches[2]";s:35:"([^/]+)/comment-page-([0-9]{1,})/?$";s:44:"index.php?name=$matches[1]&cpage=$matches[2]";s:24:"([^/]+)(?:/([0-9]+))?/?$";s:43:"index.php?name=$matches[1]&page=$matches[2]";s:16:"[^/]+/([^/]+)/?$";s:32:"index.php?attachment=$matches[1]";s:26:"[^/]+/([^/]+)/trackback/?$";s:37:"index.php?attachment=$matches[1]&tb=1";s:46:"[^/]+/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:41:"[^/]+/([^/]+)/(feed|rdf|rss|rss2|atom)/?$";s:49:"index.php?attachment=$matches[1]&feed=$matches[2]";s:41:"[^/]+/([^/]+)/comment-page-([0-9]{1,})/?$";s:50:"index.php?attachment=$matches[1]&cpage=$matches[2]";s:22:"[^/]+/([^/]+)/embed/?$";s:43:"index.php?attachment=$matches[1]&embed=true";}', 'yes'),
(30, 'hack_file', '0', 'yes'),
(31, 'blog_charset', 'UTF-8', 'yes'),
(32, 'moderation_keys', '', 'no'),
(34, 'category_base', '', 'yes'),
(35, 'ping_sites', 'http://rpc.pingomatic.com/', 'yes'),
(36, 'comment_max_links', '2', 'yes'),
(37, 'gmt_offset', '', 'yes'),
(38, 'default_email_category', '1', 'yes'),
(39, 'recently_edited', '', 'no'),
(40, 'template', 'lesanimals', 'yes'),
(41, 'stylesheet', 'lesanimals', 'yes'),
(42, 'comment_registration', '0', 'yes'),
(43, 'html_type', 'text/html', 'yes'),
(44, 'use_trackback', '0', 'yes'),
(45, 'default_role', 'subscriber', 'yes'),
(46, 'db_version', '57155', 'yes'),
(47, 'uploads_use_yearmonth_folders', '1', 'yes'),
(50, 'default_link_category', '2', 'yes'),
(51, 'show_on_front', 'page', 'yes'),
(52, 'tag_base', '', 'yes'),
(53, 'show_avatars', '1', 'yes'),
(54, 'avatar_rating', 'G', 'yes'),
(56, 'thumbnail_size_w', '256', 'yes'),
(57, 'thumbnail_size_h', '256', 'yes'),
(58, 'thumbnail_crop', '1', 'yes'),
(59, 'medium_size_w', '300', 'yes'),
(60, 'medium_size_h', '300', 'yes'),
(61, 'avatar_default', 'mystery', 'yes'),
(62, 'large_size_w', '1024', 'yes'),
(63, 'large_size_h', '1024', 'yes'),
(64, 'image_default_link_type', 'none', 'yes'),
(65, 'image_default_size', '', 'yes'),
(66, 'image_default_align', '', 'yes'),
(67, 'close_comments_for_old_posts', '0', 'yes'),
(68, 'close_comments_days_old', '14', 'yes'),
(69, 'thread_comments', '1', 'yes'),
(70, 'thread_comments_depth', '5', 'yes'),
(71, 'page_comments', '0', 'yes'),
(72, 'comments_per_page', '50', 'yes'),
(73, 'default_comments_page', 'newest', 'yes'),
(74, 'comment_order', 'asc', 'yes'),
(75, 'sticky_posts', 'a:0:{}', 'yes'),
(76, 'widget_categories', 'a:2:{i:1;a:0:{}s:12:"_multiwidget";i:1;}', 'yes'),
(77, 'widget_text', 'a:2:{i:1;a:0:{}s:12:"_multiwidget";i:1;}', 'yes'),
(78, 'widget_rss', 'a:2:{i:1;a:0:{}s:12:"_multiwidget";i:1;}', 'yes'),
(79, 'uninstall_plugins', 'a:3:{s:45:"enable-media-replace/enable-media-replace.php";a:2:{i:0;s:33:"\\EnableMediaReplace\\InstallHelper";i:1;s:15:"uninstallPlugin";}s:31:"wp-sort-order/inc/functions.php";s:14:"wpso_uninstall";s:59:"intuitive-custom-post-order/intuitive-custom-post-order.php";s:15:"hicpo_uninstall";}', 'no'),
(80, 'timezone_string', 'Europe/Paris', 'yes'),
(81, 'page_for_posts', '0', 'yes'),
(82, 'page_on_front', '2', 'yes'),
(83, 'default_post_format', '0', 'yes'),
(84, 'link_manager_enabled', '0', 'yes'),
(85, 'finished_splitting_shared_terms', '1', 'yes'),
(86, 'site_icon', '8', 'yes'),
(87, 'medium_large_size_w', '768', 'yes'),
(88, 'medium_large_size_h', '0', 'yes'),
(89, 'wp_page_for_privacy_policy', '3', 'yes'),
(90, 'show_comments_cookies_opt_in', '1', 'yes'),
(91, 'admin_email_lifespan', '1736608084', 'yes'),
(92, 'disallowed_keys', '', 'no'),
(93, 'comment_previously_approved', '1', 'yes'),
(94, 'auto_plugin_theme_update_emails', 'a:0:{}', 'no'),
(95, 'auto_update_core_dev', 'enabled', 'yes'),
(96, 'auto_update_core_minor', 'enabled', 'yes'),
(97, 'auto_update_core_major', 'enabled', 'yes'),
(98, 'wp_force_deactivated_plugins', 'a:0:{}', 'yes'),
(99, 'wp_attachment_pages_enabled', '0', 'yes'),
(100, 'initial_db_version', '57155', 'yes'),
(101, 'nd_user_roles', 'a:5:{s:13:"administrator";a:2:{s:4:"name";s:13:"Administrator";s:12:"capabilities";a:119:{s:13:"switch_themes";b:1;s:11:"edit_themes";b:1;s:16:"activate_plugins";b:1;s:12:"edit_plugins";b:1;s:10:"edit_users";b:1;s:10:"edit_files";b:1;s:14:"manage_options";b:1;s:17:"moderate_comments";b:1;s:17:"manage_categories";b:1;s:12:"manage_links";b:1;s:12:"upload_files";b:1;s:6:"import";b:1;s:15:"unfiltered_html";b:1;s:10:"edit_posts";b:1;s:17:"edit_others_posts";b:1;s:20:"edit_published_posts";b:1;s:13:"publish_posts";b:1;s:10:"edit_pages";b:1;s:4:"read";b:1;s:8:"level_10";b:1;s:7:"level_9";b:1;s:7:"level_8";b:1;s:7:"level_7";b:1;s:7:"level_6";b:1;s:7:"level_5";b:1;s:7:"level_4";b:1;s:7:"level_3";b:1;s:7:"level_2";b:1;s:7:"level_1";b:1;s:7:"level_0";b:1;s:17:"edit_others_pages";b:1;s:20:"edit_published_pages";b:1;s:13:"publish_pages";b:1;s:12:"delete_pages";b:1;s:19:"delete_others_pages";b:1;s:22:"delete_published_pages";b:1;s:12:"delete_posts";b:1;s:19:"delete_others_posts";b:1;s:22:"delete_published_posts";b:1;s:20:"delete_private_posts";b:1;s:18:"edit_private_posts";b:1;s:18:"read_private_posts";b:1;s:20:"delete_private_pages";b:1;s:18:"edit_private_pages";b:1;s:18:"read_private_pages";b:1;s:12:"delete_users";b:1;s:12:"create_users";b:1;s:17:"unfiltered_upload";b:1;s:14:"edit_dashboard";b:1;s:14:"update_plugins";b:1;s:14:"delete_plugins";b:1;s:15:"install_plugins";b:1;s:13:"update_themes";b:1;s:14:"install_themes";b:1;s:11:"update_core";b:1;s:10:"list_users";b:1;s:12:"remove_users";b:1;s:13:"promote_users";b:1;s:18:"edit_theme_options";b:1;s:13:"delete_themes";b:1;s:6:"export";b:1;s:24:"nestedpages_sorting_post";b:1;s:24:"nestedpages_sorting_page";b:1;s:25:"seopress_manage_dashboard";b:1;s:28:"seopress_manage_titles_metas";b:1;s:32:"seopress_manage_xml_html_sitemap";b:1;s:31:"seopress_manage_social_networks";b:1;s:25:"seopress_manage_analytics";b:1;s:24:"seopress_manage_advanced";b:1;s:21:"seopress_manage_tools";b:1;s:32:"seopress_manage_instant_indexing";b:1;s:19:"seopress_manage_pro";b:1;s:23:"seopress_manage_schemas";b:1;s:19:"seopress_manage_bot";b:1;s:23:"seopress_manage_license";b:1;s:27:"hicpo_hicpo_load_script_css";b:1;s:23:"hicpo_update_menu_order";b:1;s:28:"hicpo_update_menu_order_tags";b:1;s:29:"hicpo_update_menu_order_sites";b:1;s:16:"edit_redirection";b:1;s:17:"edit_redirections";b:1;s:24:"edit_others_redirections";b:1;s:20:"publish_redirections";b:1;s:16:"read_redirection";b:1;s:25:"read_private_redirections";b:1;s:18:"delete_redirection";b:1;s:19:"delete_redirections";b:1;s:26:"delete_others_redirections";b:1;s:29:"delete_published_redirections";b:1;s:11:"edit_schema";b:1;s:12:"edit_schemas";b:1;s:19:"edit_others_schemas";b:1;s:15:"publish_schemas";b:1;s:11:"read_schema";b:1;s:20:"read_private_schemas";b:1;s:13:"delete_schema";b:1;s:14:"delete_schemas";b:1;s:21:"delete_others_schemas";b:1;s:24:"delete_published_schemas";b:1;s:20:"edit_lesanimals_form";b:1;s:20:"read_lesanimals_form";b:1;s:22:"delete_lesanimals_form";b:1;s:21:"edit_lesanimals_forms";b:1;s:28:"edit_others_lesanimals_forms";b:1;s:29:"read_private_lesanimals_forms";b:1;s:21:"read_lesanimals_forms";b:1;s:23:"delete_lesanimals_forms";b:1;s:31:"delete_private_lesanimals_forms";b:1;s:33:"delete_published_lesanimals_forms";b:1;s:31:"delete_others_lesanimals_forms,";b:1;s:29:"edit_private_lesanimals_forms";b:1;s:31:"edit_published_lesanimals_forms";b:1;s:9:"edit_page";b:1;s:9:"read_page";b:1;s:11:"delete_page";b:1;s:10:"read_pages";b:1;s:20:"delete_others_pages,";b:1;s:12:"create_pages";b:1;s:21:"hicpo_load_script_css";b:1;}}s:6:"editor";a:2:{s:4:"name";s:6:"Editor";s:12:"capabilities";a:53:{s:17:"moderate_comments";b:1;s:17:"manage_categories";b:1;s:12:"manage_links";b:1;s:12:"upload_files";b:1;s:15:"unfiltered_html";b:1;s:10:"edit_posts";b:1;s:17:"edit_others_posts";b:1;s:20:"edit_published_posts";b:1;s:13:"publish_posts";b:1;s:10:"edit_pages";b:1;s:4:"read";b:1;s:7:"level_7";b:1;s:7:"level_6";b:1;s:7:"level_5";b:1;s:7:"level_4";b:1;s:7:"level_3";b:1;s:7:"level_2";b:1;s:7:"level_1";b:1;s:7:"level_0";b:1;s:17:"edit_others_pages";b:1;s:20:"edit_published_pages";b:1;s:13:"publish_pages";b:1;s:12:"delete_pages";b:1;s:19:"delete_others_pages";b:1;s:22:"delete_published_pages";b:1;s:12:"delete_posts";b:1;s:19:"delete_others_posts";b:1;s:22:"delete_published_posts";b:1;s:20:"delete_private_posts";b:1;s:18:"edit_private_posts";b:1;s:18:"read_private_posts";b:1;s:20:"delete_private_pages";b:1;s:18:"edit_private_pages";b:1;s:18:"read_private_pages";b:1;s:27:"hicpo_hicpo_load_script_css";b:1;s:23:"hicpo_update_menu_order";b:1;s:28:"hicpo_update_menu_order_tags";b:1;s:20:"edit_lesanimals_form";b:1;s:20:"read_lesanimals_form";b:1;s:21:"edit_lesanimals_forms";b:1;s:28:"edit_others_lesanimals_forms";b:1;s:29:"read_private_lesanimals_forms";b:1;s:21:"read_lesanimals_forms";b:1;s:29:"edit_private_lesanimals_forms";b:1;s:31:"edit_published_lesanimals_forms";b:1;s:9:"edit_page";b:1;s:9:"read_page";b:1;s:11:"delete_page";b:1;s:10:"read_pages";b:1;s:20:"delete_others_pages,";b:1;s:18:"edit_theme_options";b:1;s:18:"rocket_purge_cache";b:1;s:21:"hicpo_load_script_css";b:1;}}s:6:"author";a:2:{s:4:"name";s:6:"Author";s:12:"capabilities";a:10:{s:12:"upload_files";b:1;s:10:"edit_posts";b:1;s:20:"edit_published_posts";b:1;s:13:"publish_posts";b:1;s:4:"read";b:1;s:7:"level_2";b:1;s:7:"level_1";b:1;s:7:"level_0";b:1;s:12:"delete_posts";b:1;s:22:"delete_published_posts";b:1;}}s:11:"contributor";a:2:{s:4:"name";s:11:"Contributor";s:12:"capabilities";a:5:{s:10:"edit_posts";b:1;s:4:"read";b:1;s:7:"level_1";b:1;s:7:"level_0";b:1;s:12:"delete_posts";b:1;}}s:10:"subscriber";a:2:{s:4:"name";s:10:"Subscriber";s:12:"capabilities";a:2:{s:4:"read";b:1;s:7:"level_0";b:1;}}}', 'yes'),
(102, 'fresh_site', '0', 'yes'),
(103, 'user_count', '8', 'off'),
(104, 'widget_block', 'a:6:{i:2;a:1:{s:7:"content";s:19:"<!-- wp:search /-->";}i:3;a:1:{s:7:"content";s:154:"<!-- wp:group --><div class="wp-block-group"><!-- wp:heading --><h2>Recent Posts</h2><!-- /wp:heading --><!-- wp:latest-posts /--></div><!-- /wp:group -->";}i:4;a:1:{s:7:"content";s:227:"<!-- wp:group --><div class="wp-block-group"><!-- wp:heading --><h2>Recent Comments</h2><!-- /wp:heading --><!-- wp:latest-comments {"displayAvatar":false,"displayDate":false,"displayExcerpt":false} /--></div><!-- /wp:group -->";}i:5;a:1:{s:7:"content";s:146:"<!-- wp:group --><div class="wp-block-group"><!-- wp:heading --><h2>Archives</h2><!-- /wp:heading --><!-- wp:archives /--></div><!-- /wp:group -->";}i:6;a:1:{s:7:"content";s:150:"<!-- wp:group --><div class="wp-block-group"><!-- wp:heading --><h2>Categories</h2><!-- /wp:heading --><!-- wp:categories /--></div><!-- /wp:group -->";}s:12:"_multiwidget";i:1;}', 'yes') ;
INSERT INTO `nd_options` ( `option_id`, `option_name`, `option_value`, `autoload`) VALUES
(105, 'sidebars_widgets', 'a:4:{s:19:"wp_inactive_widgets";a:0:{}s:9:"sidebar-1";a:3:{i:0;s:7:"block-2";i:1;s:7:"block-3";i:2;s:7:"block-4";}s:9:"sidebar-2";a:2:{i:0;s:7:"block-5";i:1;s:7:"block-6";}s:13:"array_version";i:3;}', 'yes'),
(106, 'bedrock_autoloader', 'a:2:{s:7:"plugins";a:1:{s:55:"bedrock-disallow-indexing/bedrock-disallow-indexing.php";a:15:{s:4:"Name";s:17:"Disallow Indexing";s:9:"PluginURI";s:25:"https://roots.io/bedrock/";s:7:"Version";s:5:"2.0.0";s:11:"Description";s:62:"Disallow indexing of your site on non-production environments.";s:6:"Author";s:5:"Roots";s:9:"AuthorURI";s:17:"https://roots.io/";s:10:"TextDomain";s:5:"roots";s:10:"DomainPath";s:0:"";s:7:"Network";b:0;s:10:"RequiresWP";s:0:"";s:11:"RequiresPHP";s:0:"";s:9:"UpdateURI";s:0:"";s:15:"RequiresPlugins";s:0:"";s:5:"Title";s:17:"Disallow Indexing";s:10:"AuthorName";s:5:"Roots";}}s:5:"count";i:1;}', 'off'),
(107, 'cron', 'a:11:{i:1728375132;a:1:{s:30:"seopress_google_analytics_cron";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:6:"hourly";s:4:"args";a:0:{}s:8:"interval";i:3600;}}}i:**********;a:1:{s:34:"wp_privacy_delete_old_export_files";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:6:"hourly";s:4:"args";a:0:{}s:8:"interval";i:3600;}}}i:**********;a:1:{s:30:"seopress_matomo_analytics_cron";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:6:"hourly";s:4:"args";a:0:{}s:8:"interval";i:3600;}}}i:**********;a:4:{s:30:"wp_site_health_scheduled_check";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:6:"weekly";s:4:"args";a:0:{}s:8:"interval";i:604800;}}s:16:"wp_version_check";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:10:"twicedaily";s:4:"args";a:0:{}s:8:"interval";i:43200;}}s:17:"wp_update_plugins";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:10:"twicedaily";s:4:"args";a:0:{}s:8:"interval";i:43200;}}s:16:"wp_update_themes";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:10:"twicedaily";s:4:"args";a:0:{}s:8:"interval";i:43200;}}}i:**********;a:3:{s:19:"wp_scheduled_delete";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:5:"daily";s:4:"args";a:0:{}s:8:"interval";i:86400;}}s:25:"delete_expired_transients";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:5:"daily";s:4:"args";a:0:{}s:8:"interval";i:86400;}}s:21:"wp_update_user_counts";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:10:"twicedaily";s:4:"args";a:0:{}s:8:"interval";i:43200;}}}i:1728400159;a:1:{s:30:"wp_scheduled_auto_draft_delete";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:5:"daily";s:4:"args";a:0:{}s:8:"interval";i:86400;}}}i:1728400332;a:4:{s:26:"seopress_insights_gsc_cron";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:5:"daily";s:4:"args";a:0:{}s:8:"interval";i:86400;}}s:26:"seopress_404_cron_cleaning";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:5:"daily";s:4:"args";a:0:{}s:8:"interval";i:86400;}}s:33:"seopress_page_speed_insights_cron";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:5:"daily";s:4:"args";a:0:{}s:8:"interval";i:86400;}}s:20:"seopress_alerts_cron";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:10:"twicedaily";s:4:"args";a:0:{}s:8:"interval";i:43200;}}}i:**********;a:1:{s:27:"acf_update_site_health_data";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:6:"weekly";s:4:"args";a:0:{}s:8:"interval";i:604800;}}}i:**********;a:1:{s:30:"wp_delete_temp_updater_backups";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:6:"weekly";s:4:"args";a:0:{}s:8:"interval";i:604800;}}}i:**********;a:1:{s:30:"seopress_404_email_alerts_cron";a:1:{s:32:"40cd750bba9870f18aada2478b24840a";a:3:{s:8:"schedule";s:6:"weekly";s:4:"args";a:0:{}s:8:"interval";i:604800;}}}s:7:"version";i:2;}', 'yes'),
(108, 'widget_pages', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(109, 'widget_calendar', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(110, 'widget_archives', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(111, 'widget_media_audio', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(112, 'widget_media_image', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(113, 'widget_media_gallery', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(114, 'widget_media_video', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(115, 'widget_meta', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(116, 'widget_search', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(117, 'widget_recent-posts', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(118, 'widget_recent-comments', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(119, 'widget_tag_cloud', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(120, 'widget_nav_menu', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(121, 'widget_custom_html', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(123, 'acf_first_activated_version', '*******', 'yes'),
(124, 'acf_site_health', '{"version":"6.3.7","plugin_type":"PRO","activated":true,"activated_url":"https:\\/\\/numeridanse.local","license_type":"Developer","license_status":"active","subscription_expires":"","wp_version":"6.6.2","mysql_version":"8.0.37-29","is_multisite":false,"active_theme":{"name":"Les Animals","version":"4.0.0","theme_uri":"https:\\/\\/lesanimals.digital","stylesheet":false},"active_plugins":{"acf-extended-pro\\/acf-extended.php":{"name":"Advanced Custom Fields: Extended PRO","version":"*******","plugin_uri":"https:\\/\\/www.acf-extended.com"},"advanced-custom-fields-pro\\/acf.php":{"name":"Advanced Custom Fields PRO","version":"6.3.7","plugin_uri":"https:\\/\\/www.advancedcustomfields.com"},"enable-media-replace\\/enable-media-replace.php":{"name":"Enable Media Replace","version":"4.1.5","plugin_uri":"https:\\/\\/wordpress.org\\/plugins\\/enable-media-replace\\/"},"manage-privacy-options\\/baw-manage-privacy-options.php":{"name":"Manage Privacy Options","version":"1.1","plugin_uri":""},"wp-nested-pages\\/nestedpages.php":{"name":"Nested Pages","version":"3.2.9","plugin_uri":"http:\\/\\/nestedpages.com"},"password-protected\\/password-protected.php":{"name":"Password Protected","version":"2.7.3","plugin_uri":"https:\\/\\/wordpress.org\\/plugins\\/password-protected\\/"},"safe-svg\\/safe-svg.php":{"name":"Safe SVG","version":"2.2.6","plugin_uri":"https:\\/\\/wordpress.org\\/plugins\\/safe-svg\\/"},"wp-seopress\\/seopress.php":{"name":"SEOPress","version":"8.2","plugin_uri":"https:\\/\\/www.seopress.org\\/"},"wp-seopress-pro\\/seopress-pro.php":{"name":"SEOPress PRO","version":"8.2.1","plugin_uri":"https:\\/\\/www.seopress.org\\/seopress-pro\\/"},"spatie-ray\\/wp-ray.php":{"name":"Spatie Ray","version":"1.7.6","plugin_uri":"https:\\/\\/github.com\\/spatie\\/wordpress-ray"},"user-switching\\/user-switching.php":{"name":"User Switching","version":"1.8.0","plugin_uri":"https:\\/\\/wordpress.org\\/plugins\\/user-switching\\/"},"admin-bar-user-switching\\/admin-bar-user-switching.php":{"name":"User Switching in Admin Bar","version":"1.4","plugin_uri":"https:\\/\\/wpexperts.io"},"wp-migrate-db-pro\\/wp-migrate-db-pro.php":{"name":"WP Migrate","version":"2.7.0","plugin_uri":"https:\\/\\/deliciousbrains.com\\/wp-migrate-db-pro\\/"},"wp-sort-order\\/index.php":{"name":"WP Sort Order","version":"1.3.3","plugin_uri":"http:\\/\\/androidbubble.com\\/blog\\/wordpress\\/plugins\\/wp-sort-order"}},"ui_field_groups":"0","php_field_groups":"0","json_field_groups":"0","rest_field_groups":"0","number_of_fields_by_type":{"wysiwyg":3,"image":8,"text":7,"gallery":1,"repeater":4,"post_object":4,"radio":4,"textarea":4,"acfe_advanced_link":1,"relationship":5,"tab":11,"user":1,"acfe_dynamic_render":3,"taxonomy":7,"true_false":3,"message":1,"acfe_date_range_picker":1,"acfe_slug":1,"flexible_content":1,"group":3},"number_of_third_party_fields_by_type":[],"post_types_enabled":true,"ui_post_types":"8","json_post_types":"0","ui_taxonomies":"10","json_taxonomies":"0","ui_options_pages_enabled":true,"ui_options_pages":"0","json_options_pages":"0","php_options_pages":"1","rest_api_format":"light","registered_acf_blocks":"0","blocks_per_api_version":[],"blocks_per_acf_block_version":[],"blocks_using_post_meta":"0","preload_blocks":true,"admin_ui_enabled":true,"field_type-modal_enabled":true,"field_settings_tabs_enabled":false,"shortcode_enabled":false,"registered_acf_forms":"0","json_save_paths":1,"json_load_paths":1,"event_first_activated_pro":1721056086,"event_first_created_field_group":1721311113,"last_updated":1728313714}', 'off'),
(127, 'seopress_toggle', 'a:15:{s:13:"toggle-titles";s:1:"1";s:18:"toggle-xml-sitemap";s:1:"1";s:13:"toggle-social";s:1:"1";s:23:"toggle-google-analytics";s:1:"1";s:23:"toggle-instant-indexing";s:1:"1";s:15:"toggle-advanced";s:1:"1";s:18:"toggle-dublin-core";s:1:"1";s:21:"toggle-local-business";s:1:"1";s:20:"toggle-rich-snippets";s:1:"1";s:18:"toggle-breadcrumbs";s:1:"1";s:13:"toggle-robots";s:1:"1";s:10:"toggle-404";s:1:"1";s:10:"toggle-bot";s:1:"1";s:18:"toggle-inspect-url";s:1:"1";s:9:"toggle-ai";s:1:"1";}', 'yes'),
(128, 'seopress_titles_option_name', 'a:14:{s:31:"seopress_titles_home_site_title";s:13:"%%sitetitle%%";s:30:"seopress_titles_home_site_desc";s:11:"%%tagline%%";s:19:"seopress_titles_sep";s:1:"-";s:29:"seopress_titles_single_titles";a:2:{s:4:"post";a:2:{s:5:"title";s:36:"%%post_title%% %%sep%% %%sitetitle%%";s:11:"description";s:16:"%%post_excerpt%%";}s:4:"page";a:2:{s:5:"title";s:36:"%%post_title%% %%sep%% %%sitetitle%%";s:11:"description";s:16:"%%post_excerpt%%";}}s:30:"seopress_titles_archive_titles";a:2:{s:4:"post";a:1:{s:5:"title";s:45:"%%cpt_plural%% %%current_pagination%% %%sep%%";}s:4:"page";a:1:{s:5:"title";s:45:"%%cpt_plural%% %%current_pagination%% %%sep%%";}}s:37:"seopress_titles_archives_author_title";s:37:"%%post_author%% %%sep%% %%sitetitle%%";s:39:"seopress_titles_archives_author_noindex";s:1:"1";s:35:"seopress_titles_archives_date_title";s:38:"%%archive_date%% %%sep%% %%sitetitle%%";s:37:"seopress_titles_archives_date_noindex";s:1:"1";s:37:"seopress_titles_archives_search_title";s:41:"%%search_keywords%% %%sep%% %%sitetitle%%";s:45:"seopress_titles_archives_search_title_noindex";s:1:"1";s:34:"seopress_titles_archives_404_title";s:42:"404 - Page not found %%sep%% %%sitetitle%%";s:25:"seopress_titles_paged_rel";s:1:"1";s:35:"seopress_titles_attachments_noindex";s:1:"1";}', 'yes'),
(129, 'seopress_xml_sitemap_option_name', 'a:4:{s:35:"seopress_xml_sitemap_general_enable";s:1:"1";s:31:"seopress_xml_sitemap_img_enable";s:1:"1";s:36:"seopress_xml_sitemap_post_types_list";a:2:{s:4:"post";a:1:{s:7:"include";s:1:"1";}s:4:"page";a:1:{s:7:"include";s:1:"1";}}s:36:"seopress_xml_sitemap_taxonomies_list";a:2:{s:8:"category";a:1:{s:7:"include";s:1:"1";}s:8:"post_tag";a:1:{s:7:"include";s:1:"1";}}}', 'yes'),
(130, 'seopress_social_option_name', 'a:2:{s:27:"seopress_social_facebook_og";s:1:"1";s:28:"seopress_social_twitter_card";s:1:"1";}', 'yes'),
(131, 'seopress_advanced_option_name', 'a:6:{s:38:"seopress_advanced_advanced_attachments";s:1:"1";s:42:"seopress_advanced_advanced_tax_desc_editor";s:1:"1";s:38:"seopress_advanced_appearance_score_col";s:1:"1";s:54:"seopress_advanced_appearance_universal_metabox_disable";s:1:"1";s:41:"seopress_advanced_advanced_clean_filename";i:1;s:46:"seopress_advanced_appearance_metaboxe_position";s:3:"low";}', 'yes'),
(143, 'wp_migrate_addon_schema', '1', 'yes'),
(146, 'WPLANG', 'fr_FR', 'yes'),
(152, 'nestedpages_posttypes', 'a:1:{s:4:"page";a:1:{s:12:"replace_menu";b:1;}}', 'yes'),
(153, 'nestedpages_version', '3.2.9', 'yes'),
(154, 'nestedpages_menusync', 'nosync', 'yes'),
(160, 'acf_version', '6.3.7', 'yes'),
(168, 'seopress_versions', 'a:2:{s:4:"free";s:3:"8.2";s:3:"pro";s:5:"8.2.1";}', 'off'),
(169, 'seopress_instant_indexing_option_name', 'a:2:{s:38:"seopress_instant_indexing_bing_api_key";s:44:"********************************************";s:45:"seopress_instant_indexing_automate_submission";s:1:"1";}', 'yes'),
(170, 'acf_pro_license', 'YToyOntzOjM6ImtleSI7czo3NjoiYjNKa1pYSmZhV1E5TVRNME1qVXpmSFI1Y0dVOVpHVjJaV3h2Y0dWeWZHUmhkR1U5TWpBeE9DMHdOeTB3TWlBd09Ub3hOem8xTkE9PSI7czozOiJ1cmwiO3M6MjU6Imh0dHBzOi8vbnVtZXJpZGFuc2UubG9jYWwiO30=', 'off'),
(183, 'can_compress_scripts', '0', 'yes'),
(194, 'recently_activated', 'a:0:{}', 'yes'),
(199, 'acfe', 'a:3:{s:7:"version";s:7:"*******";s:7:"modules";a:5:{s:11:"block_types";a:0:{}s:13:"options_pages";a:0:{}s:10:"post_types";a:0:{}s:10:"taxonomies";a:0:{}s:7:"scripts";a:1:{s:20:"lesanimals_migration";a:5:{s:25:"field_item_type_to_import";s:9:"pub_piece";s:33:"field_item_number_to_import_total";s:1:"1";s:37:"field_item_number_to_import_per_batch";s:1:"1";s:17:"field_item_offset";s:1:"0";s:14:"field_language";s:2:"fr";}}}s:7:"license";s:48:"a2V5PTc5ODg4ZWU0ZWM1MTdkYTMwM2MxY2ZmNjRiYWRkZWM5";}', 'auto'),
(200, 'theme_mods_lesanimals', 'a:2:{s:18:"custom_css_post_id";i:-1;s:18:"nav_menu_locations";a:3:{s:13:"menu-footer-1";i:4;s:13:"menu-footer-2";i:5;s:18:"menu-footer-legals";i:6;}}', 'yes'),
(204, 'finished_updating_comment_type', '1', 'yes'),
(213, 'seopress_pro_activated', 'yes', 'yes'),
(214, 'widget_seopress_pro_lb_widget', 'a:1:{s:12:"_multiwidget";i:1;}', 'yes'),
(217, 'seopress_pro_license_home_url', 'https://numeridanse.local', 'yes'),
(218, 'seopress_pro_license_automatic_attempt', '1', 'yes'),
(219, 'seopress_pro_license_status', 'valid', 'yes'),
(220, 'seopress_pro_option_name', 'a:1:{s:27:"seopress_dublin_core_enable";s:1:"1";}', 'yes'),
(221, 'seopress_bot_option_name', 'a:2:{s:37:"seopress_bot_scan_settings_post_types";a:2:{s:4:"post";a:1:{s:7:"include";s:1:"1";}s:4:"page";a:1:{s:7:"include";s:1:"1";}}s:30:"seopress_bot_scan_settings_404";s:1:"1";}', 'yes') ;
INSERT INTO `nd_options` ( `option_id`, `option_name`, `option_value`, `autoload`) VALUES
(224, 'edd_sl_c282db7728782eb7971d9be3cd23e786', 'a:2:{s:7:"timeout";i:**********;s:5:"value";s:36752:"{"new_version":"8.2.2","stable_version":"8.2.2","name":"SEOPress PRO","slug":"seopress-pro","url":"https:\\/\\/www.seopress.org\\/fr\\/downloads\\/seopress-pro\\/?changelog=1","last_updated":"2024-10-07 13:52:35","homepage":"https:\\/\\/www.seopress.org\\/","package":"https:\\/\\/www.seopress.org\\/fr\\/edd-sl\\/package_download\\/MTcyODQ2NDEzOTpmNmVlMzk4Nzc3OGNkM2I4ODg3ODQ3N2Y2YmU0MmU3NDoxMTU6NGE3YmFjYTNiOWExMzdhMDkyN2YzNjgzMjdjZjFhYjA6aHR0cHNALy9udW1lcmlkYW5zZS5sb2NhbDow","download_link":"https:\\/\\/www.seopress.org\\/fr\\/edd-sl\\/package_download\\/MTcyODQ2NDEzOTpmNmVlMzk4Nzc3OGNkM2I4ODg3ODQ3N2Y2YmU0MmU3NDoxMTU6NGE3YmFjYTNiOWExMzdhMDkyN2YzNjgzMjdjZjFhYjA6aHR0cHNALy9udW1lcmlkYW5zZS5sb2NhbDow","sections":{"description":"<h3>Best SEO plugin for WordPress fully integrated with all page builders and themes!<\\/h3>\\n<h3>Now with AI (GPT 4) to automagically generate meta title, description and alternative texts for images!<\\/h3>\\n<p>SEOPress is a powerful WordPress SEO plugin to optimize your SEO, boost your traffic, improve social sharing, build custom HTML and XML Sitemaps, create optimized breadcrumbs, add schemas \\/ Google Structured data types, manage 301 redirections and so much more.<br \\/><\\/p>\\n<p>&#x2714; <strong><a href=\\"https:\\/\\/www.seopress.org\\/features\\/page-builders-integration\\/\\">Universal SEO metabox<\\/a>: edit all your SEO from any page builder \\/ theme builder. No more back and forth between your editor and the WordPress administration<\\/strong>\\n&#x2714; <strong>No advertising, no footprints, white label, in backend AND frontend<\\/strong>\\n&#x2714; <strong>Content analysis to help you write content optimized for search engines with unlimited target keywords<\\/strong>\\n&#x2714; <strong><a href=\\"https:\\/\\/www.seopress.org\\/solutions\\/migrate-from\\/\\">Import your post and term metadatas<\\/a> from other SEO plugins or CSV file in 1 click<\\/strong>\\n&#x2714; <strong><a href=\\"https:\\/\\/translate.wordpress.org\\/projects\\/wp-plugins\\/wp-seopress\\">Translated into 25 languages (and counting)<\\/a><\\/strong>\\n&#x2714; <strong>Trusted by over 300,000 WordPress websites since 2017<\\/strong><\\/p>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Buy SEOPress PRO: only $49 \\/ year \\/ 5 sites<\\/strong><\\/a><\\/p>\\n<p>[youtube <a href=\\"https:\\/\\/www.youtube.com\\/watch?v=4ysKFVr_nu0\\">https:\\/\\/www.youtube.com\\/watch?v=4ysKFVr_nu0<\\/a>]<\\/p>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/features\\/\\">Features<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/solutions\\/migrate-from\\/\\">Migrate<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/wordpress-seo-plugins\\/pro\\/\\">PRO<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/integrations\\/\\">Integrations<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/support\\/\\">Support<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/features\\/seopress-white-label\\/\\">White Label<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/features\\/openai\\/\\">AI<\\/a><\\/p>\\n<h3>Why SEOPress is the best WordPress SEO plugin?<\\/h3>\\n<ul>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/seopress-productivity\\/\\"><strong>Save time<\\/strong><\\/a>: you prefer to work with Excel or Google Spreadsheets? No problem, you can import \\/ export your metadata from CSV files with SEOPress PRO in few clicks!<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Save money<\\/strong><\\/a>: SEOPress PRO is available for $49 \\/ year \\/ 5 sites. Go unlimited sites for just $149 \\/ year!<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/\\"><strong>All in one SEO plugin<\\/strong><\\/a>: comes with all the features you need to optimize the SEO of your WordPress site. No need to install additional extensions to manage redirects, schemas, XML sitemaps... You reduce the risk of conflicts and maintenance costs. You don\'t need a special feature? Deactivate it with one click without losing your configuration. Child\'s play !<\\/li>\\n    <li><strong>Easy AND ready to use<\\/strong>: you doesn\'t need to know SEO or code to use SEOPress. Most of the parameters are automatically set. And thanks to our installation wizard, configuring SEOPress has never been easier. To go further, we provide many <a href=\\"https:\\/\\/www.seopress.org\\/support\\/\\">free tutorials<\\/a> and <a href=\\"https:\\/\\/www.seopress.org\\/support\\/ebooks\\/\\">ebooks to learn SEO<\\/a> in order to better understand how to position your content on search engines.<\\/li>\\n<\\/ul>\\n<h3>SEOPress Free Features<\\/h3>\\n<ul>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/google-indexing-api-with-seopress\\/\\"><strong>Google Indexing API and IndexNow API (Bing \\/ Yandex)<\\/strong><\\/a> to quickly index its content in these search engines<\\/li>\\n    <li><strong>Installation wizard<\\/strong> to quickly setup your site<\\/li>\\n    <li><strong>Content analysis<\\/strong> with unlimited keywords to help you write optimized content for search engines<\\/li>\\n    <li><strong>Mobile \\/ Desktop Google Preview<\\/strong> to see how your post will looks like in Google search results<\\/li>\\n    <li><strong>Facebook &amp; X (ex-Twitter) Social Preview<\\/strong> to see how your post will looks like on social media to increase conversions<\\/li>\\n    <li><strong>Titles<\\/strong> (with <a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/manage-titles-meta-descriptions\\/\\">dynamic variables<\\/a>: custom fields, terms taxonomie...)<\\/li>\\n    <li><strong>Meta descriptions<\\/strong> (with dynamic variables too)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/social-media\\/\\"><strong>Open Graph and X (ex-Twitter) Cards<\\/strong><\\/a> to improve social media sharing (Facebook, LinkedIn, Instagram, X (ex-Twitter), Pinterest, WhatsApp...)<\\/li>\\n    <li><strong>Google Knowledge Graph<\\/strong><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-analytics\\/\\"><strong>Google Analytics<\\/strong> and <strong>Matomo<\\/strong><\\/a> with downloads tracking, custom dimensions, ip anonymization, remarketing, demographics and interest reporting, cross-domain tracking...(<a href=\\"https:\\/\\/www.seopress.org\\/features\\/seopress-white-label\\/\\">GDPR compatibility<\\/a>)<\\/li>\\n    <li><strong>Microsoft Clarity integration<\\/strong>: to capture session recordings, get instant heatmaps and powerful Insights for Free. Know how people interact with your site to improve user experience and conversions<\\/li>\\n    <li><strong>Custom Canonical URL<\\/strong><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/manage-meta-robots\\/\\"><strong>Meta robots<\\/strong><\\/a> (noindex, nofollow, noimageindex, noarchive, nosnippet)<\\/li>\\n    <li>Build your <a href=\\"https:\\/\\/www.seopress.org\\/features\\/sitemaps\\/\\"><strong>custom XML Sitemap<\\/strong><\\/a> to improve search indexing<\\/li>\\n    <li><strong>Image XML Sitemaps<\\/strong> to improve search indexing for Google Images<\\/li>\\n    <li>Build your custom <strong>HTML Sitemap<\\/strong> to enhanced navigation for visitors and improve search indexing<\\/li>\\n    <li>Link your social media accounts to your site<\\/li>\\n    <li><strong>Redirections<\\/strong> in post, pages, custom post types<\\/li>\\n    <li>Remove \\/category\\/ in URLs<\\/li>\\n    <li>Remove \\/product-category\\/ in URLs<\\/li>\\n    <li>Remove ?replytocom to avoid duplicate content<\\/li>\\n    <li>Redirect attachment pages to post parent<\\/li>\\n    <li>Redirect attachment pages to their file URL<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/image-seo\\/\\"><strong>Image SEO<\\/strong><\\/a>: Automatically set the image title \\/ alt \\/ caption \\/ description<\\/li>\\n    <li>Import \\/ Export settings from site to site.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/solutions\\/migrate-from\\/\\">Import posts and terms metadata<\\/a> from Yoast SEO, All In One SEO, SEO Framework, Rank Math, SEO Ultimate, WP Meta SEO, Premium SEO Pack, Squirrly and many other SEO plugins<\\/li>\\n    <li>...<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/wordpress-seo-plugins\\/free\\/features\\/\\">Check out all SEOPress Free features here<\\/a><\\/p>\\n<h3>SEOPress PRO: to go further with your SEO<\\/h3>\\n<p>[youtube <a href=\\"https:\\/\\/youtu.be\\/zxGCY-bJYwE\\">https:\\/\\/youtu.be\\/zxGCY-bJYwE<\\/a>]<\\/p>\\n<ul>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/openai\\/\\"><strong>Use Artificial Intelligence (GPT 4 \\/ Vision and GPT 3.5 Turbo) to generate SEO metadata and alternative texts for image files. Bulk actions supported.<\\/strong><\\/a><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/site-audit\\/\\"><strong>Site Audit: Detect technical issues that could harm your site\'s SEO directly from your WordPress administration.<\\/strong><\\/a><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/seo-alerts\\/\\">Receive SEO alerts to prevent breaking your SEO before it\'s too late<\\/a><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/import-metadata-from-a-csv-file-with-seopress-pro\\/\\"><strong>Import \\/ export metadata<\\/strong><\\/a> (titles, open graph, robots...) from \\/ to CSV file<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/sitemaps\\/\\"><strong>Video XML Sitemap<\\/strong><\\/a> to improve rankings in video search results. YouTube videos are automatically added.<\\/li>\\n    <li>Internal linking suggestions<\\/li>\\n    <li>Inspect URL with Google Search Console: get details about crawling, indexing, mobile compatibility, schemas and more.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-search-console\\/\\"><strong>Search Console integration<\\/strong><\\/a>: get insights from your post \\/ page \\/ post type list with clicks, positions, CTR and impressions.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-suggest\\/\\"><strong>Google Suggestions in Content Analysis<\\/strong><\\/a> to find the top 10 Google suggestions instantly. This is useful if you want to work with the long tail technique.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-structured-data-types\\/\\"><strong>Google Structured Data types<\\/strong> (schema.org)<\\/a>:\\n        <ol>\\n            <li>article<\\/li>\\n            <li>local business<\\/li>\\n            <li>service<\\/li>\\n            <li>how-to<\\/li>\\n            <li>FAQ<\\/li>\\n            <li>course<\\/li>\\n            <li>recipe<\\/li>\\n            <li>software application<\\/li>\\n            <li>video<\\/li>\\n            <li>event<\\/li>\\n            <li>product<\\/li>\\n            <li>job<\\/li>\\n            <li>simple review<\\/li>\\n            <li>site navigation element<\\/li>\\n            <li>custom schema<\\/li>\\n        <\\/ol>\\n    <\\/li><li><strong>Automatic Schemas<\\/strong> with advanced conditions (AND, OR, Post types, taxonomies)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/breadcrumbs\\/\\"><strong>Breadcrumbs<\\/strong><\\/a> optimized with Schema.org, A11Y ready.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-analytics\\/\\"><strong>Google Analytics Stats in Dashboard<\\/strong><\\/a> to quickly see your metrics without leaving your site<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/local-seo\\/\\"><strong>Google Local Business<\\/strong><\\/a> to boost your local store<\\/li>\\n    <li><strong>Broken link checker (SEOPress BOT)<\\/strong>: scan all your links in content to find errors (e.g. 404...)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/woocommerce-seo\\/\\"><strong>WooCommerce<\\/strong><\\/a>: Product Global Identifiers type and Product Global Identifiers fields to WooCommerce metabox for product schema (barcode), disable crawling on cart page, checkout page, customer account pages, add OG Price \\/ OG Currency for better sharing and more<\\/li>\\n    <li><strong>Easy Digital Downloads<\\/strong>: add OG Price \\/ OG Currency, remove EDD meta generator<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/breadcrumbs\\/\\"><strong>Custom Breadcrumbs<\\/strong><\\/a> for single post types \\/ term taxonomy<\\/li>\\n    <li><strong>Google Page Speed Insights<\\/strong> to analyse your site performances on Mobile \\/ Desktop + your Core Web Vitals<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-analytics\\/\\"><strong>Google Enhanced Ecommerce for WooCommerce<\\/strong><\\/a>: measure purchases, singular product view details, additions to and removals from shopping carts<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/htaccess-robots-txt\\/\\">Edit your <strong>robots.txt<\\/strong><\\/a> file from the admin (multisite \\/ multidomain ready)<\\/li>\\n    <li><strong>Google News Sitemap<\\/strong> to get your posts on Google News<\\/li>\\n    <li><strong>404 Monitoring<\\/strong>: Monitor your 404 errors to improve user experience, performances and increase the crawl budget allocated by Google<\\/li>\\n    <li><strong>Redirect 404 to homepage\\/custom url automatically<\\/strong> with custom status code (301, 302, 307, 410 or 451)<\\/li>\\n    <li>Email notifications on 404<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/301-redirects\\/\\"><strong>Redirect manager<\\/strong><\\/a>: create unlimited 301, 302, 307, 410 and 451 redirections. Regular expressions supported. Import \\/ export redirections to CSV or htaccess file.<\\/li>\\n    <li>Import redirections using CSV<\\/li>\\n    <li>Import redirections from Redirections plugin (via a JSON file)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/htaccess-robots-txt\\/\\">Edit your <strong>htaccess file<\\/strong><\\/a> from the admin<\\/li>\\n    <li>Easily customize your <strong>RSS feeds<\\/strong><\\/li>\\n    <li>...<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Buy SEOPress PRO now!<\\/strong><\\/a><\\/p>\\n<h3>WooCommerce SEO (SEOPress PRO required)<\\/h3>\\nWe support WooCommerce and Easy Digital Downloads for e-commerce sites.\\n<ul>\\n    <li>Price and currency meta tags to improve social sharing<\\/li>\\n    <li>XML sitemaps for products<\\/li>\\n    <li>Support for WooCommerce product images and WooCommerce image galleries for the XML sitemap<\\/li>\\n    <li>Centralized way to set noindex meta robots tags on pages like cart, checkout...<\\/li>\\n    <li>Remove WooCommerce generator meta tag in the source code<\\/li>\\n    <li>Create manual and\\/or automatic \\"product\\" schemas in JSON-LD to increase visibility in Google search results<\\/li>\\n    <li>WooCommerce support for our breadcrumbs<\\/li>\\n    <li>Global dynamic tags to insert in your metas titles \\/ descriptions<\\/li>\\n    <li>Product Global Identifiers type and Product Global Identifiers fields to WooCommerce metabox for product schema (barcode)<\\/li>\\n    <li>...<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Increase your sales now!<\\/strong><\\/a><\\/p>\\n<h3><a href=\\"https:\\/\\/www.seopress.org\\/features\\/page-builders-integration\\/\\">Universal SEO metabox<\\/a>: edit your metadata from any page builder \\/ editor<\\/h3>\\n<p>[youtube <a href=\\"https:\\/\\/youtu.be\\/sf0ocG7vQMM\\">https:\\/\\/youtu.be\\/sf0ocG7vQMM<\\/a>]<\\/p>\\n<h3>Elementor + SEOPress: perfect combo!<\\/h3>\\nWe provide deep integration with Elementor page builder UI, see below:\\n<p>[youtube <a href=\\"https:\\/\\/www.youtube.com\\/watch?v=oC5QZ0_TH_g\\">https:\\/\\/www.youtube.com\\/watch?v=oC5QZ0_TH_g<\\/a>]<\\/p>\\n<h3>SEOPress Insights: Off-site SEO plugin to track your rankings and backlinks in WordPress<\\/h3>\\n<ul>\\n    <li><strong>Keyword rank tracker<\\/strong>: 51 Google Search locations available<\\/li>\\n    <li>Track <strong>50 keywords<\\/strong> per site daily<\\/li>\\n    <li>Track your <strong>competitors<\\/strong>: who ranks first on your keywords<\\/li>\\n    <li>Monitor and analyze your <strong>backlinks<\\/strong> weekly<\\/li>\\n    <li><strong>Google trends<\\/strong> to find new and relevant ideas for your content marketing strategy<\\/li>\\n    <li>Your <strong>data accessible for life<\\/strong>: export it to a CSV, PDF or Excel file. Sort, order, filter your data right from your WordPress.<\\/li>\\n    <li>Receive <strong>email and Slack alerts<\\/strong> for your rankings to easily follow them<\\/li>\\n<\\/ul>\\n<p>[youtube <a href=\\"https:\\/\\/youtu.be\\/p6v9Jd5lRIU\\">https:\\/\\/youtu.be\\/p6v9Jd5lRIU<\\/a>]<\\/p>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Buy SEOPress Insights now!<\\/strong><\\/a><\\/p>\\n<h3>Developers will love SEOPress!<\\/h3>\\n<ul>\\n    <li>Hundreds of hooks are available to extend SEOPress. <a href=\\"https:\\/\\/www.seopress.org\\/support\\/hooks\\/\\">Browse them all here<\\/a>!<\\/li>\\n    <li>Plus we have a <a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/get-started-with-the-seopress-rest-api\\/\\">REST API<\\/a> to build static websites.<\\/li>\\n    <li>Finally, <a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/seopress-wp-cli\\/\\"><strong>WP CLI commands<\\/strong><\\/a> are available.<\\/li>\\n<\\/ul>","changelog":"<h4> 8.2 <a href=\\"https:\\/\\/www.seopress.org\\/newsroom\\/product-news\\/seopress-8-2\\/\\">Read the blog post update<\\/a> <\\/h4>\\n<ul>\\n<li>NEW Site Audit: Detect technical issues that could harm your site\'s SEO directly from your WordPress administration (<a href=\\"https:\\/\\/www.seopress.org\\/features\\/site-audit\\/\\">https:\\/\\/www.seopress.org\\/features\\/site-audit\\/<\\/a>) &#x1f389;<\\/li>\\n<li>NEW Add a notice to the Block Editor on slug changes to quickly create a redirection to keep your SEO (PRO)<\\/li>\\n<li>INFO Table of contents Block: allow \\"paragraph\\" \\/ \\"div\\" for the title of the block<\\/li>\\n<li>INFO Add notice to robots.txt settings tab if a physical file is already present on your server<\\/li>\\n<li>INFO Support robots.txt file for WP multisite (subdirectories installation with custom domains)<\\/li>\\n<li>INFO Strengthened security<\\/li>\\n<li>FIX Wizard redirect when updating SEOPress PRO<\\/li>\\n<li>FIX Internal links list in standard content analysis metabox<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/changelog\\/\\">View our complete changelog<\\/a>\\n<a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/how-to-downgrade-seopress-pro-to-a-previous-version\\/\\">Need to downgrade\\/rollback?<\\/a><\\/p>","installation":"<ol>\\n<li>Upload \'wp-seopress\' to the \'\\/wp-content\\/plugins\\/\' directory<\\/li>\\n<li>Activate the plugin through the \'Plugins\' menu in WordPress<\\/li>\\n<li>Click on SEOPress and apply settings.<\\/li>\\n<\\/ol>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/get-started-seopress\\/\\">Requirements \\/ Installation guide<\\/a><\\/p>"},"banners":{"high":"https:\\/\\/ps.w.org\\/wp-seopress\\/assets\\/banner-1544x500.png?rev=1","low":"https:\\/\\/ps.w.org\\/wp-seopress\\/assets\\/banner-772x250.png?rev=1"},"icons":{"1x":"https:\\/\\/www.seopress.org\\/fr\\/wp-content\\/uploads\\/sites\\/2\\/edd\\/2024\\/02\\/logo-square-seopress-pro-128x128.png","2x":"https:\\/\\/www.seopress.org\\/fr\\/wp-content\\/uploads\\/sites\\/2\\/edd\\/2024\\/02\\/logo-square-seopress-pro-256x256.png"},"stable_tag":"8.2","tested":"6.6.2","description":["<h3>Best SEO plugin for WordPress fully integrated with all page builders and themes!<\\/h3>\\n<h3>Now with AI (GPT 4) to automagically generate meta title, description and alternative texts for images!<\\/h3>\\n<p>SEOPress is a powerful WordPress SEO plugin to optimize your SEO, boost your traffic, improve social sharing, build custom HTML and XML Sitemaps, create optimized breadcrumbs, add schemas \\/ Google Structured data types, manage 301 redirections and so much more.<br \\/><\\/p>\\n<p>&#x2714; <strong><a href=\\"https:\\/\\/www.seopress.org\\/features\\/page-builders-integration\\/\\">Universal SEO metabox<\\/a>: edit all your SEO from any page builder \\/ theme builder. No more back and forth between your editor and the WordPress administration<\\/strong>\\n&#x2714; <strong>No advertising, no footprints, white label, in backend AND frontend<\\/strong>\\n&#x2714; <strong>Content analysis to help you write content optimized for search engines with unlimited target keywords<\\/strong>\\n&#x2714; <strong><a href=\\"https:\\/\\/www.seopress.org\\/solutions\\/migrate-from\\/\\">Import your post and term metadatas<\\/a> from other SEO plugins or CSV file in 1 click<\\/strong>\\n&#x2714; <strong><a href=\\"https:\\/\\/translate.wordpress.org\\/projects\\/wp-plugins\\/wp-seopress\\">Translated into 25 languages (and counting)<\\/a><\\/strong>\\n&#x2714; <strong>Trusted by over 300,000 WordPress websites since 2017<\\/strong><\\/p>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Buy SEOPress PRO: only $49 \\/ year \\/ 5 sites<\\/strong><\\/a><\\/p>\\n<p>[youtube <a href=\\"https:\\/\\/www.youtube.com\\/watch?v=4ysKFVr_nu0\\">https:\\/\\/www.youtube.com\\/watch?v=4ysKFVr_nu0<\\/a>]<\\/p>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/features\\/\\">Features<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/solutions\\/migrate-from\\/\\">Migrate<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/wordpress-seo-plugins\\/pro\\/\\">PRO<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/integrations\\/\\">Integrations<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/support\\/\\">Support<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/features\\/seopress-white-label\\/\\">White Label<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/features\\/openai\\/\\">AI<\\/a><\\/p>\\n<h3>Why SEOPress is the best WordPress SEO plugin?<\\/h3>\\n<ul>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/seopress-productivity\\/\\"><strong>Save time<\\/strong><\\/a>: you prefer to work with Excel or Google Spreadsheets? No problem, you can import \\/ export your metadata from CSV files with SEOPress PRO in few clicks!<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Save money<\\/strong><\\/a>: SEOPress PRO is available for $49 \\/ year \\/ 5 sites. Go unlimited sites for just $149 \\/ year!<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/\\"><strong>All in one SEO plugin<\\/strong><\\/a>: comes with all the features you need to optimize the SEO of your WordPress site. No need to install additional extensions to manage redirects, schemas, XML sitemaps... You reduce the risk of conflicts and maintenance costs. You don\'t need a special feature? Deactivate it with one click without losing your configuration. Child\'s play !<\\/li>\\n    <li><strong>Easy AND ready to use<\\/strong>: you doesn\'t need to know SEO or code to use SEOPress. Most of the parameters are automatically set. And thanks to our installation wizard, configuring SEOPress has never been easier. To go further, we provide many <a href=\\"https:\\/\\/www.seopress.org\\/support\\/\\">free tutorials<\\/a> and <a href=\\"https:\\/\\/www.seopress.org\\/support\\/ebooks\\/\\">ebooks to learn SEO<\\/a> in order to better understand how to position your content on search engines.<\\/li>\\n<\\/ul>\\n<h3>SEOPress Free Features<\\/h3>\\n<ul>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/google-indexing-api-with-seopress\\/\\"><strong>Google Indexing API and IndexNow API (Bing \\/ Yandex)<\\/strong><\\/a> to quickly index its content in these search engines<\\/li>\\n    <li><strong>Installation wizard<\\/strong> to quickly setup your site<\\/li>\\n    <li><strong>Content analysis<\\/strong> with unlimited keywords to help you write optimized content for search engines<\\/li>\\n    <li><strong>Mobile \\/ Desktop Google Preview<\\/strong> to see how your post will looks like in Google search results<\\/li>\\n    <li><strong>Facebook &amp; X (ex-Twitter) Social Preview<\\/strong> to see how your post will looks like on social media to increase conversions<\\/li>\\n    <li><strong>Titles<\\/strong> (with <a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/manage-titles-meta-descriptions\\/\\">dynamic variables<\\/a>: custom fields, terms taxonomie...)<\\/li>\\n    <li><strong>Meta descriptions<\\/strong> (with dynamic variables too)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/social-media\\/\\"><strong>Open Graph and X (ex-Twitter) Cards<\\/strong><\\/a> to improve social media sharing (Facebook, LinkedIn, Instagram, X (ex-Twitter), Pinterest, WhatsApp...)<\\/li>\\n    <li><strong>Google Knowledge Graph<\\/strong><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-analytics\\/\\"><strong>Google Analytics<\\/strong> and <strong>Matomo<\\/strong><\\/a> with downloads tracking, custom dimensions, ip anonymization, remarketing, demographics and interest reporting, cross-domain tracking...(<a href=\\"https:\\/\\/www.seopress.org\\/features\\/seopress-white-label\\/\\">GDPR compatibility<\\/a>)<\\/li>\\n    <li><strong>Microsoft Clarity integration<\\/strong>: to capture session recordings, get instant heatmaps and powerful Insights for Free. Know how people interact with your site to improve user experience and conversions<\\/li>\\n    <li><strong>Custom Canonical URL<\\/strong><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/manage-meta-robots\\/\\"><strong>Meta robots<\\/strong><\\/a> (noindex, nofollow, noimageindex, noarchive, nosnippet)<\\/li>\\n    <li>Build your <a href=\\"https:\\/\\/www.seopress.org\\/features\\/sitemaps\\/\\"><strong>custom XML Sitemap<\\/strong><\\/a> to improve search indexing<\\/li>\\n    <li><strong>Image XML Sitemaps<\\/strong> to improve search indexing for Google Images<\\/li>\\n    <li>Build your custom <strong>HTML Sitemap<\\/strong> to enhanced navigation for visitors and improve search indexing<\\/li>\\n    <li>Link your social media accounts to your site<\\/li>\\n    <li><strong>Redirections<\\/strong> in post, pages, custom post types<\\/li>\\n    <li>Remove \\/category\\/ in URLs<\\/li>\\n    <li>Remove \\/product-category\\/ in URLs<\\/li>\\n    <li>Remove ?replytocom to avoid duplicate content<\\/li>\\n    <li>Redirect attachment pages to post parent<\\/li>\\n    <li>Redirect attachment pages to their file URL<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/image-seo\\/\\"><strong>Image SEO<\\/strong><\\/a>: Automatically set the image title \\/ alt \\/ caption \\/ description<\\/li>\\n    <li>Import \\/ Export settings from site to site.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/solutions\\/migrate-from\\/\\">Import posts and terms metadata<\\/a> from Yoast SEO, All In One SEO, SEO Framework, Rank Math, SEO Ultimate, WP Meta SEO, Premium SEO Pack, Squirrly and many other SEO plugins<\\/li>\\n    <li>...<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/wordpress-seo-plugins\\/free\\/features\\/\\">Check out all SEOPress Free features here<\\/a><\\/p>\\n<h3>SEOPress PRO: to go further with your SEO<\\/h3>\\n<p>[youtube <a href=\\"https:\\/\\/youtu.be\\/zxGCY-bJYwE\\">https:\\/\\/youtu.be\\/zxGCY-bJYwE<\\/a>]<\\/p>\\n<ul>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/openai\\/\\"><strong>Use Artificial Intelligence (GPT 4 \\/ Vision and GPT 3.5 Turbo) to generate SEO metadata and alternative texts for image files. Bulk actions supported.<\\/strong><\\/a><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/site-audit\\/\\"><strong>Site Audit: Detect technical issues that could harm your site\'s SEO directly from your WordPress administration.<\\/strong><\\/a><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/seo-alerts\\/\\">Receive SEO alerts to prevent breaking your SEO before it\'s too late<\\/a><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/import-metadata-from-a-csv-file-with-seopress-pro\\/\\"><strong>Import \\/ export metadata<\\/strong><\\/a> (titles, open graph, robots...) from \\/ to CSV file<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/sitemaps\\/\\"><strong>Video XML Sitemap<\\/strong><\\/a> to improve rankings in video search results. YouTube videos are automatically added.<\\/li>\\n    <li>Internal linking suggestions<\\/li>\\n    <li>Inspect URL with Google Search Console: get details about crawling, indexing, mobile compatibility, schemas and more.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-search-console\\/\\"><strong>Search Console integration<\\/strong><\\/a>: get insights from your post \\/ page \\/ post type list with clicks, positions, CTR and impressions.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-suggest\\/\\"><strong>Google Suggestions in Content Analysis<\\/strong><\\/a> to find the top 10 Google suggestions instantly. This is useful if you want to work with the long tail technique.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-structured-data-types\\/\\"><strong>Google Structured Data types<\\/strong> (schema.org)<\\/a>:\\n        <ol>\\n            <li>article<\\/li>\\n            <li>local business<\\/li>\\n            <li>service<\\/li>\\n            <li>how-to<\\/li>\\n            <li>FAQ<\\/li>\\n            <li>course<\\/li>\\n            <li>recipe<\\/li>\\n            <li>software application<\\/li>\\n            <li>video<\\/li>\\n            <li>event<\\/li>\\n            <li>product<\\/li>\\n            <li>job<\\/li>\\n            <li>simple review<\\/li>\\n            <li>site navigation element<\\/li>\\n            <li>custom schema<\\/li>\\n        <\\/ol>\\n    <\\/li><li><strong>Automatic Schemas<\\/strong> with advanced conditions (AND, OR, Post types, taxonomies)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/breadcrumbs\\/\\"><strong>Breadcrumbs<\\/strong><\\/a> optimized with Schema.org, A11Y ready.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-analytics\\/\\"><strong>Google Analytics Stats in Dashboard<\\/strong><\\/a> to quickly see your metrics without leaving your site<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/local-seo\\/\\"><strong>Google Local Business<\\/strong><\\/a> to boost your local store<\\/li>\\n    <li><strong>Broken link checker (SEOPress BOT)<\\/strong>: scan all your links in content to find errors (e.g. 404...)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/woocommerce-seo\\/\\"><strong>WooCommerce<\\/strong><\\/a>: Product Global Identifiers type and Product Global Identifiers fields to WooCommerce metabox for product schema (barcode), disable crawling on cart page, checkout page, customer account pages, add OG Price \\/ OG Currency for better sharing and more<\\/li>\\n    <li><strong>Easy Digital Downloads<\\/strong>: add OG Price \\/ OG Currency, remove EDD meta generator<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/breadcrumbs\\/\\"><strong>Custom Breadcrumbs<\\/strong><\\/a> for single post types \\/ term taxonomy<\\/li>\\n    <li><strong>Google Page Speed Insights<\\/strong> to analyse your site performances on Mobile \\/ Desktop + your Core Web Vitals<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-analytics\\/\\"><strong>Google Enhanced Ecommerce for WooCommerce<\\/strong><\\/a>: measure purchases, singular product view details, additions to and removals from shopping carts<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/htaccess-robots-txt\\/\\">Edit your <strong>robots.txt<\\/strong><\\/a> file from the admin (multisite \\/ multidomain ready)<\\/li>\\n    <li><strong>Google News Sitemap<\\/strong> to get your posts on Google News<\\/li>\\n    <li><strong>404 Monitoring<\\/strong>: Monitor your 404 errors to improve user experience, performances and increase the crawl budget allocated by Google<\\/li>\\n    <li><strong>Redirect 404 to homepage\\/custom url automatically<\\/strong> with custom status code (301, 302, 307, 410 or 451)<\\/li>\\n    <li>Email notifications on 404<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/301-redirects\\/\\"><strong>Redirect manager<\\/strong><\\/a>: create unlimited 301, 302, 307, 410 and 451 redirections. Regular expressions supported. Import \\/ export redirections to CSV or htaccess file.<\\/li>\\n    <li>Import redirections using CSV<\\/li>\\n    <li>Import redirections from Redirections plugin (via a JSON file)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/htaccess-robots-txt\\/\\">Edit your <strong>htaccess file<\\/strong><\\/a> from the admin<\\/li>\\n    <li>Easily customize your <strong>RSS feeds<\\/strong><\\/li>\\n    <li>...<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Buy SEOPress PRO now!<\\/strong><\\/a><\\/p>\\n<h3>WooCommerce SEO (SEOPress PRO required)<\\/h3>\\nWe support WooCommerce and Easy Digital Downloads for e-commerce sites.\\n<ul>\\n    <li>Price and currency meta tags to improve social sharing<\\/li>\\n    <li>XML sitemaps for products<\\/li>\\n    <li>Support for WooCommerce product images and WooCommerce image galleries for the XML sitemap<\\/li>\\n    <li>Centralized way to set noindex meta robots tags on pages like cart, checkout...<\\/li>\\n    <li>Remove WooCommerce generator meta tag in the source code<\\/li>\\n    <li>Create manual and\\/or automatic \\"product\\" schemas in JSON-LD to increase visibility in Google search results<\\/li>\\n    <li>WooCommerce support for our breadcrumbs<\\/li>\\n    <li>Global dynamic tags to insert in your metas titles \\/ descriptions<\\/li>\\n    <li>Product Global Identifiers type and Product Global Identifiers fields to WooCommerce metabox for product schema (barcode)<\\/li>\\n    <li>...<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Increase your sales now!<\\/strong><\\/a><\\/p>\\n<h3><a href=\\"https:\\/\\/www.seopress.org\\/features\\/page-builders-integration\\/\\">Universal SEO metabox<\\/a>: edit your metadata from any page builder \\/ editor<\\/h3>\\n<p>[youtube <a href=\\"https:\\/\\/youtu.be\\/sf0ocG7vQMM\\">https:\\/\\/youtu.be\\/sf0ocG7vQMM<\\/a>]<\\/p>\\n<h3>Elementor + SEOPress: perfect combo!<\\/h3>\\nWe provide deep integration with Elementor page builder UI, see below:\\n<p>[youtube <a href=\\"https:\\/\\/www.youtube.com\\/watch?v=oC5QZ0_TH_g\\">https:\\/\\/www.youtube.com\\/watch?v=oC5QZ0_TH_g<\\/a>]<\\/p>\\n<h3>SEOPress Insights: Off-site SEO plugin to track your rankings and backlinks in WordPress<\\/h3>\\n<ul>\\n    <li><strong>Keyword rank tracker<\\/strong>: 51 Google Search locations available<\\/li>\\n    <li>Track <strong>50 keywords<\\/strong> per site daily<\\/li>\\n    <li>Track your <strong>competitors<\\/strong>: who ranks first on your keywords<\\/li>\\n    <li>Monitor and analyze your <strong>backlinks<\\/strong> weekly<\\/li>\\n    <li><strong>Google trends<\\/strong> to find new and relevant ideas for your content marketing strategy<\\/li>\\n    <li>Your <strong>data accessible for life<\\/strong>: export it to a CSV, PDF or Excel file. Sort, order, filter your data right from your WordPress.<\\/li>\\n    <li>Receive <strong>email and Slack alerts<\\/strong> for your rankings to easily follow them<\\/li>\\n<\\/ul>\\n<p>[youtube <a href=\\"https:\\/\\/youtu.be\\/p6v9Jd5lRIU\\">https:\\/\\/youtu.be\\/p6v9Jd5lRIU<\\/a>]<\\/p>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Buy SEOPress Insights now!<\\/strong><\\/a><\\/p>\\n<h3>Developers will love SEOPress!<\\/h3>\\n<ul>\\n    <li>Hundreds of hooks are available to extend SEOPress. <a href=\\"https:\\/\\/www.seopress.org\\/support\\/hooks\\/\\">Browse them all here<\\/a>!<\\/li>\\n    <li>Plus we have a <a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/get-started-with-the-seopress-rest-api\\/\\">REST API<\\/a> to build static websites.<\\/li>\\n    <li>Finally, <a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/seopress-wp-cli\\/\\"><strong>WP CLI commands<\\/strong><\\/a> are available.<\\/li>\\n<\\/ul>"],"changelog":["<h4> 8.2 <a href=\\"https:\\/\\/www.seopress.org\\/newsroom\\/product-news\\/seopress-8-2\\/\\">Read the blog post update<\\/a> <\\/h4>\\n<ul>\\n<li>NEW Site Audit: Detect technical issues that could harm your site\'s SEO directly from your WordPress administration (<a href=\\"https:\\/\\/www.seopress.org\\/features\\/site-audit\\/\\">https:\\/\\/www.seopress.org\\/features\\/site-audit\\/<\\/a>) &#x1f389;<\\/li>\\n<li>NEW Add a notice to the Block Editor on slug changes to quickly create a redirection to keep your SEO (PRO)<\\/li>\\n<li>INFO Table of contents Block: allow \\"paragraph\\" \\/ \\"div\\" for the title of the block<\\/li>\\n<li>INFO Add notice to robots.txt settings tab if a physical file is already present on your server<\\/li>\\n<li>INFO Support robots.txt file for WP multisite (subdirectories installation with custom domains)<\\/li>\\n<li>INFO Strengthened security<\\/li>\\n<li>FIX Wizard redirect when updating SEOPress PRO<\\/li>\\n<li>FIX Internal links list in standard content analysis metabox<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/changelog\\/\\">View our complete changelog<\\/a>\\n<a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/how-to-downgrade-seopress-pro-to-a-previous-version\\/\\">Need to downgrade\\/rollback?<\\/a><\\/p>"],"installation":["<ol>\\n<li>Upload \'wp-seopress\' to the \'\\/wp-content\\/plugins\\/\' directory<\\/li>\\n<li>Activate the plugin through the \'Plugins\' menu in WordPress<\\/li>\\n<li>Click on SEOPress and apply settings.<\\/li>\\n<\\/ol>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/get-started-seopress\\/\\">Requirements \\/ Installation guide<\\/a><\\/p>"],"plugin":"wp-seopress-pro\\/seopress-pro.php","id":"wp-seopress-pro\\/seopress-pro.php"}";}', 'off'),
(338, 'nav_menu_options', 'a:2:{i:0;b:0;s:8:"auto_add";a:0:{}}', 'auto'),
(349, 'new_admin_email', '<EMAIL>', 'auto'),
(359, 'roles_for_privacy_policy', 'a:1:{s:6:"editor";s:1:"1";}', 'auto'),
(361, 'options_info_message_active', '', 'off'),
(362, '_options_info_message_active', 'field_603fb9026c829', 'off'),
(363, 'options_info_message_id', '', 'off'),
(364, '_options_info_message_id', 'field_603fb9026c831', 'off'),
(365, 'options_info_message_txt', '', 'off'),
(366, '_options_info_message_txt', 'field_603fb9026c837', 'off'),
(367, 'options_info_message', '', 'off'),
(368, '_options_info_message', 'field_651e81a5c7361', 'off'),
(369, 'category_children', 'a:0:{}', 'auto'),
(386, 'options_footer_newsletter_title', 'Souscrire \r\nà la newsletter', 'off'),
(387, '_options_footer_newsletter_title', 'field_66992042b9fb0', 'off'),
(388, 'options_footer_newsletter_link_example', 'a:3:{s:5:"title";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";s:6:"target";s:0:"";}', 'off'),
(389, '_options_footer_newsletter_link_example', 'field_66992065b9fb1', 'off'),
(390, 'options_footer_newsletter_txt', 'Un email chaque mois avec les infos exclusives et les sélections de vidéos Numeridanse', 'off'),
(391, '_options_footer_newsletter_txt', 'field_6699208eb9fb2', 'off'),
(392, 'options_footer_newsletter_link_subscribe', 'a:3:{s:5:"title";s:11:"S’abonner";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";s:6:"target";s:0:"";}', 'off'),
(393, '_options_footer_newsletter_link_subscribe', 'field_669920a0b9fb3', 'off'),
(394, 'options_footer_newsletter', '', 'off'),
(395, '_options_footer_newsletter', 'field_66991fc36d529', 'off'),
(396, 'options_footer', '', 'off'),
(397, '_options_footer', 'field_651e816af158a', 'off'),
(460, 'options_footer_copyright', '2021 - {current_year} Numeridanse tous droits réservés', 'off'),
(461, '_options_footer_copyright', 'field_669e25b050bd0', 'off'),
(462, 'options_footer_social', '', 'off'),
(463, '_options_footer_social', 'field_669e2697c367b', 'off'),
(464, 'options_footer_partners_link_mad', 'https://maisondeladanse.com', 'off'),
(465, '_options_footer_partners_link_mad', 'field_669e2880a6f09', 'off'),
(466, 'options_footer_partners_link_ministere', 'https://www.culture.gouv.fr', 'off'),
(467, '_options_footer_partners_link_ministere', 'field_669e28d7a6f0a', 'off'),
(468, 'options_footer_partners_link_cnd', 'https://www.cnd.fr', 'off'),
(469, '_options_footer_partners_link_cnd', 'field_669e28efa6f0b', 'off'),
(470, 'options_footer_partners_link_fbnp', 'https://fondation.bnpparibas', 'off'),
(471, '_options_footer_partners_link_fbnp', 'field_669e28ffa6f0c', 'off'),
(472, 'options_footer_partners', '', 'off'),
(473, '_options_footer_partners', 'field_669e2779c367e', 'off'),
(476, 'options_footer_social_link_facebook', 'https://www.facebook.com/Numeridanse', 'off'),
(477, '_options_footer_social_link_facebook', 'field_669e2a041a1b2', 'off'),
(478, 'options_footer_social_link_instagram', 'https://www.instagram.com/numeridanse', 'off'),
(479, '_options_footer_social_link_instagram', 'field_669e2a181a1b3', 'off'),
(480, 'options_footer_social_link_linkedin', 'https://www.linkedin.com/company/numeridanse', 'off'),
(481, '_options_footer_social_link_linkedin', 'field_669e2a231a1b4', 'off'),
(638, 'hicpo_options', 'a:2:{s:7:"objects";a:0:{}s:4:"tags";a:0:{}}', 'auto') ;
INSERT INTO `nd_options` ( `option_id`, `option_name`, `option_value`, `autoload`) VALUES
(783, 'acfe_plugin_updates', 'a:2:{s:7:"timeout";i:1728340044;s:5:"value";s:82420:"{"new_version":"*******","stable_version":"*******","name":"ACF Extended Pro","slug":"acf-extended","url":"https:\\/\\/www.acf-extended.com\\/?download=acf-extended-pro&#038;changelog=1","last_updated":"2024-07-25 21:50:23","homepage":"https:\\/\\/www.acf-extended.com","package":"https:\\/\\/www.acf-extended.com\\/edd-sl\\/package_download\\/MTcyODQyMjg0NDphMlY1UFRjNU9EZzRaV1UwWldNMU1UZGtZVE13TTJNeFkyWm1OalJpWVdSa1pXTTU6OTQ5OmVhMDVkZTdhNWQzM2MzYWFiMDQyOGU2ZDc5NGJkNWZkOmh0dHBzQC8vbnVtZXJpZGFuc2UubG9jYWw6MA==","download_link":"https:\\/\\/www.acf-extended.com\\/edd-sl\\/package_download\\/MTcyODQyMjg0NDphMlY1UFRjNU9EZzRaV1UwWldNMU1UZGtZVE13TTJNeFkyWm1OalJpWVdSa1pXTTU6OTQ5OmVhMDVkZTdhNWQzM2MzYWFiMDQyOGU2ZDc5NGJkNWZkOmh0dHBzQC8vbnVtZXJpZGFuc2UubG9jYWw6MA==","sections":{"description":"<p>&#x1f680; All-in-one enhancement suite that improves WordPress &amp; Advanced Custom Fields. This plugin aims to provide a powerful administration framework with a wide range of improvements &amp; optimizations.<\\/p>\\n<p><strong>This plugin requires at least ACF Pro 5.8.<\\/strong>\\nIf you don\'t already own <a href=\\"https:\\/\\/www.advancedcustomfields.com\\/pro\\/\\">ACF Pro<\\/a>, you should consider it. It\'s one of the most powerful WordPress plugin available.<\\/p>\\n<h3>&#x2b50; Highlight<\\/h3>\\n<ul>\\n<li>14+ New ACF Fields<\\/li>\\n<li>10+ ACF Fields Enhanced<\\/li>\\n<li>4+ New Field Groups Locations<\\/li>\\n<li>Self\\/Multi\\/Bidirectional Fields<\\/li>\\n<li>Advanced Fields Validation<\\/li>\\n<li>Flexible Content as Page Builder<\\/li>\\n<li>Optimize metadata with Performance Mode<\\/li>\\n<li>Advanced Front-End Forms Manager<\\/li>\\n<li>ACF Options Pages \\/ Block Types Manager<\\/li>\\n<li>ACF &amp; WordPress Meta Overview<\\/li>\\n<li>WordPress Post Types \\/ Taxonomies Manager<\\/li>\\n<li>WordPress Options Manager<\\/li>\\n<li>WordPress Admin Enhancements<\\/li>\\n<li>WPML &amp; Polylang Multilingual support<\\/li>\\n<li>... And many more features<h3>&#x1f48e; Pro Highlight<\\/h3><\\/li>\\n<li>20+ New ACF Fields<\\/li>\\n<li>10+ ACF Fields Enhanced<\\/li>\\n<li>20+ New Locations<\\/li>\\n<li>Payment Field with Stripe &amp; PayPal Express<\\/li>\\n<li>Flexible Content Grid System<\\/li>\\n<li>Flexible Content Layouts Locations Rules<\\/li>\\n<li>Templates Manager<\\/li>\\n<li>Builtin Classic Editor<\\/li>\\n<li>Settings UI<\\/li>\\n<li>Screen Layouts<\\/li>\\n<li>Force Json Sync<\\/li>\\n<li>Field Visibility Settings<\\/li>\\n<li>Global Field Conditional Rules<\\/li>\\n<li>... And many more features<h3>&#x1f91f; Philosophy<\\/h3><\\/li>\\n<li>Seamless integration<\\/li>\\n<li>No extra menu, ads or notices<\\/li>\\n<li>Built by developers, for developers<h3>&#x1f6e0;&#xfe0f; Links<\\/h3><\\/li>\\n<li><a href=\\"https:\\/\\/www.acf-extended.com\\">Website<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\">Documentation<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/www.acf-extended.com\\/guides\\">Guides<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/www.acf-extended.com\\/roadmap\\">Roadmap<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/github.com\\/acf-extended\\/ACF-Extended\\">GitHub<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/twitter.com\\/ACFExtended\\">Twitter<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/twitter.com\\/hwkfr\\">Twitter<\\/a> (Personal)<\\/li>\\n<li><a href=\\"https:\\/\\/slack.acf-extended.com\\">Slack Community<\\/a><h3>&#x1f9f0; Tools<\\/h3><\\/li>\\n<li><a href=\\"https:\\/\\/wordpress.org\\/plugins\\/acf-extended\\/#faq\\">FAQ<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/wordpress.org\\/support\\/plugin\\/acf-extended\\">Support<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/wordpress.org\\/support\\/plugin\\/acf-extended\\">Feature Request<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/wordpress.org\\/support\\/plugin\\/acf-extended\\/reviews\\/#new-post\\">Reviews<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/ko-fi.com\\/acfextended\\">Donation<\\/a><h3>&#x1f4c1; Field Groups<\\/h3>\\n<strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/advanced-settings\\">Advanced Settings<\\/a><\\/strong>\\nEnable advanced settings for all fields within the Field Group.<\\/li>\\n<\\/ul>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/autosync\\">Auto Sync PHP<\\/a><\\/strong>\\nAutomatically synchronize field groups with local PHP files upon field group updates. This feature will create, include and update a local PHP file for each field group.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/autosync\\">Auto Sync Json<\\/a><\\/strong>\\nControl which field groups you want to synchronize with local Json files. Display warnings if the Json file has been manually deleted.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/categories\\">Categories<\\/a><\\/strong>\\nSpice up your field groups with a custom taxonomy and filter field groups by terms.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/custom-key\\">Custom Key<\\/a><\\/strong>\\nSet custom field group key. Example: <code>group_custom_name<\\/code>.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/custom-meta\\">Custom Meta<\\/a><\\/strong>\\nAdd custom metas (key\\/value) in the field group administration.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/display-title\\">Display Title<\\/a><\\/strong>\\nDisplay an alternative field group title in post edition screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/field-group-ui\\">Field Group UI<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nEnable enhancements to the Field Group UI for a better user experience.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/force-sync\\">Force Sync<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nAlways keep Json files synchronized with the Field Groups in the database.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/hide-on-screen\\">Hide on Screen<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nHide Gutenberg Block Editor and 10+ more items to hide in the field group settings.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/instruction-placement\\">Instructions Placement<\\/a><\\/strong>\\nNew instruction placements let you display field description &quot;above the fields&quot; or in a &quot;tooltip&quot;.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/advanced-post\\">Location: Advanced Post<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nA collection of multiple new Field Groups locations allowing developers to target posts with specific conditions (Post author, date, slug, path etc...).<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/advanced-menu-item\\">Location: Advanced Menu Item<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nTarget specific &quot;Menu Item Depth&quot; or &quot;Menu Item Type&quot; from the Field Groups Locations rules.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/advanced-taxonomy\\">Location: Advanced Taxonomy Term<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nA collection of multiple new Field Groups locations allowing developers to target taxonomy and terms with specific conditions (Term name, parent, slug etc...).<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/all-post-types\\">Location: All post types<\\/a><\\/strong>\\nDisplay field groups on all post types edition screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/attachment-list\\">Location: Attachment List<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay field group on attachment admin list screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/dashboard\\">Location: Dashboard Widgets<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay field groups and update ACF Fields from the WP Dashboard.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/field-value\\">Location: Field Value<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a field group based on the field value of an another field group.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/post-type-archive\\">Location: Post type Archive<\\/a><\\/strong>\\nAdd an Archive Option Page under the Post Type admin menu. Display and save any field groups within it.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/post-type-list\\">Location: Post type List<\\/a><\\/strong>\\nDisplay field group on post types admin list screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/taxonomy-list\\">Location: Taxonomy List<\\/a><\\/strong>\\nDisplay field group on taxonomies admin list screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/user-list\\">Location: User List<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay field group on user admin list screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/woocommerce\\">Location: Woocommerce<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay field groups on Woocommerce pages.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/wp-settings\\">Location: WP Settings<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay field groups on WP Settings pages: General, Writing, Reading, Discussion, Media and Permalinks.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/\\">Local Field Groups<\\/a><\\/strong>\\nDisplay local field groups that are loaded by ACF, but not available in the ACF field group administration. Example: Field groups that are registered in the <code>functions.php<\\/code> file, but not in the ACF UI.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/note\\">Note<\\/a><\\/strong>\\nAdd a personal note in the field group administration. Only visible to administrators.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/permissions\\">Permissions<\\/a><\\/strong>\\nAdd permission layer to field groups. Choose which roles can view &amp; edit field groups in the post edition screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/raw-data\\">Raw Data<\\/a><\\/strong>\\nDisplay raw field group data in a modal to check your configuration &amp; settings.<h3>&#x2699;&#xfe0f; Fields Settings<\\/h3>\\n<strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/field-advanced-settings\\">Advanced Settings<\\/a><\\/strong>\\nA more sophisticated field settings based on specified location (administration\\/front-end). Example: Field is required only in front-end.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/advanced-validation\\">Advanced Validation<\\/a><\\/strong>\\nA more sophisticated validation conditions (AND\\/OR) with custom error messages based on specified location (administration\\/front-end).<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/bidirectional-fields\\">Self\\/Multi\\/Bidirectional fields<\\/a><\\/strong>\\nAn advanced bidirectional setting (also called post-to-post) is available for the following fields: Relationship, Post object, User &amp; Taxonomy terms. Fields will work bidirectionally and automatically update each others. Works in groups &amp; clones.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/field-visibility\\">Field Visibility<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nGet quick access to &quot;Field Visibility&quot;, &quot;Label Visibility&quot;, &quot;Instructions Visibility&quot; and &quot;Required Setting&quot; for the following screens: &quot;Everywhere&quot;, &quot;Front-end&quot; and &quot;Administration&quot;.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/global-condition\\">Global Condition<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nEnable Global Conditional Logic for a specific field, which can then be used in an another Field Group as condition, both as Field Group Condition and Field Condition.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/field-instruction-placement\\">Instruction Placement<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nOverride a specific field instruction placement to any position: Below labels, below fields, above fields or tooltip.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/instruction-read-more\\">Instruction Read More<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nAllow to expand instructions text with a &quot;Read More&quot; link. This feature is useful for lengthy instructions text.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/min-max\\">Min\\/Max<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nMinimum &amp; maximum items is a global field setting that let you define a specific number of items that can or should be added by the user.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/field-permissions\\">Permissions<\\/a><\\/strong>\\nAdd permission layer to fields. Choose which roles can view &amp; edit fields in the post edition screen. (can be combined with field groups permissions).<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/field-raw-data\\">Raw data<\\/a><\\/strong>\\nDisplay raw field data in a modal to check your configuration &amp; settings.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/required-message\\">Required Message<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nThis setting allow developers to define a custom error message within the field settings for a more intuitive user experience.<h3>&#x1f3f7;&#xfe0f; Fields<\\/h3>\\n<strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/advanced-link\\">Advanced Link<\\/a><\\/strong>\\nDisplay a modern Link Selection in a modal. Posts, Post Types Archives &amp; terms selection can be filtered in the field administration.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/block-editor\\">Block Editor<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an isolated Block Editor field on admin screen (with Classic Editor enabled) or on the front-end.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/block-types\\">Block Types<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an ACF Block Types selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/button\\">Button<\\/a><\\/strong>\\nDisplay a custom submit or button. Built-in ajax call setting. Usage example available in the field administration.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/checkbox\\">Checkbox<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nDefine grouped choices values using <code>## Title<\\/code> markup in the field\\u2019s choices.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/clone\\">Clone<\\/a><\\/strong>\\nAllow users to edit clone fields in a modal. Choose the edit button text, display close button and the modal size.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/code-editor\\">Code Editor<\\/a><\\/strong>\\nEdit code using the native WP Core Codemirror library. Default languages: Text\\/HTML, Javascript, CSS, PHP mixed\\/plain.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/color-picker\\">Color Picker<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nA collection of advanced settings for the ACF Color Picker. The field can now be displayed as a palette, custom colors can be predefined and RGBA mode is supported.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/columns\\">Columns<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nOrganize and re-arrange your fields using columns and line-breaks. The field acts like the ACF Accordion\\/Tab field and allow you to create virtually grouped fields which will be displayed inside columns.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/countries\\">Countries<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Country selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/currencies\\">Currencies<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Currency selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/datepicker\\">Date\\/Timepicker<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nDisplay a modern UI of the ACF Datepicker field. CSS and icons have been enhanced to fit WordPress admin UI and colors.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/date-range-picker\\">Date Range Picker<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Date Range Picker. The field support a wide range of customization, such as: Placeholder, Default dates, Range Restriction, Date restriction, No weekends etc.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/dynamic-render\\">Dynamic Render<\\/a><\\/strong>\\nDisplay custom HTML\\/PHP content using a simple named hook.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/field-groups-selector\\">Field Groups<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an ACF Field Groups selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/field-types\\">Field Types<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an ACF Field Types selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/fields-selector\\">Fields<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an ACF Fields selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/file\\">File<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nChoose the uploader type, enable multi file upload and dropzone.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/flexible-content\\">Flexible Content<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nDisplayed an enhanced version of the native Flexible Content field. Dozens of new settings and settings were added, allowing developers to create the most advanced page builder and fully control the field\\u2019s behavior.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/forms\\">Forms<\\/a><\\/strong>\\nSelect any dynamic form (format: checkbox, radio or select).<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/google-map\\">Google Map<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nA collection of new settings added to the ACF Google Map Field that allow developers to have more control over the field behavior.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/recaptcha\\">Google reCaptcha<\\/a><\\/strong>\\nDisplay a reCaptcha field (compatible v2 &amp; v3).<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/group\\">Group<\\/a><\\/strong>\\nAllow users to edit group fields in a modal Choose the edit button text, display close button and the modal size<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/hidden-input\\">Hidden Input<\\/a><\\/strong>\\nDisplay a hidden input with custom name\\/value<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/image\\">Image<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nChoose the uploader type, customize the upload folder and set the image as post featured thumbnail<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/image-selector\\">Image Selector<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an Image Selector field.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/image-sizes\\">Image Sizes<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an Image Sizes selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/languages\\">Languages<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Language selector as radio, checkbox or select field type, compatible with WPML &amp; Polylang.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/menu-locations\\">Menu Locations<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Menu Locations selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/menus\\">Menus<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Menu selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/options-pages\\">Options Pages<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an ACF Options Pages selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/payment\\">Payment<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Payment Field that supports with Stripe &amp; PayPal Express gateways, working on both front-end and back-end.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/payment-cart\\">Payment Cart<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an optional Payment Cart to easily setup an e-commerce solution.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/payment-selector\\">Payment Selector<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an optional Payment Selector which let the user switch the payment gateway.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/phone-number\\">Phone Number<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a fully customizable international Phone Number field.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/post-field\\">Post Field<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nThe Post Field is a new field that allow developers to move native WordPress fields such as Post Title, Date, Status, Visibility, Permalink etc.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/post-formats\\">Post Formats<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Post Format selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\">Post Object<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nAllow user to enter custom value which will be saved as a new post, or enable the inline post creation\\/edit.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/post-statuses\\">Post Status<\\/a><\\/strong>\\nSelect any post status (format: checkbox, radio or select)<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/post-types\\">Post Types<\\/a><\\/strong>\\nSelect any post type (format: checkbox, radio or select)<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/radio\\">Radio<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nDefine grouped choices values using <code>## Title<\\/code> markup in the field\\u2019s choices.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/relationship\\">Relationship<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nThe Relationship field includes new settings allowing users to create and edit post on-the-fly from the post edit screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/repeater\\">Repeater<\\/a><\\/strong>\\nAdd stylised to \'Add Row\' button, lock rows and remove repeater\'s actions.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/select\\">Select<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nChange the default &quot;Select&quot; placeholder text and Search Input placeholder and allow user to enter custom values.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/slug\\">Slug<\\/a><\\/strong>\\nA slug text input (ie: <code>my-text-input<\\/code>).<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/tab\\">Tab<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisable the last opened tab user preference. Which means that when the user will refresh the page, it will always load the first tab.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/taxonomies\\">Taxonomies<\\/a><\\/strong>\\nSelect any taxonomy (format: checkbox, radio or select)<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/taxonomy-terms\\">Taxonomy Terms<\\/a><\\/strong>\\nSelect any terms of any taxonomies, allow specific terms, level or childrens (format: checkbox or select). Terms can be loaded &amp; saved for the current post (just like the native ACF Taxonomy field)<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/templates-selector\\">Templates<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an ACF Extended Templates selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/textarea\\">Textarea<\\/a><\\/strong>\\nSwitch font family to monospace and allow tab indent.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/true-false\\">True\\/False<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nFive new styles have been added to the native True\\/False field.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/user-roles\\">User Roles<\\/a><\\/strong>\\nSelect any user role (format: checkbox, radio or select)<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/wysiwyg-editor\\">WYSIWYG Editor<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nNew settings allowing developers to have more control over the field behavior.<h3>&#x1f6e0;&#xfe0f; Modules<\\/h3>\\n<strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/dynamic-block-types\\">Block Types UI<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nThe Dynamic Block Types module allows you to register and manage ACF Block Types from your WordPress admin, in ACF &gt; Block Types menu. Pro version allows to sync Json\\/PHP files.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/classic-editor\\">Classic Editor<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nACF Extended is bundled with a custom merged version of the Classic Editor &amp; Disable Gutenberg plugins.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/developer-mode\\">Developer Mode<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nThe Developer Mode allow you to view all Posts, Terms, Users &amp; Options custom metadata in a readable format. This feature is very useful to check what is actually saved in any WordPress Object.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/dynamic-forms\\">Forms<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nManage Advanced ACF Forms from the WordPress administration. This module is an enhanced version of the native ACF Form feature. While all native settings can be used, Dynamic Forms adds many new settings and introduce &quot;Actions&quot; for a complete control over the form behavior.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/dynamic-options-pages\\">Options Pages UI<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nThe Dynamic Options Pages module allows you to register and manage ACF Options Pages from your WordPress admin, in ACF &gt; Options Pages menu. Pro version allows to sync Json\\/PHP files.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/performance-mode\\">Performance Mode<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nA unique module that allows developers to optimize database load when dealing with hundreds or thousands of metadata with two different methods: Ultra &amp; Hybrid Engines.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/dynamic-post-types\\">Post Types UI<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nThe Dynamic Post Types module allows you to register and manage custom post types from your WordPress admin, in Tools &gt; Post Types menu. Pro version allows to sync Json\\/PHP files.<\\/p>\\n<p>All native post types settings can be set within the UI. ACF Extended also adds more advanced settings allowing to manage posts per page, order etc\\u2026<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/rewrite-rules\\">Rewrite Rules<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nGet an overview of all WordPress permalinks structures and rules. Test URLs, export rules and flush permalinks from the UI.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/scripts\\">Scripts UI<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nRun custom scripts on thousands of posts. Including builtin &quot;Orphan Meta Cleaner&quot;, &quot;Script Launcher&quot; and &quot;Performance Converter&quot; scripts.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/settings-ui\\">Settings UI<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nThe Settings UI allows developers to get an overview of all ACF and ACF Extended settings values from the ACF &gt; Settings menu.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/dynamic-taxonomies\\">Taxonomies UI<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nThe Dynamic Taxonomies module allows you to register and manage custom taxonomies from your WordPress admin, in Tools &gt; Taxonomies menu. Pro version allows to sync Json\\/PHP files.<\\/p>\\n<p>All native taxonomies settings can be set within the UI. ACF Extended also adds more advanced settings allowing to manage posts per page, order etc\\u2026<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/templates\\">Templates<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nManage default ACF values in an advanced way and sync templates with Json\\/PHP files.<h3>&#x1f5a5;&#xfe0f; WordPress<\\/h3>\\n<strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/wordpress\\/ajax-author-box\\">Ajax Author Box<\\/a><\\/strong>\\nThe native WP Author Metabox has been replaced with an Ajax version allowing to manage thousands of users without slowing down the post administration. The new Author box also include an inline search input.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/wordpress\\/enhanced-ui\\">Enhanced UI<\\/a><\\/strong>\\nThe Taxonomy, User profile &amp; Settings views have been enhanced for a more consistent administration experience, using CSS\\/JS only.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/wordpress\\/polylang\\">Polylang<\\/a><\\/strong>\\nACF Extended adds a new layer of compatibility for Polylang. ACF Options Pages and all ACF Extended Modules (Dynamic Post Type, Taxonomy, Options Pages, Block Type) are compatible.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/wordpress\\/screen-layouts\\">Screen Layouts<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nPost Edit screens have been enhanced allowing up to 3 columns layout and multiple variations.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/wordpress\\/wpml\\">WPML<\\/a><\\/strong>\\nACF Extended adds a new layer of compatibility for WPML. ACF Options Pages and all ACF Extended Modules (Dynamic Post Type, Taxonomy, Options Pages, Block Type) are compatible.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/wordpress\\/options\\">WP Options<\\/a><\\/strong>\\nManage WordPress Options from the Settings &gt; Options page. Options value (strings, serialized &amp; Json) will be displayed in a readable form. Values can be edited or deleted.<h3>&#x2764;&#xfe0f; Early Supporters<\\/h3><\\/p>\\n<ul>\\n<li>Thanks to <a href=\\"https:\\/\\/twitter.com\\/AsmussenBrandon\\">Brandon A.<\\/a> for his support &amp; tests<\\/li>\\n<li>Thanks to <a href=\\"https:\\/\\/twitter.com\\/DamChtlv\\">Damien C.<\\/a> for his support &amp; tests<\\/li>\\n<li>Thanks to <a href=\\"https:\\/\\/twitter.com\\/Val_Pellegrin\\">Valentin P.<\\/a> for his support &amp; tests<\\/li>\\n<li>Thanks to Damian P. for his support &amp; tests<\\/li>\\n<li>Thanks to <a href=\\"https:\\/\\/twitter.com\\/jsaarenk\\">Jaakko S.<\\/a> for his support &amp; tests<\\/li>\\n<li>Thanks to <a href=\\"https:\\/\\/twitter.com\\/altendorfme\\">Renan A.<\\/a> for his support &amp; tests<h3>&#x1f970; Donors<\\/h3><\\/li>\\n<li>Thanks to RavenSays<\\/li>\\n<li>Thanks to Dave A.<\\/li>\\n<li>Thanks to Rob H.<\\/li>\\n<li>Thanks to Valentin P.<\\/li>\\n<li>Thanks to Alan A.<\\/li>\\n<li>Thanks to Damian C.<\\/li>\\n<li>Thanks to Andrew<\\/li>\\n<li>Thanks to Kimantis<\\/li>\\n<li>Thanks to Anonymous<\\/li>\\n<li>Thanks to Chris<\\/li>\\n<li>Thanks to Dennis D.<\\/li>\\n<li>Thanks to Cody R.<\\/li>\\n<li>Thanks to Jamie<\\/li>\\n<li>Thanks to Dave A.<\\/li>\\n<li>Thanks to Paul M.<\\/li>\\n<li>Thanks to David B.<\\/li>\\n<li>Thanks to Swingjac<\\/li>\\n<li>Thanks to Erik<\\/li>\\n<li>Thanks to Giancarlo P.<\\/li>\\n<li>Thanks to Geuer M.<\\/li>\\n<\\/ul>","installation":"<h4> Wordpress Install <\\/h4>\\n<ol>\\n<li>Install Advanced Custom Fields: Pro<\\/li>\\n<li>Upload the plugin files to the <code>\\/wp-content\\/plugins\\/acf-extended\\/<\\/code> directory, or install the plugin through the WordPress plugins screen directly.<\\/li>\\n<li>Activate the plugin through the \'Plugins\' screen in WordPress.<\\/li>\\n<li>Everything is ready!<\\/li>\\n<\\/ol>","changelog":"<h4> ******* <\\/h4>\\n<p><strong>ACF Extended Pro *******:<\\/strong><\\/p>\\n<ul>\\n<li>Field: Block Editor - Added WP 6.6 compatibility<\\/li>\\n<\\/ul>\\n<p><strong>ACF Extended Basic *******:<\\/strong><\\/p>\\n<ul>\\n<li>General: Updated WP 6.6 metabox order handle positioning<\\/li>\\n<\\/ul>\\n<h4> ******* <\\/h4>\\n<p><strong>ACF Extended Pro *******:<\\/strong><\\/p>\\n<ul>\\n<li>Field: Flexible Content - Grid - Added filter to rename columns (1\\/12, 2\\/12, 3\\/12\\u2026)<\\/li>\\n<li>Field: Google Map - Added small UI settings tweaks<\\/li>\\n<li>Field: Payment - Removed Polyfill library dependency<\\/li>\\n<li>Field Settings: Global Condition - Fixed Global Field Condition operators with ACF 6.3<\\/li>\\n<li>Field Settings: Instructions Tooltip - Fixed tooltip always visible when switching tab<\\/li>\\n<li>Module: Force Sync - Fixed sync from Dashboard with Flexible Content Toggle Layout<\\/li>\\n<li>Module: Force Sync - Enhanced Force Delete compatibility with Json and PHP sync<\\/li>\\n<\\/ul>\\n<p><strong>ACF Extended Basic *******:<\\/strong><\\/p>\\n<ul>\\n<li>Field: Flexible Content - Added missing &quot;Copy\\/Toggle Layout&quot; localized strings<\\/li>\\n<li>Field: Flexible Content - Added JS hook <code>acfe\\/flexible\\/preview\\/layout=my-layout<\\/code> variation<\\/li>\\n<li>Field: Flexible Content - Enhanced ACF UI CSS settings with &quot;Tabs Hidden&quot;<\\/li>\\n<li>Field: Forms - Fixed potential warning when switching from Checkbox to Radio<\\/li>\\n<li>Module: Form - Added safe guard logic for ACF fields in &quot;Load&quot; actions<\\/li>\\n<li>Module: Form - Fixed outsourced Clone Seamless Fields values<\\/li>\\n<li>Module: Form - Fixed Template Tags warning with PHP 8<\\/li>\\n<li>Module: Form - User - Builtin Validation now check if email is already used<\\/li>\\n<li>Module: Form - User - Builtin Validation now check the login during the insert<\\/li>\\n<li>Module: Form - User - Builtin Validation now check the <code>illegal_user_logins<\\/code> wp filter<\\/li>\\n<li>Module: Form - User - Updating user login now automatically re-log the user<\\/li>\\n<li>Global: Fixed Ajax Nonce verification compatibility with ACF 6.3.2<\\/li>\\n<\\/ul>\\n<h4> ******* <\\/h4>\\n<p><strong>ACF Extended Pro *******:<\\/strong><\\/p>\\n<ul>\\n<li>Field Group Location: Added &quot;Dashboard &gt; Widget&quot; Location<\\/li>\\n<li>Field Group Location: Dashboard Widget allows to update fields from the WP Dashboard<\\/li>\\n<li>Field Group Location: Added &quot;Woocommerce&quot; Cart, Checkout, Account, Shop &amp; Terms<\\/li>\\n<li>Modules: AutoSync - <code>acfe\\/php<\\/code> &amp; <code>acfe\\/json<\\/code> settings control the AutoSync metabox visbility<\\/li>\\n<\\/ul>\\n<p><strong>ACF Extended Basic *******:<\\/strong><\\/p>\\n<ul>\\n<li>Module: Form - Added ability to load form with ajax<\\/li>\\n<li>Module: Form - Added &quot;Validation &gt; Global Error&quot; settings to customize error messages<\\/li>\\n<li>Module: Form - Added Instruction Placement &quot;Tooltip&quot; &amp; &quot;Above Field&quot; options<\\/li>\\n<li>Module: Form - Cleaned front-end forms HTML markup<\\/li>\\n<li>Module: Form - Enhanced front-end forms JS logic<\\/li>\\n<li>Module: Form - Enhanced compatibility for multiple forms on the same page<\\/li>\\n<li>Module: Form - Enhanced <code>{field:gallery}<\\/code> formatted value<\\/li>\\n<li>Module: Form - Enhanced &quot;Validation &gt; Grouped Errors&quot; to use the &quot;Errors Class&quot; setting<\\/li>\\n<li>Module: Form - Fixed missing <code>l10n<\\/code> acf setting compatibility for export<\\/li>\\n<li>Module: Form - Fixed Shortcode usage in Success Message<\\/li>\\n<li>Module: Form - Fixed slashed text in Success Message template tags<\\/li>\\n<li>Module: Form - Post - Fixed Image\\/File\\/Gallery &quot;Uploaded to&quot; filter in Media Modal<\\/li>\\n<li>Module: Form - Post - Fixed Gallery attachement not being connected to created\\/updated post<\\/li>\\n<li>Module: Form - Post - Fixed Shortcode usage in Post Content<\\/li>\\n<li>Module: Form - Post - Fixed current post not displaying new data if updated without redirect<\\/li>\\n<li>Module: Form - Email - Fixed Shortcode usage in e-mail content\\/html<\\/li>\\n<li>Module: Form - User - &quot;Log In&quot; action doesn\\u2019t require to redirect on success anymore<\\/li>\\n<li>Module: Form - Added <code>acfe.renderForm()<\\/code> &amp; <code>acfe.renderFormAjax()<\\/code> JS helpers<\\/li>\\n<li>Module: Form - Added <code>acfe_enqueue_form()<\\/code> PHP helper<\\/li>\\n<li>Module: Form - Added <code>acfe_get_form_action()<\\/code> allowing dot notation &amp; default arguments<\\/li>\\n<li>Module: Form - Added <code>acfe\\/form\\/submit_success_data<\\/code> PHP hook to pass data to the JS<\\/li>\\n<li>Module: Form - Added <code>acfe\\/form\\/validation_begin<\\/code> JS hook to target front-end validation<\\/li>\\n<li>Module: Form - Added <code>acfe\\/form\\/validation_failure<\\/code> JS hook to target front-end validation<\\/li>\\n<li>Module: Form - Added <code>acfe\\/form\\/validation_success<\\/code> JS hook to target front-end validation<\\/li>\\n<li>Module: Form - Added <code>acfe\\/form\\/validation_complete<\\/code> JS hook to target front-end validation<\\/li>\\n<li>Field Groups: Advanced Settings - Added ability to assign field\\u2019s sub array settings using dot notation<\\/li>\\n<li>Field Groups: AutoSync - Added ability to remove existing Json\\/PHP sync from the Field Group UI<\\/li>\\n<li>Field Groups: AutoSync - <code>acfe\\/php<\\/code> &amp; <code>acfe\\/json<\\/code> settings now control the sync metabox visbility<\\/li>\\n<li>Field Groups: AutoSync - Added <code>acfe\\/settings\\/should_delete_php<\\/code> hook to control the file removal<\\/li>\\n<li>Field Groups: AutoSync - Added <code>acfe\\/settings\\/should_delete_json<\\/code> hook to control the file removal<\\/li>\\n<li>Field Groups: AutoSync - Fixed Json files not being correctly deleted when Field Group use a custom path<\\/li>\\n<li>Global: Modal - Renamed <code>new_modal<\\/code> js hook into <code>acfe\\/new_modal<\\/code> for consistency<\\/li>\\n<li>Global: Enhanced <code>acfe_get_post_id()<\\/code> helper<\\/li>\\n<\\/ul>\\n<h4> Version History <\\/h4>\\n<p>Full Changelog: <a href=\\"https:\\/\\/www.acf-extended.com\\/changelog\\">https:\\/\\/www.acf-extended.com\\/changelog<\\/a><\\/p>","faq":"<h4>Where can I find the documentation?<\\/h4>\\n<p>You\'ll find the documentation of every features on the <a href=\\"https:\\/\\/www.acf-extended.com\\/features\\">official ACF Extended website<\\/a>.<\\/p>\\n<h4>Where can I submit a bug report?<\\/h4>\\n<p>You can file a report on the <a href=\\"https:\\/\\/wordpress.org\\/support\\/plugin\\/acf-extended\\/\\">Plugin Support Forum<\\/a> or on the <a href=\\"https:\\/\\/github.com\\/acf-extended\\/ACF-Extended\\">Github Page<\\/a> if you prefer.<\\/p>\\n<h4>Where can I submit a feature request?<\\/h4>\\n<p>You can submit a feature request on the <a href=\\"https:\\/\\/wordpress.org\\/support\\/plugin\\/acf-extended\\/\\">Plugin Support Forum<\\/a>. Feature requests are all listed on the <a href=\\"https:\\/\\/trello.com\\/b\\/QEgpU7CL\\/acf-extended\\">Official Trello Board<\\/a>.<\\/p>\\n<h4>What is planned for the next update?<\\/h4>\\n<p>The content of the upcoming patch and work in progress features are all listed on the <a href=\\"https:\\/\\/www.acf-extended.com\\/roadmap\\">Official Roadmap<\\/a> (or the Trello Board).<\\/p>\\n"},"banners":{"high":"https:\\/\\/ps.w.org\\/acf-extended\\/assets\\/banner-1544x500.png","low":"https:\\/\\/ps.w.org\\/acf-extended\\/assets\\/banner-772x250.png"},"icons":{"1x":"https:\\/\\/www.acf-extended.com\\/wp-content\\/uploads\\/edd\\/acf-extended-256x256-1-128x128.png","2x":"https:\\/\\/www.acf-extended.com\\/wp-content\\/uploads\\/edd\\/acf-extended-256x256-1.png"},"tags":["acf","custom fields","meta","admin","fields"],"requires":"4.9","tested":"6.5","requires_php":"5.6","contributors":{"hwk-fr":{"display_name":"hwk-fr","profile":"\\/\\/profiles.wordpress.org\\/hwk-fr","avatar":"https:\\/\\/wordpress.org\\/grav-redirect.php?user=hwk-fr"}},"stable_tag":"*******","donate_link":"https:\\/\\/www.acf-extended.com","short_description":"All-in-one enhancement suite that improves WordPress &amp; Advanced Custom Fields.","license":"GPLv2 or later","screenshots":{"1":"Flexible Content Preview","2":"Flexible Content Modal","3":"New Fields","4":"Post Type List Location","5":"Self\\/Multi\\/Bidirectional Fields","6":"Developer Mode","7":"Dynamic Post Types","8":"Enhanced WordPress UI"},"faq":["<h4>Where can I find the documentation?<\\/h4>\\n<p>You\'ll find the documentation of every features on the <a href=\\"https:\\/\\/www.acf-extended.com\\/features\\">official ACF Extended website<\\/a>.<\\/p>\\n<h4>Where can I submit a bug report?<\\/h4>\\n<p>You can file a report on the <a href=\\"https:\\/\\/wordpress.org\\/support\\/plugin\\/acf-extended\\/\\">Plugin Support Forum<\\/a> or on the <a href=\\"https:\\/\\/github.com\\/acf-extended\\/ACF-Extended\\">Github Page<\\/a> if you prefer.<\\/p>\\n<h4>Where can I submit a feature request?<\\/h4>\\n<p>You can submit a feature request on the <a href=\\"https:\\/\\/wordpress.org\\/support\\/plugin\\/acf-extended\\/\\">Plugin Support Forum<\\/a>. Feature requests are all listed on the <a href=\\"https:\\/\\/trello.com\\/b\\/QEgpU7CL\\/acf-extended\\">Official Trello Board<\\/a>.<\\/p>\\n<h4>What is planned for the next update?<\\/h4>\\n<p>The content of the upcoming patch and work in progress features are all listed on the <a href=\\"https:\\/\\/www.acf-extended.com\\/roadmap\\">Official Roadmap<\\/a> (or the Trello Board).<\\/p>\\n"],"warnings":[],"description":["<p>&#x1f680; All-in-one enhancement suite that improves WordPress &amp; Advanced Custom Fields. This plugin aims to provide a powerful administration framework with a wide range of improvements &amp; optimizations.<\\/p>\\n<p><strong>This plugin requires at least ACF Pro 5.8.<\\/strong>\\nIf you don\'t already own <a href=\\"https:\\/\\/www.advancedcustomfields.com\\/pro\\/\\">ACF Pro<\\/a>, you should consider it. It\'s one of the most powerful WordPress plugin available.<\\/p>\\n<h3>&#x2b50; Highlight<\\/h3>\\n<ul>\\n<li>14+ New ACF Fields<\\/li>\\n<li>10+ ACF Fields Enhanced<\\/li>\\n<li>4+ New Field Groups Locations<\\/li>\\n<li>Self\\/Multi\\/Bidirectional Fields<\\/li>\\n<li>Advanced Fields Validation<\\/li>\\n<li>Flexible Content as Page Builder<\\/li>\\n<li>Optimize metadata with Performance Mode<\\/li>\\n<li>Advanced Front-End Forms Manager<\\/li>\\n<li>ACF Options Pages \\/ Block Types Manager<\\/li>\\n<li>ACF &amp; WordPress Meta Overview<\\/li>\\n<li>WordPress Post Types \\/ Taxonomies Manager<\\/li>\\n<li>WordPress Options Manager<\\/li>\\n<li>WordPress Admin Enhancements<\\/li>\\n<li>WPML &amp; Polylang Multilingual support<\\/li>\\n<li>... And many more features<h3>&#x1f48e; Pro Highlight<\\/h3><\\/li>\\n<li>20+ New ACF Fields<\\/li>\\n<li>10+ ACF Fields Enhanced<\\/li>\\n<li>20+ New Locations<\\/li>\\n<li>Payment Field with Stripe &amp; PayPal Express<\\/li>\\n<li>Flexible Content Grid System<\\/li>\\n<li>Flexible Content Layouts Locations Rules<\\/li>\\n<li>Templates Manager<\\/li>\\n<li>Builtin Classic Editor<\\/li>\\n<li>Settings UI<\\/li>\\n<li>Screen Layouts<\\/li>\\n<li>Force Json Sync<\\/li>\\n<li>Field Visibility Settings<\\/li>\\n<li>Global Field Conditional Rules<\\/li>\\n<li>... And many more features<h3>&#x1f91f; Philosophy<\\/h3><\\/li>\\n<li>Seamless integration<\\/li>\\n<li>No extra menu, ads or notices<\\/li>\\n<li>Built by developers, for developers<h3>&#x1f6e0;&#xfe0f; Links<\\/h3><\\/li>\\n<li><a href=\\"https:\\/\\/www.acf-extended.com\\">Website<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\">Documentation<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/www.acf-extended.com\\/guides\\">Guides<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/www.acf-extended.com\\/roadmap\\">Roadmap<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/github.com\\/acf-extended\\/ACF-Extended\\">GitHub<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/twitter.com\\/ACFExtended\\">Twitter<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/twitter.com\\/hwkfr\\">Twitter<\\/a> (Personal)<\\/li>\\n<li><a href=\\"https:\\/\\/slack.acf-extended.com\\">Slack Community<\\/a><h3>&#x1f9f0; Tools<\\/h3><\\/li>\\n<li><a href=\\"https:\\/\\/wordpress.org\\/plugins\\/acf-extended\\/#faq\\">FAQ<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/wordpress.org\\/support\\/plugin\\/acf-extended\\">Support<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/wordpress.org\\/support\\/plugin\\/acf-extended\\">Feature Request<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/wordpress.org\\/support\\/plugin\\/acf-extended\\/reviews\\/#new-post\\">Reviews<\\/a><\\/li>\\n<li><a href=\\"https:\\/\\/ko-fi.com\\/acfextended\\">Donation<\\/a><h3>&#x1f4c1; Field Groups<\\/h3>\\n<strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/advanced-settings\\">Advanced Settings<\\/a><\\/strong>\\nEnable advanced settings for all fields within the Field Group.<\\/li>\\n<\\/ul>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/autosync\\">Auto Sync PHP<\\/a><\\/strong>\\nAutomatically synchronize field groups with local PHP files upon field group updates. This feature will create, include and update a local PHP file for each field group.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/autosync\\">Auto Sync Json<\\/a><\\/strong>\\nControl which field groups you want to synchronize with local Json files. Display warnings if the Json file has been manually deleted.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/categories\\">Categories<\\/a><\\/strong>\\nSpice up your field groups with a custom taxonomy and filter field groups by terms.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/custom-key\\">Custom Key<\\/a><\\/strong>\\nSet custom field group key. Example: <code>group_custom_name<\\/code>.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/custom-meta\\">Custom Meta<\\/a><\\/strong>\\nAdd custom metas (key\\/value) in the field group administration.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/display-title\\">Display Title<\\/a><\\/strong>\\nDisplay an alternative field group title in post edition screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/field-group-ui\\">Field Group UI<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nEnable enhancements to the Field Group UI for a better user experience.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/force-sync\\">Force Sync<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nAlways keep Json files synchronized with the Field Groups in the database.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/hide-on-screen\\">Hide on Screen<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nHide Gutenberg Block Editor and 10+ more items to hide in the field group settings.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/instruction-placement\\">Instructions Placement<\\/a><\\/strong>\\nNew instruction placements let you display field description &quot;above the fields&quot; or in a &quot;tooltip&quot;.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/advanced-post\\">Location: Advanced Post<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nA collection of multiple new Field Groups locations allowing developers to target posts with specific conditions (Post author, date, slug, path etc...).<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/advanced-menu-item\\">Location: Advanced Menu Item<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nTarget specific &quot;Menu Item Depth&quot; or &quot;Menu Item Type&quot; from the Field Groups Locations rules.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/advanced-taxonomy\\">Location: Advanced Taxonomy Term<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nA collection of multiple new Field Groups locations allowing developers to target taxonomy and terms with specific conditions (Term name, parent, slug etc...).<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/all-post-types\\">Location: All post types<\\/a><\\/strong>\\nDisplay field groups on all post types edition screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/attachment-list\\">Location: Attachment List<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay field group on attachment admin list screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/dashboard\\">Location: Dashboard Widgets<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay field groups and update ACF Fields from the WP Dashboard.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/field-value\\">Location: Field Value<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a field group based on the field value of an another field group.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/post-type-archive\\">Location: Post type Archive<\\/a><\\/strong>\\nAdd an Archive Option Page under the Post Type admin menu. Display and save any field groups within it.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/post-type-list\\">Location: Post type List<\\/a><\\/strong>\\nDisplay field group on post types admin list screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/taxonomy-list\\">Location: Taxonomy List<\\/a><\\/strong>\\nDisplay field group on taxonomies admin list screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/user-list\\">Location: User List<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay field group on user admin list screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/woocommerce\\">Location: Woocommerce<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay field groups on Woocommerce pages.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/locations\\/wp-settings\\">Location: WP Settings<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay field groups on WP Settings pages: General, Writing, Reading, Discussion, Media and Permalinks.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/\\">Local Field Groups<\\/a><\\/strong>\\nDisplay local field groups that are loaded by ACF, but not available in the ACF field group administration. Example: Field groups that are registered in the <code>functions.php<\\/code> file, but not in the ACF UI.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/note\\">Note<\\/a><\\/strong>\\nAdd a personal note in the field group administration. Only visible to administrators.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/permissions\\">Permissions<\\/a><\\/strong>\\nAdd permission layer to field groups. Choose which roles can view &amp; edit field groups in the post edition screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-groups\\/raw-data\\">Raw Data<\\/a><\\/strong>\\nDisplay raw field group data in a modal to check your configuration &amp; settings.<h3>&#x2699;&#xfe0f; Fields Settings<\\/h3>\\n<strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/field-advanced-settings\\">Advanced Settings<\\/a><\\/strong>\\nA more sophisticated field settings based on specified location (administration\\/front-end). Example: Field is required only in front-end.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/advanced-validation\\">Advanced Validation<\\/a><\\/strong>\\nA more sophisticated validation conditions (AND\\/OR) with custom error messages based on specified location (administration\\/front-end).<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/bidirectional-fields\\">Self\\/Multi\\/Bidirectional fields<\\/a><\\/strong>\\nAn advanced bidirectional setting (also called post-to-post) is available for the following fields: Relationship, Post object, User &amp; Taxonomy terms. Fields will work bidirectionally and automatically update each others. Works in groups &amp; clones.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/field-visibility\\">Field Visibility<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nGet quick access to &quot;Field Visibility&quot;, &quot;Label Visibility&quot;, &quot;Instructions Visibility&quot; and &quot;Required Setting&quot; for the following screens: &quot;Everywhere&quot;, &quot;Front-end&quot; and &quot;Administration&quot;.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/global-condition\\">Global Condition<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nEnable Global Conditional Logic for a specific field, which can then be used in an another Field Group as condition, both as Field Group Condition and Field Condition.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/field-instruction-placement\\">Instruction Placement<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nOverride a specific field instruction placement to any position: Below labels, below fields, above fields or tooltip.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/instruction-read-more\\">Instruction Read More<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nAllow to expand instructions text with a &quot;Read More&quot; link. This feature is useful for lengthy instructions text.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/min-max\\">Min\\/Max<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nMinimum &amp; maximum items is a global field setting that let you define a specific number of items that can or should be added by the user.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/field-permissions\\">Permissions<\\/a><\\/strong>\\nAdd permission layer to fields. Choose which roles can view &amp; edit fields in the post edition screen. (can be combined with field groups permissions).<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/field-raw-data\\">Raw data<\\/a><\\/strong>\\nDisplay raw field data in a modal to check your configuration &amp; settings.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/field-settings\\/required-message\\">Required Message<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nThis setting allow developers to define a custom error message within the field settings for a more intuitive user experience.<h3>&#x1f3f7;&#xfe0f; Fields<\\/h3>\\n<strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/advanced-link\\">Advanced Link<\\/a><\\/strong>\\nDisplay a modern Link Selection in a modal. Posts, Post Types Archives &amp; terms selection can be filtered in the field administration.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/block-editor\\">Block Editor<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an isolated Block Editor field on admin screen (with Classic Editor enabled) or on the front-end.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/block-types\\">Block Types<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an ACF Block Types selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/button\\">Button<\\/a><\\/strong>\\nDisplay a custom submit or button. Built-in ajax call setting. Usage example available in the field administration.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/checkbox\\">Checkbox<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nDefine grouped choices values using <code>## Title<\\/code> markup in the field\\u2019s choices.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/clone\\">Clone<\\/a><\\/strong>\\nAllow users to edit clone fields in a modal. Choose the edit button text, display close button and the modal size.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/code-editor\\">Code Editor<\\/a><\\/strong>\\nEdit code using the native WP Core Codemirror library. Default languages: Text\\/HTML, Javascript, CSS, PHP mixed\\/plain.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/color-picker\\">Color Picker<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nA collection of advanced settings for the ACF Color Picker. The field can now be displayed as a palette, custom colors can be predefined and RGBA mode is supported.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/columns\\">Columns<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nOrganize and re-arrange your fields using columns and line-breaks. The field acts like the ACF Accordion\\/Tab field and allow you to create virtually grouped fields which will be displayed inside columns.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/countries\\">Countries<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Country selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/currencies\\">Currencies<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Currency selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/datepicker\\">Date\\/Timepicker<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nDisplay a modern UI of the ACF Datepicker field. CSS and icons have been enhanced to fit WordPress admin UI and colors.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/date-range-picker\\">Date Range Picker<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Date Range Picker. The field support a wide range of customization, such as: Placeholder, Default dates, Range Restriction, Date restriction, No weekends etc.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/dynamic-render\\">Dynamic Render<\\/a><\\/strong>\\nDisplay custom HTML\\/PHP content using a simple named hook.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/field-groups-selector\\">Field Groups<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an ACF Field Groups selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/field-types\\">Field Types<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an ACF Field Types selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/fields-selector\\">Fields<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an ACF Fields selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/file\\">File<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nChoose the uploader type, enable multi file upload and dropzone.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/flexible-content\\">Flexible Content<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nDisplayed an enhanced version of the native Flexible Content field. Dozens of new settings and settings were added, allowing developers to create the most advanced page builder and fully control the field\\u2019s behavior.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/forms\\">Forms<\\/a><\\/strong>\\nSelect any dynamic form (format: checkbox, radio or select).<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/google-map\\">Google Map<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nA collection of new settings added to the ACF Google Map Field that allow developers to have more control over the field behavior.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/recaptcha\\">Google reCaptcha<\\/a><\\/strong>\\nDisplay a reCaptcha field (compatible v2 &amp; v3).<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/group\\">Group<\\/a><\\/strong>\\nAllow users to edit group fields in a modal Choose the edit button text, display close button and the modal size<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/hidden-input\\">Hidden Input<\\/a><\\/strong>\\nDisplay a hidden input with custom name\\/value<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/image\\">Image<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nChoose the uploader type, customize the upload folder and set the image as post featured thumbnail<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/image-selector\\">Image Selector<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an Image Selector field.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/image-sizes\\">Image Sizes<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an Image Sizes selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/languages\\">Languages<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Language selector as radio, checkbox or select field type, compatible with WPML &amp; Polylang.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/menu-locations\\">Menu Locations<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Menu Locations selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/menus\\">Menus<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Menu selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/options-pages\\">Options Pages<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an ACF Options Pages selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/payment\\">Payment<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Payment Field that supports with Stripe &amp; PayPal Express gateways, working on both front-end and back-end.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/payment-cart\\">Payment Cart<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an optional Payment Cart to easily setup an e-commerce solution.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/payment-selector\\">Payment Selector<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an optional Payment Selector which let the user switch the payment gateway.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/phone-number\\">Phone Number<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a fully customizable international Phone Number field.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/post-field\\">Post Field<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nThe Post Field is a new field that allow developers to move native WordPress fields such as Post Title, Date, Status, Visibility, Permalink etc.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/post-formats\\">Post Formats<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay a Post Format selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\">Post Object<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nAllow user to enter custom value which will be saved as a new post, or enable the inline post creation\\/edit.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/post-statuses\\">Post Status<\\/a><\\/strong>\\nSelect any post status (format: checkbox, radio or select)<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/post-types\\">Post Types<\\/a><\\/strong>\\nSelect any post type (format: checkbox, radio or select)<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/radio\\">Radio<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nDefine grouped choices values using <code>## Title<\\/code> markup in the field\\u2019s choices.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/relationship\\">Relationship<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nThe Relationship field includes new settings allowing users to create and edit post on-the-fly from the post edit screen.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/repeater\\">Repeater<\\/a><\\/strong>\\nAdd stylised to \'Add Row\' button, lock rows and remove repeater\'s actions.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/select\\">Select<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nChange the default &quot;Select&quot; placeholder text and Search Input placeholder and allow user to enter custom values.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/slug\\">Slug<\\/a><\\/strong>\\nA slug text input (ie: <code>my-text-input<\\/code>).<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/tab\\">Tab<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisable the last opened tab user preference. Which means that when the user will refresh the page, it will always load the first tab.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/taxonomies\\">Taxonomies<\\/a><\\/strong>\\nSelect any taxonomy (format: checkbox, radio or select)<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/taxonomy-terms\\">Taxonomy Terms<\\/a><\\/strong>\\nSelect any terms of any taxonomies, allow specific terms, level or childrens (format: checkbox or select). Terms can be loaded &amp; saved for the current post (just like the native ACF Taxonomy field)<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/templates-selector\\">Templates<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nDisplay an ACF Extended Templates selector as radio, checkbox or select field type.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/textarea\\">Textarea<\\/a><\\/strong>\\nSwitch font family to monospace and allow tab indent.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/true-false\\">True\\/False<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nFive new styles have been added to the native True\\/False field.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/user-roles\\">User Roles<\\/a><\\/strong>\\nSelect any user role (format: checkbox, radio or select)<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/fields\\/wysiwyg-editor\\">WYSIWYG Editor<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nNew settings allowing developers to have more control over the field behavior.<h3>&#x1f6e0;&#xfe0f; Modules<\\/h3>\\n<strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/dynamic-block-types\\">Block Types UI<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nThe Dynamic Block Types module allows you to register and manage ACF Block Types from your WordPress admin, in ACF &gt; Block Types menu. Pro version allows to sync Json\\/PHP files.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/classic-editor\\">Classic Editor<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nACF Extended is bundled with a custom merged version of the Classic Editor &amp; Disable Gutenberg plugins.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/developer-mode\\">Developer Mode<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nThe Developer Mode allow you to view all Posts, Terms, Users &amp; Options custom metadata in a readable format. This feature is very useful to check what is actually saved in any WordPress Object.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/dynamic-forms\\">Forms<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nManage Advanced ACF Forms from the WordPress administration. This module is an enhanced version of the native ACF Form feature. While all native settings can be used, Dynamic Forms adds many new settings and introduce &quot;Actions&quot; for a complete control over the form behavior.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/dynamic-options-pages\\">Options Pages UI<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nThe Dynamic Options Pages module allows you to register and manage ACF Options Pages from your WordPress admin, in ACF &gt; Options Pages menu. Pro version allows to sync Json\\/PHP files.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/performance-mode\\">Performance Mode<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nA unique module that allows developers to optimize database load when dealing with hundreds or thousands of metadata with two different methods: Ultra &amp; Hybrid Engines.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/dynamic-post-types\\">Post Types UI<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nThe Dynamic Post Types module allows you to register and manage custom post types from your WordPress admin, in Tools &gt; Post Types menu. Pro version allows to sync Json\\/PHP files.<\\/p>\\n<p>All native post types settings can be set within the UI. ACF Extended also adds more advanced settings allowing to manage posts per page, order etc\\u2026<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/rewrite-rules\\">Rewrite Rules<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nGet an overview of all WordPress permalinks structures and rules. Test URLs, export rules and flush permalinks from the UI.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/scripts\\">Scripts UI<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nRun custom scripts on thousands of posts. Including builtin &quot;Orphan Meta Cleaner&quot;, &quot;Script Launcher&quot; and &quot;Performance Converter&quot; scripts.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/settings-ui\\">Settings UI<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nThe Settings UI allows developers to get an overview of all ACF and ACF Extended settings values from the ACF &gt; Settings menu.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/dynamic-taxonomies\\">Taxonomies UI<\\/a><\\/strong> <strong>(FREE \\/ PRO)<\\/strong>\\nThe Dynamic Taxonomies module allows you to register and manage custom taxonomies from your WordPress admin, in Tools &gt; Taxonomies menu. Pro version allows to sync Json\\/PHP files.<\\/p>\\n<p>All native taxonomies settings can be set within the UI. ACF Extended also adds more advanced settings allowing to manage posts per page, order etc\\u2026<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/modules\\/templates\\">Templates<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nManage default ACF values in an advanced way and sync templates with Json\\/PHP files.<h3>&#x1f5a5;&#xfe0f; WordPress<\\/h3>\\n<strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/wordpress\\/ajax-author-box\\">Ajax Author Box<\\/a><\\/strong>\\nThe native WP Author Metabox has been replaced with an Ajax version allowing to manage thousands of users without slowing down the post administration. The new Author box also include an inline search input.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/wordpress\\/enhanced-ui\\">Enhanced UI<\\/a><\\/strong>\\nThe Taxonomy, User profile &amp; Settings views have been enhanced for a more consistent administration experience, using CSS\\/JS only.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/wordpress\\/polylang\\">Polylang<\\/a><\\/strong>\\nACF Extended adds a new layer of compatibility for Polylang. ACF Options Pages and all ACF Extended Modules (Dynamic Post Type, Taxonomy, Options Pages, Block Type) are compatible.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/wordpress\\/screen-layouts\\">Screen Layouts<\\/a><\\/strong> <strong>(PRO)<\\/strong>\\nPost Edit screens have been enhanced allowing up to 3 columns layout and multiple variations.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/wordpress\\/wpml\\">WPML<\\/a><\\/strong>\\nACF Extended adds a new layer of compatibility for WPML. ACF Options Pages and all ACF Extended Modules (Dynamic Post Type, Taxonomy, Options Pages, Block Type) are compatible.<\\/p>\\n<p><strong><a href=\\"https:\\/\\/www.acf-extended.com\\/features\\/wordpress\\/options\\">WP Options<\\/a><\\/strong>\\nManage WordPress Options from the Settings &gt; Options page. Options value (strings, serialized &amp; Json) will be displayed in a readable form. Values can be edited or deleted.<h3>&#x2764;&#xfe0f; Early Supporters<\\/h3><\\/p>\\n<ul>\\n<li>Thanks to <a href=\\"https:\\/\\/twitter.com\\/AsmussenBrandon\\">Brandon A.<\\/a> for his support &amp; tests<\\/li>\\n<li>Thanks to <a href=\\"https:\\/\\/twitter.com\\/DamChtlv\\">Damien C.<\\/a> for his support &amp; tests<\\/li>\\n<li>Thanks to <a href=\\"https:\\/\\/twitter.com\\/Val_Pellegrin\\">Valentin P.<\\/a> for his support &amp; tests<\\/li>\\n<li>Thanks to Damian P. for his support &amp; tests<\\/li>\\n<li>Thanks to <a href=\\"https:\\/\\/twitter.com\\/jsaarenk\\">Jaakko S.<\\/a> for his support &amp; tests<\\/li>\\n<li>Thanks to <a href=\\"https:\\/\\/twitter.com\\/altendorfme\\">Renan A.<\\/a> for his support &amp; tests<h3>&#x1f970; Donors<\\/h3><\\/li>\\n<li>Thanks to RavenSays<\\/li>\\n<li>Thanks to Dave A.<\\/li>\\n<li>Thanks to Rob H.<\\/li>\\n<li>Thanks to Valentin P.<\\/li>\\n<li>Thanks to Alan A.<\\/li>\\n<li>Thanks to Damian C.<\\/li>\\n<li>Thanks to Andrew<\\/li>\\n<li>Thanks to Kimantis<\\/li>\\n<li>Thanks to Anonymous<\\/li>\\n<li>Thanks to Chris<\\/li>\\n<li>Thanks to Dennis D.<\\/li>\\n<li>Thanks to Cody R.<\\/li>\\n<li>Thanks to Jamie<\\/li>\\n<li>Thanks to Dave A.<\\/li>\\n<li>Thanks to Paul M.<\\/li>\\n<li>Thanks to David B.<\\/li>\\n<li>Thanks to Swingjac<\\/li>\\n<li>Thanks to Erik<\\/li>\\n<li>Thanks to Giancarlo P.<\\/li>\\n<li>Thanks to Geuer M.<\\/li>\\n<\\/ul>"],"installation":["<h4> Wordpress Install <\\/h4>\\n<ol>\\n<li>Install Advanced Custom Fields: Pro<\\/li>\\n<li>Upload the plugin files to the <code>\\/wp-content\\/plugins\\/acf-extended\\/<\\/code> directory, or install the plugin through the WordPress plugins screen directly.<\\/li>\\n<li>Activate the plugin through the \'Plugins\' screen in WordPress.<\\/li>\\n<li>Everything is ready!<\\/li>\\n<\\/ol>"],"changelog":["<h4> ******* <\\/h4>\\n<p><strong>ACF Extended Pro *******:<\\/strong><\\/p>\\n<ul>\\n<li>Field: Block Editor - Added WP 6.6 compatibility<\\/li>\\n<\\/ul>\\n<p><strong>ACF Extended Basic *******:<\\/strong><\\/p>\\n<ul>\\n<li>General: Updated WP 6.6 metabox order handle positioning<\\/li>\\n<\\/ul>\\n<h4> ******* <\\/h4>\\n<p><strong>ACF Extended Pro *******:<\\/strong><\\/p>\\n<ul>\\n<li>Field: Flexible Content - Grid - Added filter to rename columns (1\\/12, 2\\/12, 3\\/12\\u2026)<\\/li>\\n<li>Field: Google Map - Added small UI settings tweaks<\\/li>\\n<li>Field: Payment - Removed Polyfill library dependency<\\/li>\\n<li>Field Settings: Global Condition - Fixed Global Field Condition operators with ACF 6.3<\\/li>\\n<li>Field Settings: Instructions Tooltip - Fixed tooltip always visible when switching tab<\\/li>\\n<li>Module: Force Sync - Fixed sync from Dashboard with Flexible Content Toggle Layout<\\/li>\\n<li>Module: Force Sync - Enhanced Force Delete compatibility with Json and PHP sync<\\/li>\\n<\\/ul>\\n<p><strong>ACF Extended Basic *******:<\\/strong><\\/p>\\n<ul>\\n<li>Field: Flexible Content - Added missing &quot;Copy\\/Toggle Layout&quot; localized strings<\\/li>\\n<li>Field: Flexible Content - Added JS hook <code>acfe\\/flexible\\/preview\\/layout=my-layout<\\/code> variation<\\/li>\\n<li>Field: Flexible Content - Enhanced ACF UI CSS settings with &quot;Tabs Hidden&quot;<\\/li>\\n<li>Field: Forms - Fixed potential warning when switching from Checkbox to Radio<\\/li>\\n<li>Module: Form - Added safe guard logic for ACF fields in &quot;Load&quot; actions<\\/li>\\n<li>Module: Form - Fixed outsourced Clone Seamless Fields values<\\/li>\\n<li>Module: Form - Fixed Template Tags warning with PHP 8<\\/li>\\n<li>Module: Form - User - Builtin Validation now check if email is already used<\\/li>\\n<li>Module: Form - User - Builtin Validation now check the login during the insert<\\/li>\\n<li>Module: Form - User - Builtin Validation now check the <code>illegal_user_logins<\\/code> wp filter<\\/li>\\n<li>Module: Form - User - Updating user login now automatically re-log the user<\\/li>\\n<li>Global: Fixed Ajax Nonce verification compatibility with ACF 6.3.2<\\/li>\\n<\\/ul>\\n<h4> ******* <\\/h4>\\n<p><strong>ACF Extended Pro *******:<\\/strong><\\/p>\\n<ul>\\n<li>Field Group Location: Added &quot;Dashboard &gt; Widget&quot; Location<\\/li>\\n<li>Field Group Location: Dashboard Widget allows to update fields from the WP Dashboard<\\/li>\\n<li>Field Group Location: Added &quot;Woocommerce&quot; Cart, Checkout, Account, Shop &amp; Terms<\\/li>\\n<li>Modules: AutoSync - <code>acfe\\/php<\\/code> &amp; <code>acfe\\/json<\\/code> settings control the AutoSync metabox visbility<\\/li>\\n<\\/ul>\\n<p><strong>ACF Extended Basic *******:<\\/strong><\\/p>\\n<ul>\\n<li>Module: Form - Added ability to load form with ajax<\\/li>\\n<li>Module: Form - Added &quot;Validation &gt; Global Error&quot; settings to customize error messages<\\/li>\\n<li>Module: Form - Added Instruction Placement &quot;Tooltip&quot; &amp; &quot;Above Field&quot; options<\\/li>\\n<li>Module: Form - Cleaned front-end forms HTML markup<\\/li>\\n<li>Module: Form - Enhanced front-end forms JS logic<\\/li>\\n<li>Module: Form - Enhanced compatibility for multiple forms on the same page<\\/li>\\n<li>Module: Form - Enhanced <code>{field:gallery}<\\/code> formatted value<\\/li>\\n<li>Module: Form - Enhanced &quot;Validation &gt; Grouped Errors&quot; to use the &quot;Errors Class&quot; setting<\\/li>\\n<li>Module: Form - Fixed missing <code>l10n<\\/code> acf setting compatibility for export<\\/li>\\n<li>Module: Form - Fixed Shortcode usage in Success Message<\\/li>\\n<li>Module: Form - Fixed slashed text in Success Message template tags<\\/li>\\n<li>Module: Form - Post - Fixed Image\\/File\\/Gallery &quot;Uploaded to&quot; filter in Media Modal<\\/li>\\n<li>Module: Form - Post - Fixed Gallery attachement not being connected to created\\/updated post<\\/li>\\n<li>Module: Form - Post - Fixed Shortcode usage in Post Content<\\/li>\\n<li>Module: Form - Post - Fixed current post not displaying new data if updated without redirect<\\/li>\\n<li>Module: Form - Email - Fixed Shortcode usage in e-mail content\\/html<\\/li>\\n<li>Module: Form - User - &quot;Log In&quot; action doesn\\u2019t require to redirect on success anymore<\\/li>\\n<li>Module: Form - Added <code>acfe.renderForm()<\\/code> &amp; <code>acfe.renderFormAjax()<\\/code> JS helpers<\\/li>\\n<li>Module: Form - Added <code>acfe_enqueue_form()<\\/code> PHP helper<\\/li>\\n<li>Module: Form - Added <code>acfe_get_form_action()<\\/code> allowing dot notation &amp; default arguments<\\/li>\\n<li>Module: Form - Added <code>acfe\\/form\\/submit_success_data<\\/code> PHP hook to pass data to the JS<\\/li>\\n<li>Module: Form - Added <code>acfe\\/form\\/validation_begin<\\/code> JS hook to target front-end validation<\\/li>\\n<li>Module: Form - Added <code>acfe\\/form\\/validation_failure<\\/code> JS hook to target front-end validation<\\/li>\\n<li>Module: Form - Added <code>acfe\\/form\\/validation_success<\\/code> JS hook to target front-end validation<\\/li>\\n<li>Module: Form - Added <code>acfe\\/form\\/validation_complete<\\/code> JS hook to target front-end validation<\\/li>\\n<li>Field Groups: Advanced Settings - Added ability to assign field\\u2019s sub array settings using dot notation<\\/li>\\n<li>Field Groups: AutoSync - Added ability to remove existing Json\\/PHP sync from the Field Group UI<\\/li>\\n<li>Field Groups: AutoSync - <code>acfe\\/php<\\/code> &amp; <code>acfe\\/json<\\/code> settings now control the sync metabox visbility<\\/li>\\n<li>Field Groups: AutoSync - Added <code>acfe\\/settings\\/should_delete_php<\\/code> hook to control the file removal<\\/li>\\n<li>Field Groups: AutoSync - Added <code>acfe\\/settings\\/should_delete_json<\\/code> hook to control the file removal<\\/li>\\n<li>Field Groups: AutoSync - Fixed Json files not being correctly deleted when Field Group use a custom path<\\/li>\\n<li>Global: Modal - Renamed <code>new_modal<\\/code> js hook into <code>acfe\\/new_modal<\\/code> for consistency<\\/li>\\n<li>Global: Enhanced <code>acfe_get_post_id()<\\/code> helper<\\/li>\\n<\\/ul>\\n<h4> Version History <\\/h4>\\n<p>Full Changelog: <a href=\\"https:\\/\\/www.acf-extended.com\\/changelog\\">https:\\/\\/www.acf-extended.com\\/changelog<\\/a><\\/p>"],"plugin":"acf-extended-pro\\/acf-extended.php","id":"acf-extended-pro\\/acf-extended.php"}";}', 'off') ;
INSERT INTO `nd_options` ( `option_id`, `option_name`, `option_value`, `autoload`) VALUES
(882, 'options_index_page_id', '177', 'off'),
(883, '_options_index_page_id', 'field_66a8bcbb6f66b', 'off'),
(884, 'options_index', '', 'off'),
(885, '_options_index', 'field_66a8bca16f66a', 'off'),
(908, 'wpso_activation', '1', 'auto'),
(911, 'wpso_options', 'a:3:{s:7:"objects";s:0:"";s:4:"tags";a:4:{i:0;s:12:"pub_category";i:1;s:14:"pub_chronology";i:2;s:14:"pub_dance_type";i:3;s:10:"pub_format";}s:6:"extras";s:0:"";}', 'auto'),
(1074, 'wpmdb_usage', 'a:2:{s:6:"action";s:4:"pull";s:4:"time";i:1728377672;}', 'off'),
(1794, 'options_push_content_variations_0_title', 'Devenez contributeur', 'off') ;
INSERT INTO `nd_options` ( `option_id`, `option_name`, `option_value`, `autoload`) VALUES
(1795, '_options_push_content_variations_0_title', 'field_66d6e6e0601be', 'off'),
(1796, 'options_push_content_variations_0_txt', 'Vous souhaitez promouvoir votre collection vidéo tout en conservant la propriété de vos œuvres. Bienvenue chez Numeridanse.', 'off'),
(1797, '_options_push_content_variations_0_txt', 'field_66d6e8301d2d1', 'off'),
(1798, 'options_push_content_variations_0_link', 'a:4:{s:4:"type";s:4:"post";s:5:"value";s:3:"145";s:5:"title";s:20:"Devenez contributeur";s:6:"target";b:0;}', 'off'),
(1799, '_options_push_content_variations_0_link', 'field_66d6e85b1d2d2', 'off'),
(1800, 'options_push_content_variations_0_img_id', '470', 'off'),
(1801, '_options_push_content_variations_0_img_id', 'field_66d6e8bb1d2d3', 'off'),
(1802, 'options_push_content_variations', '2', 'off'),
(1803, '_options_push_content_variations', 'field_66d6e5cd00959', 'off'),
(1808, 'options_push_content_variations_1_title', 'Voluptate proident ad officia ullamco laboris nulla nisi dolore incididunt ullamco', 'off'),
(1809, '_options_push_content_variations_1_title', 'field_66d6e6e0601be', 'off'),
(1810, 'options_push_content_variations_1_txt', 'Ex id cupidatat anim consectetur eu ad id. Do id mollit adipisicing commodo.\r\nIrure non eu deserunt occaecat non ea. Officia non non in enim et. Magna do proident elit consequat magna. Amet tempor qui do esse incididunt consectetur quis pariatur. Irure non pariatur deserunt. In ullamco fugiat officia est elit anim reprehenderit ut duis adipisicing cupidatat nisi.', 'off'),
(1811, '_options_push_content_variations_1_txt', 'field_66d6e8301d2d1', 'off'),
(1812, 'options_push_content_variations_1_link', 'a:4:{s:4:"type";s:4:"post";s:5:"value";s:3:"177";s:5:"title";s:28:"Nostrud fugiat reprehenderit";s:6:"target";b:0;}', 'off'),
(1813, '_options_push_content_variations_1_link', 'field_66d6e85b1d2d2', 'off'),
(1814, 'options_push_content_variations_1_img_id', '446', 'off'),
(1815, '_options_push_content_variations_1_img_id', 'field_66d6e8bb1d2d3', 'off'),
(2099, 'tax_pub_category_options_pub_category_event_id', '13', 'off'),
(2100, '_tax_pub_category_options_pub_category_event_id', 'field_66f1882171185', 'off'),
(2445, 'acf-field-group-category_children', 'a:0:{}', 'auto'),
(2607, 'fs_active_plugins', 'O:8:"stdClass":3:{s:7:"plugins";a:1:{s:27:"password-protected/freemius";O:8:"stdClass":4:{s:7:"version";s:5:"2.7.2";s:4:"type";s:6:"plugin";s:9:"timestamp";i:**********;s:11:"plugin_path";s:41:"password-protected/password-protected.php";}}s:7:"abspath";s:52:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp/";s:6:"newest";O:8:"stdClass":5:{s:11:"plugin_path";s:41:"password-protected/password-protected.php";s:8:"sdk_path";s:27:"password-protected/freemius";s:7:"version";s:5:"2.7.2";s:13:"in_activation";b:0;s:9:"timestamp";i:**********;}}', 'auto'),
(2608, 'fs_debug_mode', '', 'auto'),
(2609, 'fs_accounts', 'a:7:{s:21:"id_slug_type_path_map";a:1:{i:12503;a:3:{s:4:"slug";s:23:"password-protected-free";s:4:"type";s:6:"plugin";s:4:"path";s:41:"password-protected/password-protected.php";}}s:11:"plugin_data";a:1:{s:23:"password-protected-free";a:17:{s:19:"last_load_timestamp";i:**********;s:16:"plugin_main_file";O:8:"stdClass":1:{s:4:"path";s:41:"password-protected/password-protected.php";}s:20:"is_network_activated";b:0;s:17:"install_timestamp";i:**********;s:17:"was_plugin_loaded";b:1;s:21:"is_plugin_new_install";b:1;s:16:"sdk_last_version";N;s:11:"sdk_version";s:5:"2.7.2";s:16:"sdk_upgrade_mode";b:1;s:18:"sdk_downgrade_mode";b:0;s:19:"plugin_last_version";N;s:14:"plugin_version";s:5:"2.7.3";s:19:"plugin_upgrade_mode";b:1;s:21:"plugin_downgrade_mode";b:0;s:17:"connectivity_test";a:6:{s:12:"is_connected";N;s:4:"host";s:20:"local.numeridanse.tv";s:9:"server_ip";s:9:"127.0.0.1";s:9:"is_active";b:1;s:9:"timestamp";i:**********;s:7:"version";s:5:"2.7.3";}s:15:"prev_is_premium";b:0;s:12:"is_anonymous";a:3:{s:2:"is";b:1;s:9:"timestamp";i:1727854787;s:7:"version";s:5:"2.7.3";}}}s:13:"file_slug_map";a:1:{s:41:"password-protected/password-protected.php";s:23:"password-protected-free";}s:7:"plugins";a:1:{s:23:"password-protected-free";O:9:"FS_Plugin":25:{s:2:"id";s:5:"12503";s:7:"updated";N;s:7:"created";N;s:22:"\0FS_Entity\0_is_updated";b:0;s:10:"public_key";s:32:"pk_e9210517721d27b5112fa7773a600";s:10:"secret_key";N;s:16:"parent_plugin_id";N;s:5:"title";s:18:"Password Protected";s:4:"slug";s:23:"password-protected-free";s:12:"premium_slug";s:34:"password-protected-premium-premium";s:4:"type";s:6:"plugin";s:20:"affiliate_moderation";b:0;s:19:"is_wp_org_compliant";b:1;s:22:"premium_releases_count";N;s:4:"file";s:41:"password-protected/password-protected.php";s:7:"version";s:5:"2.7.3";s:11:"auto_update";N;s:4:"info";N;s:10:"is_premium";b:0;s:14:"premium_suffix";s:9:"(Premium)";s:7:"is_live";b:1;s:9:"bundle_id";N;s:17:"bundle_public_key";N;s:17:"opt_in_moderation";N;s:11:"_is_updated";b:0;}}s:12:"gc_timestamp";a:0:{}s:10:"theme_data";a:0:{}s:9:"unique_id";s:32:"ac376023d3b925a7ec461ac9705577d4";}', 'auto'),
(2610, 'fs_api_cache', 'a:0:{}', 'off'),
(2613, 'password_protected_version', '2.7.3', 'auto'),
(2616, 'password_protected_1.5_update_database', '1', 'auto'),
(2617, 'password_protected_status', '1', 'auto'),
(2618, 'password_protected_feeds', '0', 'auto'),
(2619, 'password_protected_rest', '0', 'auto'),
(2620, 'password_protected_administrators', '0', 'auto'),
(2621, 'password_protected_users', '0', 'auto'),
(2622, 'password_protected_password', 'c442af9a0087c0081797f1b1fd480711', 'auto'),
(2623, 'password_protected_allowed_ip_addresses', '', 'auto'),
(2624, 'password_protected_remember_me_lifetime', '14', 'auto'),
(2774, 'seopress_instant_indexing_log_option_name', 'a:1:{s:5:"error";s:39:"Aucun moteur de recherche sélectionné";}', 'off'),
(3089, 'edd_sl_1527ed503f26c35768c408d386205567', 'a:2:{s:7:"timeout";i:1728324547;s:5:"value";s:36795:"{"new_version":"8.2.2","stable_version":"8.2.2","name":"SEOPress PRO","slug":"wp-seopress-pro","url":"https:\\/\\/www.seopress.org\\/fr\\/downloads\\/seopress-pro\\/?changelog=1","last_updated":"2024-10-07 13:52:35","homepage":"https:\\/\\/www.seopress.org\\/","package":"https:\\/\\/www.seopress.org\\/fr\\/edd-sl\\/package_download\\/MTcyODQwNzE2OTpmNmVlMzk4Nzc3OGNkM2I4ODg3ODQ3N2Y2YmU0MmU3NDoxMTU6YTA4NzllZTczOWVhYmFmOTZkOGYzYzE4NWRiY2ZlOTA6aHR0cHNALy9kZXYubnVtZXJpZGFuc2UuY29tOjA=","download_link":"https:\\/\\/www.seopress.org\\/fr\\/edd-sl\\/package_download\\/MTcyODQwNzE2OTpmNmVlMzk4Nzc3OGNkM2I4ODg3ODQ3N2Y2YmU0MmU3NDoxMTU6YTA4NzllZTczOWVhYmFmOTZkOGYzYzE4NWRiY2ZlOTA6aHR0cHNALy9kZXYubnVtZXJpZGFuc2UuY29tOjA=","sections":{"description":"<h3>Best SEO plugin for WordPress fully integrated with all page builders and themes!<\\/h3>\\n<h3>Now with AI (GPT 4) to automagically generate meta title, description and alternative texts for images!<\\/h3>\\n<p>SEOPress is a powerful WordPress SEO plugin to optimize your SEO, boost your traffic, improve social sharing, build custom HTML and XML Sitemaps, create optimized breadcrumbs, add schemas \\/ Google Structured data types, manage 301 redirections and so much more.<br \\/><\\/p>\\n<p>&#x2714; <strong><a href=\\"https:\\/\\/www.seopress.org\\/features\\/page-builders-integration\\/\\">Universal SEO metabox<\\/a>: edit all your SEO from any page builder \\/ theme builder. No more back and forth between your editor and the WordPress administration<\\/strong>\\n&#x2714; <strong>No advertising, no footprints, white label, in backend AND frontend<\\/strong>\\n&#x2714; <strong>Content analysis to help you write content optimized for search engines with unlimited target keywords<\\/strong>\\n&#x2714; <strong><a href=\\"https:\\/\\/www.seopress.org\\/solutions\\/migrate-from\\/\\">Import your post and term metadatas<\\/a> from other SEO plugins or CSV file in 1 click<\\/strong>\\n&#x2714; <strong><a href=\\"https:\\/\\/translate.wordpress.org\\/projects\\/wp-plugins\\/wp-seopress\\">Translated into 25 languages (and counting)<\\/a><\\/strong>\\n&#x2714; <strong>Trusted by over 300,000 WordPress websites since 2017<\\/strong><\\/p>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Buy SEOPress PRO: only $49 \\/ year \\/ 5 sites<\\/strong><\\/a><\\/p>\\n<p>[youtube <a href=\\"https:\\/\\/www.youtube.com\\/watch?v=4ysKFVr_nu0\\">https:\\/\\/www.youtube.com\\/watch?v=4ysKFVr_nu0<\\/a>]<\\/p>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/features\\/\\">Features<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/solutions\\/migrate-from\\/\\">Migrate<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/wordpress-seo-plugins\\/pro\\/\\">PRO<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/integrations\\/\\">Integrations<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/support\\/\\">Support<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/features\\/seopress-white-label\\/\\">White Label<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/features\\/openai\\/\\">AI<\\/a><\\/p>\\n<h3>Why SEOPress is the best WordPress SEO plugin?<\\/h3>\\n<ul>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/seopress-productivity\\/\\"><strong>Save time<\\/strong><\\/a>: you prefer to work with Excel or Google Spreadsheets? No problem, you can import \\/ export your metadata from CSV files with SEOPress PRO in few clicks!<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Save money<\\/strong><\\/a>: SEOPress PRO is available for $49 \\/ year \\/ 5 sites. Go unlimited sites for just $149 \\/ year!<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/\\"><strong>All in one SEO plugin<\\/strong><\\/a>: comes with all the features you need to optimize the SEO of your WordPress site. No need to install additional extensions to manage redirects, schemas, XML sitemaps... You reduce the risk of conflicts and maintenance costs. You don\'t need a special feature? Deactivate it with one click without losing your configuration. Child\'s play !<\\/li>\\n    <li><strong>Easy AND ready to use<\\/strong>: you doesn\'t need to know SEO or code to use SEOPress. Most of the parameters are automatically set. And thanks to our installation wizard, configuring SEOPress has never been easier. To go further, we provide many <a href=\\"https:\\/\\/www.seopress.org\\/support\\/\\">free tutorials<\\/a> and <a href=\\"https:\\/\\/www.seopress.org\\/support\\/ebooks\\/\\">ebooks to learn SEO<\\/a> in order to better understand how to position your content on search engines.<\\/li>\\n<\\/ul>\\n<h3>SEOPress Free Features<\\/h3>\\n<ul>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/google-indexing-api-with-seopress\\/\\"><strong>Google Indexing API and IndexNow API (Bing \\/ Yandex)<\\/strong><\\/a> to quickly index its content in these search engines<\\/li>\\n    <li><strong>Installation wizard<\\/strong> to quickly setup your site<\\/li>\\n    <li><strong>Content analysis<\\/strong> with unlimited keywords to help you write optimized content for search engines<\\/li>\\n    <li><strong>Mobile \\/ Desktop Google Preview<\\/strong> to see how your post will looks like in Google search results<\\/li>\\n    <li><strong>Facebook &amp; X (ex-Twitter) Social Preview<\\/strong> to see how your post will looks like on social media to increase conversions<\\/li>\\n    <li><strong>Titles<\\/strong> (with <a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/manage-titles-meta-descriptions\\/\\">dynamic variables<\\/a>: custom fields, terms taxonomie...)<\\/li>\\n    <li><strong>Meta descriptions<\\/strong> (with dynamic variables too)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/social-media\\/\\"><strong>Open Graph and X (ex-Twitter) Cards<\\/strong><\\/a> to improve social media sharing (Facebook, LinkedIn, Instagram, X (ex-Twitter), Pinterest, WhatsApp...)<\\/li>\\n    <li><strong>Google Knowledge Graph<\\/strong><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-analytics\\/\\"><strong>Google Analytics<\\/strong> and <strong>Matomo<\\/strong><\\/a> with downloads tracking, custom dimensions, ip anonymization, remarketing, demographics and interest reporting, cross-domain tracking...(<a href=\\"https:\\/\\/www.seopress.org\\/features\\/seopress-white-label\\/\\">GDPR compatibility<\\/a>)<\\/li>\\n    <li><strong>Microsoft Clarity integration<\\/strong>: to capture session recordings, get instant heatmaps and powerful Insights for Free. Know how people interact with your site to improve user experience and conversions<\\/li>\\n    <li><strong>Custom Canonical URL<\\/strong><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/manage-meta-robots\\/\\"><strong>Meta robots<\\/strong><\\/a> (noindex, nofollow, noimageindex, noarchive, nosnippet)<\\/li>\\n    <li>Build your <a href=\\"https:\\/\\/www.seopress.org\\/features\\/sitemaps\\/\\"><strong>custom XML Sitemap<\\/strong><\\/a> to improve search indexing<\\/li>\\n    <li><strong>Image XML Sitemaps<\\/strong> to improve search indexing for Google Images<\\/li>\\n    <li>Build your custom <strong>HTML Sitemap<\\/strong> to enhanced navigation for visitors and improve search indexing<\\/li>\\n    <li>Link your social media accounts to your site<\\/li>\\n    <li><strong>Redirections<\\/strong> in post, pages, custom post types<\\/li>\\n    <li>Remove \\/category\\/ in URLs<\\/li>\\n    <li>Remove \\/product-category\\/ in URLs<\\/li>\\n    <li>Remove ?replytocom to avoid duplicate content<\\/li>\\n    <li>Redirect attachment pages to post parent<\\/li>\\n    <li>Redirect attachment pages to their file URL<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/image-seo\\/\\"><strong>Image SEO<\\/strong><\\/a>: Automatically set the image title \\/ alt \\/ caption \\/ description<\\/li>\\n    <li>Import \\/ Export settings from site to site.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/solutions\\/migrate-from\\/\\">Import posts and terms metadata<\\/a> from Yoast SEO, All In One SEO, SEO Framework, Rank Math, SEO Ultimate, WP Meta SEO, Premium SEO Pack, Squirrly and many other SEO plugins<\\/li>\\n    <li>...<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/wordpress-seo-plugins\\/free\\/features\\/\\">Check out all SEOPress Free features here<\\/a><\\/p>\\n<h3>SEOPress PRO: to go further with your SEO<\\/h3>\\n<p>[youtube <a href=\\"https:\\/\\/youtu.be\\/zxGCY-bJYwE\\">https:\\/\\/youtu.be\\/zxGCY-bJYwE<\\/a>]<\\/p>\\n<ul>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/openai\\/\\"><strong>Use Artificial Intelligence (GPT 4 \\/ Vision and GPT 3.5 Turbo) to generate SEO metadata and alternative texts for image files. Bulk actions supported.<\\/strong><\\/a><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/site-audit\\/\\"><strong>Site Audit: Detect technical issues that could harm your site\'s SEO directly from your WordPress administration.<\\/strong><\\/a><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/seo-alerts\\/\\">Receive SEO alerts to prevent breaking your SEO before it\'s too late<\\/a><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/import-metadata-from-a-csv-file-with-seopress-pro\\/\\"><strong>Import \\/ export metadata<\\/strong><\\/a> (titles, open graph, robots...) from \\/ to CSV file<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/sitemaps\\/\\"><strong>Video XML Sitemap<\\/strong><\\/a> to improve rankings in video search results. YouTube videos are automatically added.<\\/li>\\n    <li>Internal linking suggestions<\\/li>\\n    <li>Inspect URL with Google Search Console: get details about crawling, indexing, mobile compatibility, schemas and more.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-search-console\\/\\"><strong>Search Console integration<\\/strong><\\/a>: get insights from your post \\/ page \\/ post type list with clicks, positions, CTR and impressions.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-suggest\\/\\"><strong>Google Suggestions in Content Analysis<\\/strong><\\/a> to find the top 10 Google suggestions instantly. This is useful if you want to work with the long tail technique.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-structured-data-types\\/\\"><strong>Google Structured Data types<\\/strong> (schema.org)<\\/a>:\\n        <ol>\\n            <li>article<\\/li>\\n            <li>local business<\\/li>\\n            <li>service<\\/li>\\n            <li>how-to<\\/li>\\n            <li>FAQ<\\/li>\\n            <li>course<\\/li>\\n            <li>recipe<\\/li>\\n            <li>software application<\\/li>\\n            <li>video<\\/li>\\n            <li>event<\\/li>\\n            <li>product<\\/li>\\n            <li>job<\\/li>\\n            <li>simple review<\\/li>\\n            <li>site navigation element<\\/li>\\n            <li>custom schema<\\/li>\\n        <\\/ol>\\n    <\\/li><li><strong>Automatic Schemas<\\/strong> with advanced conditions (AND, OR, Post types, taxonomies)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/breadcrumbs\\/\\"><strong>Breadcrumbs<\\/strong><\\/a> optimized with Schema.org, A11Y ready.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-analytics\\/\\"><strong>Google Analytics Stats in Dashboard<\\/strong><\\/a> to quickly see your metrics without leaving your site<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/local-seo\\/\\"><strong>Google Local Business<\\/strong><\\/a> to boost your local store<\\/li>\\n    <li><strong>Broken link checker (SEOPress BOT)<\\/strong>: scan all your links in content to find errors (e.g. 404...)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/woocommerce-seo\\/\\"><strong>WooCommerce<\\/strong><\\/a>: Product Global Identifiers type and Product Global Identifiers fields to WooCommerce metabox for product schema (barcode), disable crawling on cart page, checkout page, customer account pages, add OG Price \\/ OG Currency for better sharing and more<\\/li>\\n    <li><strong>Easy Digital Downloads<\\/strong>: add OG Price \\/ OG Currency, remove EDD meta generator<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/breadcrumbs\\/\\"><strong>Custom Breadcrumbs<\\/strong><\\/a> for single post types \\/ term taxonomy<\\/li>\\n    <li><strong>Google Page Speed Insights<\\/strong> to analyse your site performances on Mobile \\/ Desktop + your Core Web Vitals<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-analytics\\/\\"><strong>Google Enhanced Ecommerce for WooCommerce<\\/strong><\\/a>: measure purchases, singular product view details, additions to and removals from shopping carts<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/htaccess-robots-txt\\/\\">Edit your <strong>robots.txt<\\/strong><\\/a> file from the admin (multisite \\/ multidomain ready)<\\/li>\\n    <li><strong>Google News Sitemap<\\/strong> to get your posts on Google News<\\/li>\\n    <li><strong>404 Monitoring<\\/strong>: Monitor your 404 errors to improve user experience, performances and increase the crawl budget allocated by Google<\\/li>\\n    <li><strong>Redirect 404 to homepage\\/custom url automatically<\\/strong> with custom status code (301, 302, 307, 410 or 451)<\\/li>\\n    <li>Email notifications on 404<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/301-redirects\\/\\"><strong>Redirect manager<\\/strong><\\/a>: create unlimited 301, 302, 307, 410 and 451 redirections. Regular expressions supported. Import \\/ export redirections to CSV or htaccess file.<\\/li>\\n    <li>Import redirections using CSV<\\/li>\\n    <li>Import redirections from Redirections plugin (via a JSON file)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/htaccess-robots-txt\\/\\">Edit your <strong>htaccess file<\\/strong><\\/a> from the admin<\\/li>\\n    <li>Easily customize your <strong>RSS feeds<\\/strong><\\/li>\\n    <li>...<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Buy SEOPress PRO now!<\\/strong><\\/a><\\/p>\\n<h3>WooCommerce SEO (SEOPress PRO required)<\\/h3>\\nWe support WooCommerce and Easy Digital Downloads for e-commerce sites.\\n<ul>\\n    <li>Price and currency meta tags to improve social sharing<\\/li>\\n    <li>XML sitemaps for products<\\/li>\\n    <li>Support for WooCommerce product images and WooCommerce image galleries for the XML sitemap<\\/li>\\n    <li>Centralized way to set noindex meta robots tags on pages like cart, checkout...<\\/li>\\n    <li>Remove WooCommerce generator meta tag in the source code<\\/li>\\n    <li>Create manual and\\/or automatic \\"product\\" schemas in JSON-LD to increase visibility in Google search results<\\/li>\\n    <li>WooCommerce support for our breadcrumbs<\\/li>\\n    <li>Global dynamic tags to insert in your metas titles \\/ descriptions<\\/li>\\n    <li>Product Global Identifiers type and Product Global Identifiers fields to WooCommerce metabox for product schema (barcode)<\\/li>\\n    <li>...<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Increase your sales now!<\\/strong><\\/a><\\/p>\\n<h3><a href=\\"https:\\/\\/www.seopress.org\\/features\\/page-builders-integration\\/\\">Universal SEO metabox<\\/a>: edit your metadata from any page builder \\/ editor<\\/h3>\\n<p>[youtube <a href=\\"https:\\/\\/youtu.be\\/sf0ocG7vQMM\\">https:\\/\\/youtu.be\\/sf0ocG7vQMM<\\/a>]<\\/p>\\n<h3>Elementor + SEOPress: perfect combo!<\\/h3>\\nWe provide deep integration with Elementor page builder UI, see below:\\n<p>[youtube <a href=\\"https:\\/\\/www.youtube.com\\/watch?v=oC5QZ0_TH_g\\">https:\\/\\/www.youtube.com\\/watch?v=oC5QZ0_TH_g<\\/a>]<\\/p>\\n<h3>SEOPress Insights: Off-site SEO plugin to track your rankings and backlinks in WordPress<\\/h3>\\n<ul>\\n    <li><strong>Keyword rank tracker<\\/strong>: 51 Google Search locations available<\\/li>\\n    <li>Track <strong>50 keywords<\\/strong> per site daily<\\/li>\\n    <li>Track your <strong>competitors<\\/strong>: who ranks first on your keywords<\\/li>\\n    <li>Monitor and analyze your <strong>backlinks<\\/strong> weekly<\\/li>\\n    <li><strong>Google trends<\\/strong> to find new and relevant ideas for your content marketing strategy<\\/li>\\n    <li>Your <strong>data accessible for life<\\/strong>: export it to a CSV, PDF or Excel file. Sort, order, filter your data right from your WordPress.<\\/li>\\n    <li>Receive <strong>email and Slack alerts<\\/strong> for your rankings to easily follow them<\\/li>\\n<\\/ul>\\n<p>[youtube <a href=\\"https:\\/\\/youtu.be\\/p6v9Jd5lRIU\\">https:\\/\\/youtu.be\\/p6v9Jd5lRIU<\\/a>]<\\/p>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Buy SEOPress Insights now!<\\/strong><\\/a><\\/p>\\n<h3>Developers will love SEOPress!<\\/h3>\\n<ul>\\n    <li>Hundreds of hooks are available to extend SEOPress. <a href=\\"https:\\/\\/www.seopress.org\\/support\\/hooks\\/\\">Browse them all here<\\/a>!<\\/li>\\n    <li>Plus we have a <a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/get-started-with-the-seopress-rest-api\\/\\">REST API<\\/a> to build static websites.<\\/li>\\n    <li>Finally, <a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/seopress-wp-cli\\/\\"><strong>WP CLI commands<\\/strong><\\/a> are available.<\\/li>\\n<\\/ul>","changelog":"<h4> 8.2 <a href=\\"https:\\/\\/www.seopress.org\\/newsroom\\/product-news\\/seopress-8-2\\/\\">Read the blog post update<\\/a> <\\/h4>\\n<ul>\\n<li>NEW Site Audit: Detect technical issues that could harm your site\'s SEO directly from your WordPress administration (<a href=\\"https:\\/\\/www.seopress.org\\/features\\/site-audit\\/\\">https:\\/\\/www.seopress.org\\/features\\/site-audit\\/<\\/a>) &#x1f389;<\\/li>\\n<li>NEW Add a notice to the Block Editor on slug changes to quickly create a redirection to keep your SEO (PRO)<\\/li>\\n<li>INFO Table of contents Block: allow \\"paragraph\\" \\/ \\"div\\" for the title of the block<\\/li>\\n<li>INFO Add notice to robots.txt settings tab if a physical file is already present on your server<\\/li>\\n<li>INFO Support robots.txt file for WP multisite (subdirectories installation with custom domains)<\\/li>\\n<li>INFO Strengthened security<\\/li>\\n<li>FIX Wizard redirect when updating SEOPress PRO<\\/li>\\n<li>FIX Internal links list in standard content analysis metabox<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/changelog\\/\\">View our complete changelog<\\/a>\\n<a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/how-to-downgrade-seopress-pro-to-a-previous-version\\/\\">Need to downgrade\\/rollback?<\\/a><\\/p>","installation":"<ol>\\n<li>Upload \'wp-seopress\' to the \'\\/wp-content\\/plugins\\/\' directory<\\/li>\\n<li>Activate the plugin through the \'Plugins\' menu in WordPress<\\/li>\\n<li>Click on SEOPress and apply settings.<\\/li>\\n<\\/ol>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/get-started-seopress\\/\\">Requirements \\/ Installation guide<\\/a><\\/p>"},"banners":{"high":"https:\\/\\/ps.w.org\\/wp-seopress\\/assets\\/banner-1544x500.png?rev=1","low":"https:\\/\\/ps.w.org\\/wp-seopress\\/assets\\/banner-772x250.png?rev=1"},"icons":{"1x":"https:\\/\\/www.seopress.org\\/fr\\/wp-content\\/uploads\\/sites\\/2\\/edd\\/2024\\/02\\/logo-square-seopress-pro-128x128.png","2x":"https:\\/\\/www.seopress.org\\/fr\\/wp-content\\/uploads\\/sites\\/2\\/edd\\/2024\\/02\\/logo-square-seopress-pro-256x256.png"},"stable_tag":"8.2","tested":"6.6.2","description":["<h3>Best SEO plugin for WordPress fully integrated with all page builders and themes!<\\/h3>\\n<h3>Now with AI (GPT 4) to automagically generate meta title, description and alternative texts for images!<\\/h3>\\n<p>SEOPress is a powerful WordPress SEO plugin to optimize your SEO, boost your traffic, improve social sharing, build custom HTML and XML Sitemaps, create optimized breadcrumbs, add schemas \\/ Google Structured data types, manage 301 redirections and so much more.<br \\/><\\/p>\\n<p>&#x2714; <strong><a href=\\"https:\\/\\/www.seopress.org\\/features\\/page-builders-integration\\/\\">Universal SEO metabox<\\/a>: edit all your SEO from any page builder \\/ theme builder. No more back and forth between your editor and the WordPress administration<\\/strong>\\n&#x2714; <strong>No advertising, no footprints, white label, in backend AND frontend<\\/strong>\\n&#x2714; <strong>Content analysis to help you write content optimized for search engines with unlimited target keywords<\\/strong>\\n&#x2714; <strong><a href=\\"https:\\/\\/www.seopress.org\\/solutions\\/migrate-from\\/\\">Import your post and term metadatas<\\/a> from other SEO plugins or CSV file in 1 click<\\/strong>\\n&#x2714; <strong><a href=\\"https:\\/\\/translate.wordpress.org\\/projects\\/wp-plugins\\/wp-seopress\\">Translated into 25 languages (and counting)<\\/a><\\/strong>\\n&#x2714; <strong>Trusted by over 300,000 WordPress websites since 2017<\\/strong><\\/p>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Buy SEOPress PRO: only $49 \\/ year \\/ 5 sites<\\/strong><\\/a><\\/p>\\n<p>[youtube <a href=\\"https:\\/\\/www.youtube.com\\/watch?v=4ysKFVr_nu0\\">https:\\/\\/www.youtube.com\\/watch?v=4ysKFVr_nu0<\\/a>]<\\/p>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/features\\/\\">Features<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/solutions\\/migrate-from\\/\\">Migrate<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/wordpress-seo-plugins\\/pro\\/\\">PRO<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/integrations\\/\\">Integrations<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/support\\/\\">Support<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/features\\/seopress-white-label\\/\\">White Label<\\/a> | <a href=\\"https:\\/\\/www.seopress.org\\/features\\/openai\\/\\">AI<\\/a><\\/p>\\n<h3>Why SEOPress is the best WordPress SEO plugin?<\\/h3>\\n<ul>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/seopress-productivity\\/\\"><strong>Save time<\\/strong><\\/a>: you prefer to work with Excel or Google Spreadsheets? No problem, you can import \\/ export your metadata from CSV files with SEOPress PRO in few clicks!<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Save money<\\/strong><\\/a>: SEOPress PRO is available for $49 \\/ year \\/ 5 sites. Go unlimited sites for just $149 \\/ year!<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/\\"><strong>All in one SEO plugin<\\/strong><\\/a>: comes with all the features you need to optimize the SEO of your WordPress site. No need to install additional extensions to manage redirects, schemas, XML sitemaps... You reduce the risk of conflicts and maintenance costs. You don\'t need a special feature? Deactivate it with one click without losing your configuration. Child\'s play !<\\/li>\\n    <li><strong>Easy AND ready to use<\\/strong>: you doesn\'t need to know SEO or code to use SEOPress. Most of the parameters are automatically set. And thanks to our installation wizard, configuring SEOPress has never been easier. To go further, we provide many <a href=\\"https:\\/\\/www.seopress.org\\/support\\/\\">free tutorials<\\/a> and <a href=\\"https:\\/\\/www.seopress.org\\/support\\/ebooks\\/\\">ebooks to learn SEO<\\/a> in order to better understand how to position your content on search engines.<\\/li>\\n<\\/ul>\\n<h3>SEOPress Free Features<\\/h3>\\n<ul>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/google-indexing-api-with-seopress\\/\\"><strong>Google Indexing API and IndexNow API (Bing \\/ Yandex)<\\/strong><\\/a> to quickly index its content in these search engines<\\/li>\\n    <li><strong>Installation wizard<\\/strong> to quickly setup your site<\\/li>\\n    <li><strong>Content analysis<\\/strong> with unlimited keywords to help you write optimized content for search engines<\\/li>\\n    <li><strong>Mobile \\/ Desktop Google Preview<\\/strong> to see how your post will looks like in Google search results<\\/li>\\n    <li><strong>Facebook &amp; X (ex-Twitter) Social Preview<\\/strong> to see how your post will looks like on social media to increase conversions<\\/li>\\n    <li><strong>Titles<\\/strong> (with <a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/manage-titles-meta-descriptions\\/\\">dynamic variables<\\/a>: custom fields, terms taxonomie...)<\\/li>\\n    <li><strong>Meta descriptions<\\/strong> (with dynamic variables too)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/social-media\\/\\"><strong>Open Graph and X (ex-Twitter) Cards<\\/strong><\\/a> to improve social media sharing (Facebook, LinkedIn, Instagram, X (ex-Twitter), Pinterest, WhatsApp...)<\\/li>\\n    <li><strong>Google Knowledge Graph<\\/strong><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-analytics\\/\\"><strong>Google Analytics<\\/strong> and <strong>Matomo<\\/strong><\\/a> with downloads tracking, custom dimensions, ip anonymization, remarketing, demographics and interest reporting, cross-domain tracking...(<a href=\\"https:\\/\\/www.seopress.org\\/features\\/seopress-white-label\\/\\">GDPR compatibility<\\/a>)<\\/li>\\n    <li><strong>Microsoft Clarity integration<\\/strong>: to capture session recordings, get instant heatmaps and powerful Insights for Free. Know how people interact with your site to improve user experience and conversions<\\/li>\\n    <li><strong>Custom Canonical URL<\\/strong><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/manage-meta-robots\\/\\"><strong>Meta robots<\\/strong><\\/a> (noindex, nofollow, noimageindex, noarchive, nosnippet)<\\/li>\\n    <li>Build your <a href=\\"https:\\/\\/www.seopress.org\\/features\\/sitemaps\\/\\"><strong>custom XML Sitemap<\\/strong><\\/a> to improve search indexing<\\/li>\\n    <li><strong>Image XML Sitemaps<\\/strong> to improve search indexing for Google Images<\\/li>\\n    <li>Build your custom <strong>HTML Sitemap<\\/strong> to enhanced navigation for visitors and improve search indexing<\\/li>\\n    <li>Link your social media accounts to your site<\\/li>\\n    <li><strong>Redirections<\\/strong> in post, pages, custom post types<\\/li>\\n    <li>Remove \\/category\\/ in URLs<\\/li>\\n    <li>Remove \\/product-category\\/ in URLs<\\/li>\\n    <li>Remove ?replytocom to avoid duplicate content<\\/li>\\n    <li>Redirect attachment pages to post parent<\\/li>\\n    <li>Redirect attachment pages to their file URL<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/image-seo\\/\\"><strong>Image SEO<\\/strong><\\/a>: Automatically set the image title \\/ alt \\/ caption \\/ description<\\/li>\\n    <li>Import \\/ Export settings from site to site.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/solutions\\/migrate-from\\/\\">Import posts and terms metadata<\\/a> from Yoast SEO, All In One SEO, SEO Framework, Rank Math, SEO Ultimate, WP Meta SEO, Premium SEO Pack, Squirrly and many other SEO plugins<\\/li>\\n    <li>...<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/wordpress-seo-plugins\\/free\\/features\\/\\">Check out all SEOPress Free features here<\\/a><\\/p>\\n<h3>SEOPress PRO: to go further with your SEO<\\/h3>\\n<p>[youtube <a href=\\"https:\\/\\/youtu.be\\/zxGCY-bJYwE\\">https:\\/\\/youtu.be\\/zxGCY-bJYwE<\\/a>]<\\/p>\\n<ul>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/openai\\/\\"><strong>Use Artificial Intelligence (GPT 4 \\/ Vision and GPT 3.5 Turbo) to generate SEO metadata and alternative texts for image files. Bulk actions supported.<\\/strong><\\/a><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/site-audit\\/\\"><strong>Site Audit: Detect technical issues that could harm your site\'s SEO directly from your WordPress administration.<\\/strong><\\/a><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/seo-alerts\\/\\">Receive SEO alerts to prevent breaking your SEO before it\'s too late<\\/a><\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/import-metadata-from-a-csv-file-with-seopress-pro\\/\\"><strong>Import \\/ export metadata<\\/strong><\\/a> (titles, open graph, robots...) from \\/ to CSV file<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/sitemaps\\/\\"><strong>Video XML Sitemap<\\/strong><\\/a> to improve rankings in video search results. YouTube videos are automatically added.<\\/li>\\n    <li>Internal linking suggestions<\\/li>\\n    <li>Inspect URL with Google Search Console: get details about crawling, indexing, mobile compatibility, schemas and more.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-search-console\\/\\"><strong>Search Console integration<\\/strong><\\/a>: get insights from your post \\/ page \\/ post type list with clicks, positions, CTR and impressions.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-suggest\\/\\"><strong>Google Suggestions in Content Analysis<\\/strong><\\/a> to find the top 10 Google suggestions instantly. This is useful if you want to work with the long tail technique.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-structured-data-types\\/\\"><strong>Google Structured Data types<\\/strong> (schema.org)<\\/a>:\\n        <ol>\\n            <li>article<\\/li>\\n            <li>local business<\\/li>\\n            <li>service<\\/li>\\n            <li>how-to<\\/li>\\n            <li>FAQ<\\/li>\\n            <li>course<\\/li>\\n            <li>recipe<\\/li>\\n            <li>software application<\\/li>\\n            <li>video<\\/li>\\n            <li>event<\\/li>\\n            <li>product<\\/li>\\n            <li>job<\\/li>\\n            <li>simple review<\\/li>\\n            <li>site navigation element<\\/li>\\n            <li>custom schema<\\/li>\\n        <\\/ol>\\n    <\\/li><li><strong>Automatic Schemas<\\/strong> with advanced conditions (AND, OR, Post types, taxonomies)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/breadcrumbs\\/\\"><strong>Breadcrumbs<\\/strong><\\/a> optimized with Schema.org, A11Y ready.<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-analytics\\/\\"><strong>Google Analytics Stats in Dashboard<\\/strong><\\/a> to quickly see your metrics without leaving your site<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/local-seo\\/\\"><strong>Google Local Business<\\/strong><\\/a> to boost your local store<\\/li>\\n    <li><strong>Broken link checker (SEOPress BOT)<\\/strong>: scan all your links in content to find errors (e.g. 404...)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/woocommerce-seo\\/\\"><strong>WooCommerce<\\/strong><\\/a>: Product Global Identifiers type and Product Global Identifiers fields to WooCommerce metabox for product schema (barcode), disable crawling on cart page, checkout page, customer account pages, add OG Price \\/ OG Currency for better sharing and more<\\/li>\\n    <li><strong>Easy Digital Downloads<\\/strong>: add OG Price \\/ OG Currency, remove EDD meta generator<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/breadcrumbs\\/\\"><strong>Custom Breadcrumbs<\\/strong><\\/a> for single post types \\/ term taxonomy<\\/li>\\n    <li><strong>Google Page Speed Insights<\\/strong> to analyse your site performances on Mobile \\/ Desktop + your Core Web Vitals<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/google-analytics\\/\\"><strong>Google Enhanced Ecommerce for WooCommerce<\\/strong><\\/a>: measure purchases, singular product view details, additions to and removals from shopping carts<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/htaccess-robots-txt\\/\\">Edit your <strong>robots.txt<\\/strong><\\/a> file from the admin (multisite \\/ multidomain ready)<\\/li>\\n    <li><strong>Google News Sitemap<\\/strong> to get your posts on Google News<\\/li>\\n    <li><strong>404 Monitoring<\\/strong>: Monitor your 404 errors to improve user experience, performances and increase the crawl budget allocated by Google<\\/li>\\n    <li><strong>Redirect 404 to homepage\\/custom url automatically<\\/strong> with custom status code (301, 302, 307, 410 or 451)<\\/li>\\n    <li>Email notifications on 404<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/301-redirects\\/\\"><strong>Redirect manager<\\/strong><\\/a>: create unlimited 301, 302, 307, 410 and 451 redirections. Regular expressions supported. Import \\/ export redirections to CSV or htaccess file.<\\/li>\\n    <li>Import redirections using CSV<\\/li>\\n    <li>Import redirections from Redirections plugin (via a JSON file)<\\/li>\\n    <li><a href=\\"https:\\/\\/www.seopress.org\\/features\\/htaccess-robots-txt\\/\\">Edit your <strong>htaccess file<\\/strong><\\/a> from the admin<\\/li>\\n    <li>Easily customize your <strong>RSS feeds<\\/strong><\\/li>\\n    <li>...<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Buy SEOPress PRO now!<\\/strong><\\/a><\\/p>\\n<h3>WooCommerce SEO (SEOPress PRO required)<\\/h3>\\nWe support WooCommerce and Easy Digital Downloads for e-commerce sites.\\n<ul>\\n    <li>Price and currency meta tags to improve social sharing<\\/li>\\n    <li>XML sitemaps for products<\\/li>\\n    <li>Support for WooCommerce product images and WooCommerce image galleries for the XML sitemap<\\/li>\\n    <li>Centralized way to set noindex meta robots tags on pages like cart, checkout...<\\/li>\\n    <li>Remove WooCommerce generator meta tag in the source code<\\/li>\\n    <li>Create manual and\\/or automatic \\"product\\" schemas in JSON-LD to increase visibility in Google search results<\\/li>\\n    <li>WooCommerce support for our breadcrumbs<\\/li>\\n    <li>Global dynamic tags to insert in your metas titles \\/ descriptions<\\/li>\\n    <li>Product Global Identifiers type and Product Global Identifiers fields to WooCommerce metabox for product schema (barcode)<\\/li>\\n    <li>...<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Increase your sales now!<\\/strong><\\/a><\\/p>\\n<h3><a href=\\"https:\\/\\/www.seopress.org\\/features\\/page-builders-integration\\/\\">Universal SEO metabox<\\/a>: edit your metadata from any page builder \\/ editor<\\/h3>\\n<p>[youtube <a href=\\"https:\\/\\/youtu.be\\/sf0ocG7vQMM\\">https:\\/\\/youtu.be\\/sf0ocG7vQMM<\\/a>]<\\/p>\\n<h3>Elementor + SEOPress: perfect combo!<\\/h3>\\nWe provide deep integration with Elementor page builder UI, see below:\\n<p>[youtube <a href=\\"https:\\/\\/www.youtube.com\\/watch?v=oC5QZ0_TH_g\\">https:\\/\\/www.youtube.com\\/watch?v=oC5QZ0_TH_g<\\/a>]<\\/p>\\n<h3>SEOPress Insights: Off-site SEO plugin to track your rankings and backlinks in WordPress<\\/h3>\\n<ul>\\n    <li><strong>Keyword rank tracker<\\/strong>: 51 Google Search locations available<\\/li>\\n    <li>Track <strong>50 keywords<\\/strong> per site daily<\\/li>\\n    <li>Track your <strong>competitors<\\/strong>: who ranks first on your keywords<\\/li>\\n    <li>Monitor and analyze your <strong>backlinks<\\/strong> weekly<\\/li>\\n    <li><strong>Google trends<\\/strong> to find new and relevant ideas for your content marketing strategy<\\/li>\\n    <li>Your <strong>data accessible for life<\\/strong>: export it to a CSV, PDF or Excel file. Sort, order, filter your data right from your WordPress.<\\/li>\\n    <li>Receive <strong>email and Slack alerts<\\/strong> for your rankings to easily follow them<\\/li>\\n<\\/ul>\\n<p>[youtube <a href=\\"https:\\/\\/youtu.be\\/p6v9Jd5lRIU\\">https:\\/\\/youtu.be\\/p6v9Jd5lRIU<\\/a>]<\\/p>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/pricing\\/\\"><strong>Buy SEOPress Insights now!<\\/strong><\\/a><\\/p>\\n<h3>Developers will love SEOPress!<\\/h3>\\n<ul>\\n    <li>Hundreds of hooks are available to extend SEOPress. <a href=\\"https:\\/\\/www.seopress.org\\/support\\/hooks\\/\\">Browse them all here<\\/a>!<\\/li>\\n    <li>Plus we have a <a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/get-started-with-the-seopress-rest-api\\/\\">REST API<\\/a> to build static websites.<\\/li>\\n    <li>Finally, <a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/seopress-wp-cli\\/\\"><strong>WP CLI commands<\\/strong><\\/a> are available.<\\/li>\\n<\\/ul>"],"changelog":["<h4> 8.2 <a href=\\"https:\\/\\/www.seopress.org\\/newsroom\\/product-news\\/seopress-8-2\\/\\">Read the blog post update<\\/a> <\\/h4>\\n<ul>\\n<li>NEW Site Audit: Detect technical issues that could harm your site\'s SEO directly from your WordPress administration (<a href=\\"https:\\/\\/www.seopress.org\\/features\\/site-audit\\/\\">https:\\/\\/www.seopress.org\\/features\\/site-audit\\/<\\/a>) &#x1f389;<\\/li>\\n<li>NEW Add a notice to the Block Editor on slug changes to quickly create a redirection to keep your SEO (PRO)<\\/li>\\n<li>INFO Table of contents Block: allow \\"paragraph\\" \\/ \\"div\\" for the title of the block<\\/li>\\n<li>INFO Add notice to robots.txt settings tab if a physical file is already present on your server<\\/li>\\n<li>INFO Support robots.txt file for WP multisite (subdirectories installation with custom domains)<\\/li>\\n<li>INFO Strengthened security<\\/li>\\n<li>FIX Wizard redirect when updating SEOPress PRO<\\/li>\\n<li>FIX Internal links list in standard content analysis metabox<\\/li>\\n<\\/ul>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/changelog\\/\\">View our complete changelog<\\/a>\\n<a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/how-to-downgrade-seopress-pro-to-a-previous-version\\/\\">Need to downgrade\\/rollback?<\\/a><\\/p>"],"installation":["<ol>\\n<li>Upload \'wp-seopress\' to the \'\\/wp-content\\/plugins\\/\' directory<\\/li>\\n<li>Activate the plugin through the \'Plugins\' menu in WordPress<\\/li>\\n<li>Click on SEOPress and apply settings.<\\/li>\\n<\\/ol>\\n<p><a href=\\"https:\\/\\/www.seopress.org\\/support\\/guides\\/get-started-seopress\\/\\">Requirements \\/ Installation guide<\\/a><\\/p>"],"plugin":"wp-seopress-pro\\/seopress-pro.php","id":"wp-seopress-pro\\/seopress-pro.php","requires":"","requires_php":""}";}', 'off'),
(3109, 'active_plugins', 'a:12:{i:0;s:33:"acf-extended-pro/acf-extended.php";i:1;s:53:"admin-bar-user-switching/admin-bar-user-switching.php";i:2;s:34:"advanced-custom-fields-pro/acf.php";i:3;s:45:"enable-media-replace/enable-media-replace.php";i:4;s:53:"manage-privacy-options/baw-manage-privacy-options.php";i:5;s:21:"safe-svg/safe-svg.php";i:6;s:21:"spatie-ray/wp-ray.php";i:7;s:33:"user-switching/user-switching.php";i:8;s:39:"wp-migrate-db-pro/wp-migrate-db-pro.php";i:9;s:31:"wp-nested-pages/nestedpages.php";i:10;s:32:"wp-seopress-pro/seopress-pro.php";i:11;s:24:"wp-seopress/seopress.php";}', 'yes'),
(3110, 'blog_public', '1', 'yes'),
(3111, 'upload_path', '', 'yes'),
(3112, 'upload_url_path', '', 'yes'),
(3113, 'wpmdb_error_log', '********************************************\n******  Log date: 2024/10/07 05:59:21 ******\n********************************************\n\nWP Migrate Version: 2.7.0\n\nError: WP_Error Object\n(\n    [errors] => Array\n        (\n            [http_request_failed] => Array\n                (\n                    [0] => cURL error 28: Resolving timed out after 10002 milliseconds\n                )\n\n        )\n\n    [error_data] => Array\n        (\n        )\n\n    [additional_data:protected] => Array\n        (\n        )\n\n)\n\n\n********************************************\n******  Log date: 2024/10/07 05:59:23 ******\n********************************************\n\nWP Migrate Version: 2.7.0\n\nError: WP_Error Object\n(\n    [errors] => Array\n        (\n            [http_request_failed] => Array\n                (\n                    [0] => cURL error 28: Resolving timed out after 10001 milliseconds\n                )\n\n        )\n\n    [error_data] => Array\n        (\n        )\n\n    [additional_data:protected] => Array\n        (\n        )\n\n)\n\n\n********************************************\n******  Log date: 2024/10/07 05:59:32 ******\n********************************************\n\nWP Migrate Version: 2.7.0\n\nError: Error trying to get upgrade data.\n\nArray\n(\n    [connection_failed] => <div class="updated warning inline-message"><strong>Could not connect to api.deliciousbrains.com</strong> &mdash; You will not receive update notifications or be able to activate your license until this is fixed. This issue is often caused by an improperly configured SSL server (https). We recommend <a href="https://deliciousbrains.com/wp-migrate-db-pro/doc/could-not-connect-deliciousbrains-com/?utm_campaign=error%2Bmessages&utm_source=MDB%2BPaid&utm_medium=insideplugin" target="_blank">fixing the SSL configuration on your server</a>, but if you need a quick fix you can:<p><a href="https://numeridanse.local/wp/wp-admin/tools.php?page=wp-migrate-db-pro&nonce=f4fa3c5da3&wpmdb-disable-ssl=1" class="temporarily-disable-ssl button">Temporarily disable SSL for connections to api.deliciousbrains.com</a></p></div>\n)\n\n\n', 'off'),
(3116, 'wpmdb_recent_migrations', 'a:0:{}', 'off'),
(3117, 'wpmdb_remote_migration_state', '', 'off') ;
INSERT INTO `nd_options` ( `option_id`, `option_name`, `option_value`, `autoload`) VALUES
(3118, 'wpmdb_saved_profiles', 'a:1:{i:1;a:4:{s:4:"name";s:7:"pulldev";s:5:"value";s:4165:"{"current_migration":{"connected":true,"intent":"pull","tables_option":"all","tables_selected":["nd_commentmeta","nd_comments","nd_links","nd_options","nd_postmeta","nd_posts","nd_pp_activity_logs","nd_seopress_content_analysis","nd_seopress_seo_issues","nd_seopress_significant_keywords","nd_term_relationships","nd_term_taxonomy","nd_termmeta","nd_terms","nd_usermeta","nd_users"],"backup_option":"backup_only_with_prefix","backup_tables_selected":["nd_commentmeta","nd_comments","nd_links","nd_options","nd_postmeta","nd_posts","nd_seopress_content_analysis","nd_seopress_significant_keywords","nd_term_relationships","nd_term_taxonomy","nd_termmeta","nd_terms","nd_usermeta","nd_users"],"post_types_option":"all","post_types_selected":[],"advanced_options_selected":["replace_guids","exclude_transients","exclude_spam","keep_active_plugins","exclude_post_revisions"],"profile_name":"pulldev","selected_existing_profile":null,"profile_type":null,"status":"","stages":[],"current_stage":"","stages_complete":[],"running":false,"migration_enabled":false,"migration_id":"81870d0a-e7c4-484d-a665-16956c5c2088","source_prefix":"nd_","destination_prefix":"nd_","preview":false,"selectedComboOption":"preview","twoMultisites":false,"localSource":false,"databaseEnabled":true,"currentPayloadSize":0,"currentMaxPayloadSize":null,"fileTransferRequests":0,"payloadSizeHistory":[],"fileTransferStats":[],"forceHighPerformanceTransfers":true,"fseDumpFilename":null,"highPerformanceTransfersStatus":false},"connection_info":{"connection_state":{"value":"https:\\/\\/dev.numeridanse.com\\/wp\\nYjN\\/0F24tAdyMY+zROM0tDFAzEjkkyOizrD+uiCo","url":"https:\\/\\/dev.numeridanse.com\\/wp","key":"YjN\\/0F24tAdyMY+zROM0tDFAzEjkkyOizrD+uiCo"},"status":{"auth_form":{"username":"","password":""},"show_auth_form":false,"connecting":false,"error":false,"error_msg":"","button_status":"disabled","ssl_notice":false,"pasted":false,"copy_to_remote":false,"prefix_mismatch":false,"mixed_case_table_name_warning":false,"show_mst_warning":false,"update_plugin_on_remote":false,"retry_over_http":false}},"search_replace":{"standard_search_replace":{"domain":{"search":"\\/\\/dev.numeridanse.com","replace":"\\/\\/numeridanse.local","enabled":true},"path":{"search":"\\/home\\/<USER>\\/dev\\/web","replace":"\\/Users\\/<USER>\\/www\\/numeridanse.tv\\/app\\/public\\/web","enabled":true}},"standard_options_enabled":["domain","path"],"standard_search_visible":true,"custom_search_replace":[{"replace_old":"","replace_new":"","focus":false,"regex":false,"isValidRegex":null,"replace_old_placeholder":null,"replace_new_placeholder":null,"id":"3776263b-a59f-4875-adb6-0518ead5136a"}],"custom_search_domain_locked":false},"media_files":{"enabled":true,"option":"new_subsequent","available":true,"is_licensed":true,"message":"<b>Addon Missing<\\/b> - The Media Files addon is inactive on the <strong>remote site<\\/strong>. Please install and activate it to enable media file migration.","excludes":".DS_Store\\n*.log\\n*backup*\\/\\n*cache*\\/","last_migration":"2024-10-07T19:26:25+00:00","date":"2024-10-03T15:12:42.245Z"},"theme_plugin_files":{"available":true,"is_licensed":true,"message":"<b>Addon Missing<\\/b> - The Theme & Plugin Files addon is inactive on the <strong>remote site<\\/strong>. Please install and activate it to enable Theme & Plugin Files migration.","theme_files":{"enabled":false},"themes_option":"all","themes_selected":[],"themes_excluded":[],"themes_excludes":".DS_Store\\n.git\\nnode_modules","plugin_files":{"enabled":false},"plugins_option":"all","plugins_selected":[],"plugins_excluded":[],"plugins_excludes":".DS_Store\\n.git\\nnode_modules","muplugin_files":{"enabled":false},"muplugins_option":"selected","muplugins_selected":[],"muplugins_excludes":".DS_Store\\n.git\\nnode_modules","other_files":{"enabled":false},"others_option":"selected","others_selected":[],"others_excludes":".DS_Store\\n.git\\nnode_modules","core_files":{"enabled":false},"core_option":"all","core_selected":[],"core_excludes":".DS_Store\\n.git\\nnode_modules","state":{"status":""}},"multisite_tools":{"enabled":false,"available":true,"is_licensed":true,"selected_subsite":0,"destination_subsite":0,"new_prefix":"","message":""}}";s:4:"guid";s:36:"30dfafa3-f9ad-47a8-905f-ff248b1868e4";s:4:"date";i:**********;}}', 'off'),
(3119, 'wpmdb_schema_version', '3.8.0', 'off'),
(3120, 'wpmdb_settings', 'a:15:{s:3:"key";s:40:"qZA7DyaHWtGdOHH4DSmYbMNSqCIi/trrX32S/Je8";s:10:"allow_pull";b:0;s:10:"allow_push";b:0;s:8:"profiles";a:0:{}s:7:"licence";s:0:"";s:10:"verify_ssl";b:0;s:17:"whitelist_plugins";a:0:{}s:11:"max_request";i:1048576;s:22:"delay_between_requests";i:0;s:18:"prog_tables_hidden";b:1;s:21:"pause_before_finalize";b:0;s:14:"allow_tracking";N;s:26:"high_performance_transfers";b:0;s:18:"compatibility_mode";b:1;s:28:"compatibility_plugin_version";s:3:"1.3";}', 'off'),
(3121, '_site_transient_wpmdb_disabled_legacy_addons', '1', 'off'),
(3124, '_site_transient_wpmdb_available_addons', 'a:4:{s:29:"wp-migrate-db-pro-media-files";i:2351;s:21:"wp-migrate-db-pro-cli";i:3948;s:33:"wp-migrate-db-pro-multisite-tools";i:7999;s:36:"wp-migrate-db-pro-theme-plugin-files";i:36287;}', 'off'),
(3128, '_transient_jetpack_autoloader_plugin_paths', 'a:1:{i:0;s:28:"{{WP_PLUGIN_DIR}}/spatie-ray";}', 'on'),
(3129, '_site_transient_timeout_wpmdb_licence_response_2', '1728372441', 'off'),
(3130, '_site_transient_wpmdb_licence_response_2', '{"features":[],"addons_available":"1","addons_available_list":{"wp-migrate-db-pro-media-files":2351,"wp-migrate-db-pro-cli":3948,"wp-migrate-db-pro-multisite-tools":7999,"wp-migrate-db-pro-theme-plugin-files":36287},"addon_list":{"wp-migrate-db-pro-media-files":{"type":"feature","name":"Media Files","desc":"Allows you to push and pull your files in the Media Library between two WordPress installs. It can compare both libraries and only migrate those missing or updated, or it can do a complete copy of one site\\u2019s library to another. <a href=\\"https:\\/\\/deliciousbrains.com\\/wp-migrate-db-pro\\/doc\\/media-files-addon\\/?utm_campaign=addons%252Binstall&utm_source=MDB%252BPaid&utm_medium=insideplugin\\">More Details &rarr;<\\/a>","version":"2.1.0","beta_version":false,"tested":"6.6.1"},"wp-migrate-db-pro-cli":{"type":"feature","name":"CLI","desc":"Integrates WP Migrate with WP-CLI allowing you to run migrations from the command line: <code>wp migratedb &lt;push|pull&gt; &lt;url&gt; &lt;secret-key&gt;<\\/code> <code>[--find=&lt;strings&gt;] [--replace=&lt;strings&gt;] ...<\\/code> <a href=\\"https:\\/\\/deliciousbrains.com\\/wp-migrate-db-pro\\/doc\\/cli-addon\\/?utm_campaign=addons%252Binstall&utm_source=MDB%252BPaid&utm_medium=insideplugin\\">More Details &rarr;<\\/a>","required":"1.4b1","version":"1.6.0","beta_version":false,"tested":"6.6.1"},"wp-migrate-db-pro-multisite-tools":{"type":"feature","name":"Multisite Tools","desc":"Export a subsite as an SQL file that can then be imported as a single site install. <a href=\\"https:\\/\\/deliciousbrains.com\\/wp-migrate-db-pro\\/doc\\/multisite-tools-addon\\/?utm_campaign=addons%252Binstall&utm_source=MDB%252BPaid&utm_medium=insideplugin\\">More Details &rarr;<\\/a>","required":"1.5-dev","version":"1.4.1","beta_version":false,"tested":"6.6.1"},"wp-migrate-db-pro-theme-plugin-files":{"type":"feature","name":"Theme & Plugin Files","desc":"Allows you to push and pull your theme and plugin files between two WordPress installs. <a href=\\"https:\\/\\/deliciousbrains.com\\/wp-migrate-db-pro\\/doc\\/theme-plugin-files-addon\\/?utm_campaign=addons%252Binstall&utm_source=MDB%252BPaid&utm_medium=insideplugin\\">More Details &rarr;<\\/a>","required":"1.8.2b1","version":"1.2.0","beta_version":false,"tested":"6.6.1"}},"form_url":"https:\\/\\/api.deliciousbrains.com\\/?wc-api=delicious-brains&request=submit_support_request&licence_key=31f7c2a2-20a6-4c34-bf4b-7c7ae7c057e0&product=wp-migrate-db-pro","license_name":"Developer&nbsp;(Legacy)","display_name":"jb4","user_email":"<EMAIL>","upgrade_url":"https:\\/\\/deliciousbrains.com\\/my-account\\/license-upgrade\\/15521","support_contacts":["<EMAIL>"],"support_email":"<EMAIL>"}', 'off'),
(3131, '_site_transient_wpmdb_available_addons_per_user_2', 'a:4:{s:29:"wp-migrate-db-pro-media-files";i:2351;s:21:"wp-migrate-db-pro-cli";i:3948;s:33:"wp-migrate-db-pro-multisite-tools";i:7999;s:36:"wp-migrate-db-pro-theme-plugin-files";i:36287;}', 'off'),
(3134, '_site_transient_t15s-registry-wp-seopress-pro', 'O:8:"stdClass":2:{s:15:"wp-seopress-pro";a:1:{s:12:"translations";a:18:{i:0;a:7:{s:8:"language";s:5:"zh_CN";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-10-01T16:31:05+00:00";s:12:"english_name";s:15:"Chinese (China)";s:11:"native_name";s:12:"简体中文";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-zh_CN.zip";s:3:"iso";a:2:{i:0;s:2:"zh";i:1;s:3:"zho";}}i:1;a:7:{s:8:"language";s:5:"nl_NL";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-10-03T09:16:11+00:00";s:12:"english_name";s:5:"Dutch";s:11:"native_name";s:10:"Nederlands";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-nl_NL.zip";s:3:"iso";a:2:{i:0;s:2:"nl";i:1;s:3:"nld";}}i:2;a:7:{s:8:"language";s:5:"en_GB";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-09-30T15:33:30+00:00";s:12:"english_name";s:12:"English (UK)";s:11:"native_name";s:12:"English (UK)";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-en_GB.zip";s:3:"iso";a:3:{i:0;s:2:"en";i:1;s:3:"eng";i:2;s:3:"eng";}}i:3;a:7:{s:8:"language";s:5:"fr_FR";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-10-02T10:53:06+00:00";s:12:"english_name";s:15:"French (France)";s:11:"native_name";s:9:"Français";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-fr_FR.zip";s:3:"iso";a:1:{i:0;s:2:"fr";}}i:4;a:7:{s:8:"language";s:5:"fr_BE";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2023-09-28T06:50:55+00:00";s:12:"english_name";s:16:"French (Belgium)";s:11:"native_name";s:21:"Français de Belgique";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-fr_BE.zip";s:3:"iso";a:2:{i:0;s:2:"fr";i:1;s:3:"fra";}}i:5;a:7:{s:8:"language";s:5:"fr_CA";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2023-09-28T06:51:02+00:00";s:12:"english_name";s:15:"French (Canada)";s:11:"native_name";s:19:"Français du Canada";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-fr_CA.zip";s:3:"iso";a:2:{i:0;s:2:"fr";i:1;s:3:"fra";}}i:6;a:7:{s:8:"language";s:5:"de_DE";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-10-02T17:16:19+00:00";s:12:"english_name";s:6:"German";s:11:"native_name";s:7:"Deutsch";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-de_DE.zip";s:3:"iso";a:1:{i:0;s:2:"de";}}i:7;a:7:{s:8:"language";s:5:"hi_IN";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-10-01T16:29:32+00:00";s:12:"english_name";s:5:"Hindi";s:11:"native_name";s:18:"हिन्दी";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-hi_IN.zip";s:3:"iso";a:2:{i:0;s:2:"hi";i:1;s:3:"hin";}}i:8;a:7:{s:8:"language";s:5:"it_IT";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-10-01T16:29:43+00:00";s:12:"english_name";s:7:"Italian";s:11:"native_name";s:8:"Italiano";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-it_IT.zip";s:3:"iso";a:2:{i:0;s:2:"it";i:1;s:3:"ita";}}i:9;a:7:{s:8:"language";s:2:"ja";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-10-01T16:29:52+00:00";s:12:"english_name";s:8:"Japanese";s:11:"native_name";s:9:"日本語";s:7:"package";s:86:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-ja.zip";s:3:"iso";a:1:{i:0;s:2:"ja";}}i:10;a:7:{s:8:"language";s:5:"ko_KR";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-02-23T10:24:08+00:00";s:12:"english_name";s:6:"Korean";s:11:"native_name";s:9:"한국어";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-ko_KR.zip";s:3:"iso";a:2:{i:0;s:2:"ko";i:1;s:3:"kor";}}i:11;a:7:{s:8:"language";s:5:"pl_PL";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-10-02T10:31:44+00:00";s:12:"english_name";s:6:"Polish";s:11:"native_name";s:6:"Polski";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-pl_PL.zip";s:3:"iso";a:2:{i:0;s:2:"pl";i:1;s:3:"pol";}}i:12;a:7:{s:8:"language";s:5:"pt_BR";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-10-07T00:01:18+00:00";s:12:"english_name";s:19:"Portuguese (Brazil)";s:11:"native_name";s:20:"Português do Brasil";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-pt_BR.zip";s:3:"iso";a:2:{i:0;s:2:"pt";i:1;s:3:"por";}}i:13;a:7:{s:8:"language";s:5:"ru_RU";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-10-01T16:30:47+00:00";s:12:"english_name";s:7:"Russian";s:11:"native_name";s:14:"Русский";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-ru_RU.zip";s:3:"iso";a:2:{i:0;s:2:"ru";i:1;s:3:"rus";}}i:14;a:7:{s:8:"language";s:5:"es_ES";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-09-30T18:13:24+00:00";s:12:"english_name";s:15:"Spanish (Spain)";s:11:"native_name";s:8:"Español";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-es_ES.zip";s:3:"iso";a:3:{i:0;s:2:"es";i:1;s:3:"spa";i:2;s:3:"spa";}}i:15;a:7:{s:8:"language";s:5:"sv_SE";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-10-01T16:30:53+00:00";s:12:"english_name";s:7:"Swedish";s:11:"native_name";s:7:"Svenska";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-sv_SE.zip";s:3:"iso";a:2:{i:0;s:2:"sv";i:1;s:3:"swe";}}i:16;a:7:{s:8:"language";s:5:"tr_TR";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-10-01T16:31:00+00:00";s:12:"english_name";s:7:"Turkish";s:11:"native_name";s:8:"Türkçe";s:7:"package";s:89:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-tr_TR.zip";s:3:"iso";a:2:{i:0;s:2:"tr";i:1;s:3:"tur";}}i:17;a:7:{s:8:"language";s:2:"vi";s:7:"version";s:5:"6.3.2";s:7:"updated";s:25:"2024-09-30T15:33:35+00:00";s:12:"english_name";s:10:"Vietnamese";s:11:"native_name";s:14:"Tiếng Việt";s:7:"package";s:86:"https://packages.translationspress.com/seopress/wp-seopress-pro/wp-seopress-pro-vi.zip";s:3:"iso";a:2:{i:0;s:2:"vi";i:1;s:3:"vie";}}}}s:13:"_last_checked";i:1728329242;}', 'off'),
(3135, '_site_transient_update_core', 'O:8:"stdClass":4:{s:7:"updates";a:1:{i:0;O:8:"stdClass":10:{s:8:"response";s:6:"latest";s:8:"download";s:65:"https://downloads.wordpress.org/release/fr_FR/wordpress-6.6.2.zip";s:6:"locale";s:5:"fr_FR";s:8:"packages";O:8:"stdClass":5:{s:4:"full";s:65:"https://downloads.wordpress.org/release/fr_FR/wordpress-6.6.2.zip";s:10:"no_content";s:0:"";s:11:"new_bundled";s:0:"";s:7:"partial";s:0:"";s:8:"rollback";s:0:"";}s:7:"current";s:5:"6.6.2";s:7:"version";s:5:"6.6.2";s:11:"php_version";s:6:"7.2.24";s:13:"mysql_version";s:5:"5.5.5";s:11:"new_bundled";s:3:"6.4";s:15:"partial_version";s:0:"";}}s:12:"last_checked";i:**********;s:15:"version_checked";s:5:"6.6.2";s:12:"translations";a:0:{}}', 'off'),
(3141, '_site_transient_update_themes', 'O:8:"stdClass":5:{s:12:"last_checked";i:**********;s:7:"checked";a:1:{s:10:"lesanimals";s:5:"4.0.0";}s:8:"response";a:0:{}s:9:"no_update";a:0:{}s:12:"translations";a:0:{}}', 'off'),
(3142, 'acf_pro_license_status', 'a:11:{s:6:"status";s:6:"active";s:7:"created";i:0;s:6:"expiry";i:0;s:4:"name";s:9:"Developer";s:8:"lifetime";b:1;s:8:"refunded";b:0;s:17:"view_licenses_url";s:62:"https://www.advancedcustomfields.com/my-account/view-licenses/";s:23:"manage_subscription_url";s:0:"";s:9:"error_msg";s:0:"";s:10:"next_check";i:**********;s:16:"legacy_multisite";b:1;}', 'on'),
(3143, '_transient_timeout_acf_plugin_updates', '**********', 'off'),
(3144, '_transient_acf_plugin_updates', 'a:5:{s:7:"plugins";a:1:{s:34:"advanced-custom-fields-pro/acf.php";a:12:{s:4:"slug";s:26:"advanced-custom-fields-pro";s:6:"plugin";s:34:"advanced-custom-fields-pro/acf.php";s:11:"new_version";s:5:"6.3.8";s:3:"url";s:36:"https://www.advancedcustomfields.com";s:6:"tested";s:5:"6.6.1";s:7:"package";s:361:"https://connect.advancedcustomfields.com/v2/plugins/download?version=6.3.8&token=eyJwIjoicHJvIiwiayI6ImIzSmtaWEpmYVdROU1UTTBNalV6ZkhSNWNHVTlaR1YyWld4dmNHVnlmR1JoZEdVOU1qQXhPQzB3Tnkwd01pQXdPVG94TnpvMU5BPT0iLCJ3cF91cmwiOiJodHRwczpcL1wvbnVtZXJpZGFuc2UubG9jYWwiLCJ3cF92ZXJzaW9uIjoiNi42LjIiLCJ3cF9tdWx0aXNpdGUiOjAsInBocF92ZXJzaW9uIjoiOC4xLjI5IiwiYmxvY2tfY291bnQiOjB9";s:5:"icons";a:1:{s:7:"default";s:75:"https://ps.w.org/advanced-custom-fields/assets/icon-256x256.png?rev=3079482";}s:7:"banners";a:2:{s:3:"low";s:77:"https://ps.w.org/advanced-custom-fields/assets/banner-772x250.jpg?rev=1729102";s:4:"high";s:78:"https://ps.w.org/advanced-custom-fields/assets/banner-1544x500.jpg?rev=1729099";}s:8:"requires";s:3:"6.0";s:12:"requires_php";s:3:"7.4";s:12:"release_date";s:8:"20241007";s:13:"license_valid";b:1;}}s:9:"no_update";a:0:{}s:10:"expiration";i:172800;s:6:"status";i:1;s:7:"checked";a:1:{s:34:"advanced-custom-fields-pro/acf.php";s:5:"6.3.7";}}', 'off') ;
INSERT INTO `nd_options` ( `option_id`, `option_name`, `option_value`, `autoload`) VALUES
(3145, '_site_transient_update_plugins', 'O:8:"stdClass":5:{s:12:"last_checked";i:**********;s:8:"response";a:2:{s:32:"wp-seopress-pro/seopress-pro.php";O:8:"stdClass":19:{s:11:"new_version";s:5:"8.2.2";s:14:"stable_version";s:5:"8.2.2";s:4:"name";s:12:"SEOPress PRO";s:4:"slug";s:12:"seopress-pro";s:3:"url";s:63:"https://www.seopress.org/fr/downloads/seopress-pro/?changelog=1";s:12:"last_updated";s:19:"2024-10-07 13:52:35";s:8:"homepage";s:25:"https://www.seopress.org/";s:7:"package";s:196:"https://www.seopress.org/fr/edd-sl/package_download/MTcyODQ2NDEzOTpmNmVlMzk4Nzc3OGNkM2I4ODg3ODQ3N2Y2YmU0MmU3NDoxMTU6NGE3YmFjYTNiOWExMzdhMDkyN2YzNjgzMjdjZjFhYjA6aHR0cHNALy9udW1lcmlkYW5zZS5sb2NhbDow";s:13:"download_link";s:196:"https://www.seopress.org/fr/edd-sl/package_download/MTcyODQ2NDEzOTpmNmVlMzk4Nzc3OGNkM2I4ODg3ODQ3N2Y2YmU0MmU3NDoxMTU6NGE3YmFjYTNiOWExMzdhMDkyN2YzNjgzMjdjZjFhYjA6aHR0cHNALy9udW1lcmlkYW5zZS5sb2NhbDow";s:8:"sections";a:3:{s:11:"description";s:15323:"<h3>Best SEO plugin for WordPress fully integrated with all page builders and themes!</h3>\n<h3>Now with AI (GPT 4) to automagically generate meta title, description and alternative texts for images!</h3>\n<p>SEOPress is a powerful WordPress SEO plugin to optimize your SEO, boost your traffic, improve social sharing, build custom HTML and XML Sitemaps, create optimized breadcrumbs, add schemas / Google Structured data types, manage 301 redirections and so much more.<br /></p>\n<p>&#x2714; <strong><a href="https://www.seopress.org/features/page-builders-integration/">Universal SEO metabox</a>: edit all your SEO from any page builder / theme builder. No more back and forth between your editor and the WordPress administration</strong>\n&#x2714; <strong>No advertising, no footprints, white label, in backend AND frontend</strong>\n&#x2714; <strong>Content analysis to help you write content optimized for search engines with unlimited target keywords</strong>\n&#x2714; <strong><a href="https://www.seopress.org/solutions/migrate-from/">Import your post and term metadatas</a> from other SEO plugins or CSV file in 1 click</strong>\n&#x2714; <strong><a href="https://translate.wordpress.org/projects/wp-plugins/wp-seopress">Translated into 25 languages (and counting)</a></strong>\n&#x2714; <strong>Trusted by over 300,000 WordPress websites since 2017</strong></p>\n<p><a href="https://www.seopress.org/pricing/"><strong>Buy SEOPress PRO: only $49 / year / 5 sites</strong></a></p>\n<p>[youtube <a href="https://www.youtube.com/watch?v=4ysKFVr_nu0">https://www.youtube.com/watch?v=4ysKFVr_nu0</a>]</p>\n<p><a href="https://www.seopress.org/features/">Features</a> | <a href="https://www.seopress.org/solutions/migrate-from/">Migrate</a> | <a href="https://www.seopress.org/wordpress-seo-plugins/pro/">PRO</a> | <a href="https://www.seopress.org/integrations/">Integrations</a> | <a href="https://www.seopress.org/support/">Support</a> | <a href="https://www.seopress.org/features/seopress-white-label/">White Label</a> | <a href="https://www.seopress.org/features/openai/">AI</a></p>\n<h3>Why SEOPress is the best WordPress SEO plugin?</h3>\n<ul>\n    <li><a href="https://www.seopress.org/seopress-productivity/"><strong>Save time</strong></a>: you prefer to work with Excel or Google Spreadsheets? No problem, you can import / export your metadata from CSV files with SEOPress PRO in few clicks!</li>\n    <li><a href="https://www.seopress.org/pricing/"><strong>Save money</strong></a>: SEOPress PRO is available for $49 / year / 5 sites. Go unlimited sites for just $149 / year!</li>\n    <li><a href="https://www.seopress.org/features/"><strong>All in one SEO plugin</strong></a>: comes with all the features you need to optimize the SEO of your WordPress site. No need to install additional extensions to manage redirects, schemas, XML sitemaps... You reduce the risk of conflicts and maintenance costs. You don\'t need a special feature? Deactivate it with one click without losing your configuration. Child\'s play !</li>\n    <li><strong>Easy AND ready to use</strong>: you doesn\'t need to know SEO or code to use SEOPress. Most of the parameters are automatically set. And thanks to our installation wizard, configuring SEOPress has never been easier. To go further, we provide many <a href="https://www.seopress.org/support/">free tutorials</a> and <a href="https://www.seopress.org/support/ebooks/">ebooks to learn SEO</a> in order to better understand how to position your content on search engines.</li>\n</ul>\n<h3>SEOPress Free Features</h3>\n<ul>\n    <li><a href="https://www.seopress.org/support/guides/google-indexing-api-with-seopress/"><strong>Google Indexing API and IndexNow API (Bing / Yandex)</strong></a> to quickly index its content in these search engines</li>\n    <li><strong>Installation wizard</strong> to quickly setup your site</li>\n    <li><strong>Content analysis</strong> with unlimited keywords to help you write optimized content for search engines</li>\n    <li><strong>Mobile / Desktop Google Preview</strong> to see how your post will looks like in Google search results</li>\n    <li><strong>Facebook &amp; X (ex-Twitter) Social Preview</strong> to see how your post will looks like on social media to increase conversions</li>\n    <li><strong>Titles</strong> (with <a href="https://www.seopress.org/support/guides/manage-titles-meta-descriptions/">dynamic variables</a>: custom fields, terms taxonomie...)</li>\n    <li><strong>Meta descriptions</strong> (with dynamic variables too)</li>\n    <li><a href="https://www.seopress.org/features/social-media/"><strong>Open Graph and X (ex-Twitter) Cards</strong></a> to improve social media sharing (Facebook, LinkedIn, Instagram, X (ex-Twitter), Pinterest, WhatsApp...)</li>\n    <li><strong>Google Knowledge Graph</strong></li>\n    <li><a href="https://www.seopress.org/features/google-analytics/"><strong>Google Analytics</strong> and <strong>Matomo</strong></a> with downloads tracking, custom dimensions, ip anonymization, remarketing, demographics and interest reporting, cross-domain tracking...(<a href="https://www.seopress.org/features/seopress-white-label/">GDPR compatibility</a>)</li>\n    <li><strong>Microsoft Clarity integration</strong>: to capture session recordings, get instant heatmaps and powerful Insights for Free. Know how people interact with your site to improve user experience and conversions</li>\n    <li><strong>Custom Canonical URL</strong></li>\n    <li><a href="https://www.seopress.org/support/guides/manage-meta-robots/"><strong>Meta robots</strong></a> (noindex, nofollow, noimageindex, noarchive, nosnippet)</li>\n    <li>Build your <a href="https://www.seopress.org/features/sitemaps/"><strong>custom XML Sitemap</strong></a> to improve search indexing</li>\n    <li><strong>Image XML Sitemaps</strong> to improve search indexing for Google Images</li>\n    <li>Build your custom <strong>HTML Sitemap</strong> to enhanced navigation for visitors and improve search indexing</li>\n    <li>Link your social media accounts to your site</li>\n    <li><strong>Redirections</strong> in post, pages, custom post types</li>\n    <li>Remove /category/ in URLs</li>\n    <li>Remove /product-category/ in URLs</li>\n    <li>Remove ?replytocom to avoid duplicate content</li>\n    <li>Redirect attachment pages to post parent</li>\n    <li>Redirect attachment pages to their file URL</li>\n    <li><a href="https://www.seopress.org/features/image-seo/"><strong>Image SEO</strong></a>: Automatically set the image title / alt / caption / description</li>\n    <li>Import / Export settings from site to site.</li>\n    <li><a href="https://www.seopress.org/solutions/migrate-from/">Import posts and terms metadata</a> from Yoast SEO, All In One SEO, SEO Framework, Rank Math, SEO Ultimate, WP Meta SEO, Premium SEO Pack, Squirrly and many other SEO plugins</li>\n    <li>...</li>\n</ul>\n<p><a href="https://www.seopress.org/wordpress-seo-plugins/free/features/">Check out all SEOPress Free features here</a></p>\n<h3>SEOPress PRO: to go further with your SEO</h3>\n<p>[youtube <a href="https://youtu.be/zxGCY-bJYwE">https://youtu.be/zxGCY-bJYwE</a>]</p>\n<ul>\n    <li><a href="https://www.seopress.org/features/openai/"><strong>Use Artificial Intelligence (GPT 4 / Vision and GPT 3.5 Turbo) to generate SEO metadata and alternative texts for image files. Bulk actions supported.</strong></a></li>\n    <li><a href="https://www.seopress.org/features/site-audit/"><strong>Site Audit: Detect technical issues that could harm your site\'s SEO directly from your WordPress administration.</strong></a></li>\n    <li><a href="https://www.seopress.org/features/seo-alerts/">Receive SEO alerts to prevent breaking your SEO before it\'s too late</a></li>\n    <li><a href="https://www.seopress.org/support/guides/import-metadata-from-a-csv-file-with-seopress-pro/"><strong>Import / export metadata</strong></a> (titles, open graph, robots...) from / to CSV file</li>\n    <li><a href="https://www.seopress.org/features/sitemaps/"><strong>Video XML Sitemap</strong></a> to improve rankings in video search results. YouTube videos are automatically added.</li>\n    <li>Internal linking suggestions</li>\n    <li>Inspect URL with Google Search Console: get details about crawling, indexing, mobile compatibility, schemas and more.</li>\n    <li><a href="https://www.seopress.org/features/google-search-console/"><strong>Search Console integration</strong></a>: get insights from your post / page / post type list with clicks, positions, CTR and impressions.</li>\n    <li><a href="https://www.seopress.org/features/google-suggest/"><strong>Google Suggestions in Content Analysis</strong></a> to find the top 10 Google suggestions instantly. This is useful if you want to work with the long tail technique.</li>\n    <li><a href="https://www.seopress.org/features/google-structured-data-types/"><strong>Google Structured Data types</strong> (schema.org)</a>:\n        <ol>\n            <li>article</li>\n            <li>local business</li>\n            <li>service</li>\n            <li>how-to</li>\n            <li>FAQ</li>\n            <li>course</li>\n            <li>recipe</li>\n            <li>software application</li>\n            <li>video</li>\n            <li>event</li>\n            <li>product</li>\n            <li>job</li>\n            <li>simple review</li>\n            <li>site navigation element</li>\n            <li>custom schema</li>\n        </ol>\n    </li><li><strong>Automatic Schemas</strong> with advanced conditions (AND, OR, Post types, taxonomies)</li>\n    <li><a href="https://www.seopress.org/features/breadcrumbs/"><strong>Breadcrumbs</strong></a> optimized with Schema.org, A11Y ready.</li>\n    <li><a href="https://www.seopress.org/features/google-analytics/"><strong>Google Analytics Stats in Dashboard</strong></a> to quickly see your metrics without leaving your site</li>\n    <li><a href="https://www.seopress.org/features/local-seo/"><strong>Google Local Business</strong></a> to boost your local store</li>\n    <li><strong>Broken link checker (SEOPress BOT)</strong>: scan all your links in content to find errors (e.g. 404...)</li>\n    <li><a href="https://www.seopress.org/features/woocommerce-seo/"><strong>WooCommerce</strong></a>: Product Global Identifiers type and Product Global Identifiers fields to WooCommerce metabox for product schema (barcode), disable crawling on cart page, checkout page, customer account pages, add OG Price / OG Currency for better sharing and more</li>\n    <li><strong>Easy Digital Downloads</strong>: add OG Price / OG Currency, remove EDD meta generator</li>\n    <li><a href="https://www.seopress.org/features/breadcrumbs/"><strong>Custom Breadcrumbs</strong></a> for single post types / term taxonomy</li>\n    <li><strong>Google Page Speed Insights</strong> to analyse your site performances on Mobile / Desktop + your Core Web Vitals</li>\n    <li><a href="https://www.seopress.org/features/google-analytics/"><strong>Google Enhanced Ecommerce for WooCommerce</strong></a>: measure purchases, singular product view details, additions to and removals from shopping carts</li>\n    <li><a href="https://www.seopress.org/features/htaccess-robots-txt/">Edit your <strong>robots.txt</strong></a> file from the admin (multisite / multidomain ready)</li>\n    <li><strong>Google News Sitemap</strong> to get your posts on Google News</li>\n    <li><strong>404 Monitoring</strong>: Monitor your 404 errors to improve user experience, performances and increase the crawl budget allocated by Google</li>\n    <li><strong>Redirect 404 to homepage/custom url automatically</strong> with custom status code (301, 302, 307, 410 or 451)</li>\n    <li>Email notifications on 404</li>\n    <li><a href="https://www.seopress.org/features/301-redirects/"><strong>Redirect manager</strong></a>: create unlimited 301, 302, 307, 410 and 451 redirections. Regular expressions supported. Import / export redirections to CSV or htaccess file.</li>\n    <li>Import redirections using CSV</li>\n    <li>Import redirections from Redirections plugin (via a JSON file)</li>\n    <li><a href="https://www.seopress.org/features/htaccess-robots-txt/">Edit your <strong>htaccess file</strong></a> from the admin</li>\n    <li>Easily customize your <strong>RSS feeds</strong></li>\n    <li>...</li>\n</ul>\n<p><a href="https://www.seopress.org/pricing/"><strong>Buy SEOPress PRO now!</strong></a></p>\n<h3>WooCommerce SEO (SEOPress PRO required)</h3>\nWe support WooCommerce and Easy Digital Downloads for e-commerce sites.\n<ul>\n    <li>Price and currency meta tags to improve social sharing</li>\n    <li>XML sitemaps for products</li>\n    <li>Support for WooCommerce product images and WooCommerce image galleries for the XML sitemap</li>\n    <li>Centralized way to set noindex meta robots tags on pages like cart, checkout...</li>\n    <li>Remove WooCommerce generator meta tag in the source code</li>\n    <li>Create manual and/or automatic "product" schemas in JSON-LD to increase visibility in Google search results</li>\n    <li>WooCommerce support for our breadcrumbs</li>\n    <li>Global dynamic tags to insert in your metas titles / descriptions</li>\n    <li>Product Global Identifiers type and Product Global Identifiers fields to WooCommerce metabox for product schema (barcode)</li>\n    <li>...</li>\n</ul>\n<p><a href="https://www.seopress.org/pricing/"><strong>Increase your sales now!</strong></a></p>\n<h3><a href="https://www.seopress.org/features/page-builders-integration/">Universal SEO metabox</a>: edit your metadata from any page builder / editor</h3>\n<p>[youtube <a href="https://youtu.be/sf0ocG7vQMM">https://youtu.be/sf0ocG7vQMM</a>]</p>\n<h3>Elementor + SEOPress: perfect combo!</h3>\nWe provide deep integration with Elementor page builder UI, see below:\n<p>[youtube <a href="https://www.youtube.com/watch?v=oC5QZ0_TH_g">https://www.youtube.com/watch?v=oC5QZ0_TH_g</a>]</p>\n<h3>SEOPress Insights: Off-site SEO plugin to track your rankings and backlinks in WordPress</h3>\n<ul>\n    <li><strong>Keyword rank tracker</strong>: 51 Google Search locations available</li>\n    <li>Track <strong>50 keywords</strong> per site daily</li>\n    <li>Track your <strong>competitors</strong>: who ranks first on your keywords</li>\n    <li>Monitor and analyze your <strong>backlinks</strong> weekly</li>\n    <li><strong>Google trends</strong> to find new and relevant ideas for your content marketing strategy</li>\n    <li>Your <strong>data accessible for life</strong>: export it to a CSV, PDF or Excel file. Sort, order, filter your data right from your WordPress.</li>\n    <li>Receive <strong>email and Slack alerts</strong> for your rankings to easily follow them</li>\n</ul>\n<p>[youtube <a href="https://youtu.be/p6v9Jd5lRIU">https://youtu.be/p6v9Jd5lRIU</a>]</p>\n<p><a href="https://www.seopress.org/pricing/"><strong>Buy SEOPress Insights now!</strong></a></p>\n<h3>Developers will love SEOPress!</h3>\n<ul>\n    <li>Hundreds of hooks are available to extend SEOPress. <a href="https://www.seopress.org/support/hooks/">Browse them all here</a>!</li>\n    <li>Plus we have a <a href="https://www.seopress.org/support/guides/get-started-with-the-seopress-rest-api/">REST API</a> to build static websites.</li>\n    <li>Finally, <a href="https://www.seopress.org/support/guides/seopress-wp-cli/"><strong>WP CLI commands</strong></a> are available.</li>\n</ul>";s:9:"changelog";s:1170:"<h4> 8.2 <a href="https://www.seopress.org/newsroom/product-news/seopress-8-2/">Read the blog post update</a> </h4>\n<ul>\n<li>NEW Site Audit: Detect technical issues that could harm your site\'s SEO directly from your WordPress administration (<a href="https://www.seopress.org/features/site-audit/">https://www.seopress.org/features/site-audit/</a>) &#x1f389;</li>\n<li>NEW Add a notice to the Block Editor on slug changes to quickly create a redirection to keep your SEO (PRO)</li>\n<li>INFO Table of contents Block: allow "paragraph" / "div" for the title of the block</li>\n<li>INFO Add notice to robots.txt settings tab if a physical file is already present on your server</li>\n<li>INFO Support robots.txt file for WP multisite (subdirectories installation with custom domains)</li>\n<li>INFO Strengthened security</li>\n<li>FIX Wizard redirect when updating SEOPress PRO</li>\n<li>FIX Internal links list in standard content analysis metabox</li>\n</ul>\n<p><a href="https://www.seopress.org/changelog/">View our complete changelog</a>\n<a href="https://www.seopress.org/support/guides/how-to-downgrade-seopress-pro-to-a-previous-version/">Need to downgrade/rollback?</a></p>";s:12:"installation";s:313:"<ol>\n<li>Upload \'wp-seopress\' to the \'/wp-content/plugins/\' directory</li>\n<li>Activate the plugin through the \'Plugins\' menu in WordPress</li>\n<li>Click on SEOPress and apply settings.</li>\n</ol>\n<p><a href="https://www.seopress.org/support/guides/get-started-seopress/">Requirements / Installation guide</a></p>";}s:7:"banners";a:2:{s:4:"high";s:61:"https://ps.w.org/wp-seopress/assets/banner-1544x500.png?rev=1";s:3:"low";s:60:"https://ps.w.org/wp-seopress/assets/banner-772x250.png?rev=1";}s:5:"icons";a:2:{s:2:"1x";s:103:"https://www.seopress.org/fr/wp-content/uploads/sites/2/edd/2024/02/logo-square-seopress-pro-128x128.png";s:2:"2x";s:103:"https://www.seopress.org/fr/wp-content/uploads/sites/2/edd/2024/02/logo-square-seopress-pro-256x256.png";}s:10:"stable_tag";s:3:"8.2";s:6:"tested";s:5:"6.6.2";s:11:"description";a:1:{i:0;s:15323:"<h3>Best SEO plugin for WordPress fully integrated with all page builders and themes!</h3>\n<h3>Now with AI (GPT 4) to automagically generate meta title, description and alternative texts for images!</h3>\n<p>SEOPress is a powerful WordPress SEO plugin to optimize your SEO, boost your traffic, improve social sharing, build custom HTML and XML Sitemaps, create optimized breadcrumbs, add schemas / Google Structured data types, manage 301 redirections and so much more.<br /></p>\n<p>&#x2714; <strong><a href="https://www.seopress.org/features/page-builders-integration/">Universal SEO metabox</a>: edit all your SEO from any page builder / theme builder. No more back and forth between your editor and the WordPress administration</strong>\n&#x2714; <strong>No advertising, no footprints, white label, in backend AND frontend</strong>\n&#x2714; <strong>Content analysis to help you write content optimized for search engines with unlimited target keywords</strong>\n&#x2714; <strong><a href="https://www.seopress.org/solutions/migrate-from/">Import your post and term metadatas</a> from other SEO plugins or CSV file in 1 click</strong>\n&#x2714; <strong><a href="https://translate.wordpress.org/projects/wp-plugins/wp-seopress">Translated into 25 languages (and counting)</a></strong>\n&#x2714; <strong>Trusted by over 300,000 WordPress websites since 2017</strong></p>\n<p><a href="https://www.seopress.org/pricing/"><strong>Buy SEOPress PRO: only $49 / year / 5 sites</strong></a></p>\n<p>[youtube <a href="https://www.youtube.com/watch?v=4ysKFVr_nu0">https://www.youtube.com/watch?v=4ysKFVr_nu0</a>]</p>\n<p><a href="https://www.seopress.org/features/">Features</a> | <a href="https://www.seopress.org/solutions/migrate-from/">Migrate</a> | <a href="https://www.seopress.org/wordpress-seo-plugins/pro/">PRO</a> | <a href="https://www.seopress.org/integrations/">Integrations</a> | <a href="https://www.seopress.org/support/">Support</a> | <a href="https://www.seopress.org/features/seopress-white-label/">White Label</a> | <a href="https://www.seopress.org/features/openai/">AI</a></p>\n<h3>Why SEOPress is the best WordPress SEO plugin?</h3>\n<ul>\n    <li><a href="https://www.seopress.org/seopress-productivity/"><strong>Save time</strong></a>: you prefer to work with Excel or Google Spreadsheets? No problem, you can import / export your metadata from CSV files with SEOPress PRO in few clicks!</li>\n    <li><a href="https://www.seopress.org/pricing/"><strong>Save money</strong></a>: SEOPress PRO is available for $49 / year / 5 sites. Go unlimited sites for just $149 / year!</li>\n    <li><a href="https://www.seopress.org/features/"><strong>All in one SEO plugin</strong></a>: comes with all the features you need to optimize the SEO of your WordPress site. No need to install additional extensions to manage redirects, schemas, XML sitemaps... You reduce the risk of conflicts and maintenance costs. You don\'t need a special feature? Deactivate it with one click without losing your configuration. Child\'s play !</li>\n    <li><strong>Easy AND ready to use</strong>: you doesn\'t need to know SEO or code to use SEOPress. Most of the parameters are automatically set. And thanks to our installation wizard, configuring SEOPress has never been easier. To go further, we provide many <a href="https://www.seopress.org/support/">free tutorials</a> and <a href="https://www.seopress.org/support/ebooks/">ebooks to learn SEO</a> in order to better understand how to position your content on search engines.</li>\n</ul>\n<h3>SEOPress Free Features</h3>\n<ul>\n    <li><a href="https://www.seopress.org/support/guides/google-indexing-api-with-seopress/"><strong>Google Indexing API and IndexNow API (Bing / Yandex)</strong></a> to quickly index its content in these search engines</li>\n    <li><strong>Installation wizard</strong> to quickly setup your site</li>\n    <li><strong>Content analysis</strong> with unlimited keywords to help you write optimized content for search engines</li>\n    <li><strong>Mobile / Desktop Google Preview</strong> to see how your post will looks like in Google search results</li>\n    <li><strong>Facebook &amp; X (ex-Twitter) Social Preview</strong> to see how your post will looks like on social media to increase conversions</li>\n    <li><strong>Titles</strong> (with <a href="https://www.seopress.org/support/guides/manage-titles-meta-descriptions/">dynamic variables</a>: custom fields, terms taxonomie...)</li>\n    <li><strong>Meta descriptions</strong> (with dynamic variables too)</li>\n    <li><a href="https://www.seopress.org/features/social-media/"><strong>Open Graph and X (ex-Twitter) Cards</strong></a> to improve social media sharing (Facebook, LinkedIn, Instagram, X (ex-Twitter), Pinterest, WhatsApp...)</li>\n    <li><strong>Google Knowledge Graph</strong></li>\n    <li><a href="https://www.seopress.org/features/google-analytics/"><strong>Google Analytics</strong> and <strong>Matomo</strong></a> with downloads tracking, custom dimensions, ip anonymization, remarketing, demographics and interest reporting, cross-domain tracking...(<a href="https://www.seopress.org/features/seopress-white-label/">GDPR compatibility</a>)</li>\n    <li><strong>Microsoft Clarity integration</strong>: to capture session recordings, get instant heatmaps and powerful Insights for Free. Know how people interact with your site to improve user experience and conversions</li>\n    <li><strong>Custom Canonical URL</strong></li>\n    <li><a href="https://www.seopress.org/support/guides/manage-meta-robots/"><strong>Meta robots</strong></a> (noindex, nofollow, noimageindex, noarchive, nosnippet)</li>\n    <li>Build your <a href="https://www.seopress.org/features/sitemaps/"><strong>custom XML Sitemap</strong></a> to improve search indexing</li>\n    <li><strong>Image XML Sitemaps</strong> to improve search indexing for Google Images</li>\n    <li>Build your custom <strong>HTML Sitemap</strong> to enhanced navigation for visitors and improve search indexing</li>\n    <li>Link your social media accounts to your site</li>\n    <li><strong>Redirections</strong> in post, pages, custom post types</li>\n    <li>Remove /category/ in URLs</li>\n    <li>Remove /product-category/ in URLs</li>\n    <li>Remove ?replytocom to avoid duplicate content</li>\n    <li>Redirect attachment pages to post parent</li>\n    <li>Redirect attachment pages to their file URL</li>\n    <li><a href="https://www.seopress.org/features/image-seo/"><strong>Image SEO</strong></a>: Automatically set the image title / alt / caption / description</li>\n    <li>Import / Export settings from site to site.</li>\n    <li><a href="https://www.seopress.org/solutions/migrate-from/">Import posts and terms metadata</a> from Yoast SEO, All In One SEO, SEO Framework, Rank Math, SEO Ultimate, WP Meta SEO, Premium SEO Pack, Squirrly and many other SEO plugins</li>\n    <li>...</li>\n</ul>\n<p><a href="https://www.seopress.org/wordpress-seo-plugins/free/features/">Check out all SEOPress Free features here</a></p>\n<h3>SEOPress PRO: to go further with your SEO</h3>\n<p>[youtube <a href="https://youtu.be/zxGCY-bJYwE">https://youtu.be/zxGCY-bJYwE</a>]</p>\n<ul>\n    <li><a href="https://www.seopress.org/features/openai/"><strong>Use Artificial Intelligence (GPT 4 / Vision and GPT 3.5 Turbo) to generate SEO metadata and alternative texts for image files. Bulk actions supported.</strong></a></li>\n    <li><a href="https://www.seopress.org/features/site-audit/"><strong>Site Audit: Detect technical issues that could harm your site\'s SEO directly from your WordPress administration.</strong></a></li>\n    <li><a href="https://www.seopress.org/features/seo-alerts/">Receive SEO alerts to prevent breaking your SEO before it\'s too late</a></li>\n    <li><a href="https://www.seopress.org/support/guides/import-metadata-from-a-csv-file-with-seopress-pro/"><strong>Import / export metadata</strong></a> (titles, open graph, robots...) from / to CSV file</li>\n    <li><a href="https://www.seopress.org/features/sitemaps/"><strong>Video XML Sitemap</strong></a> to improve rankings in video search results. YouTube videos are automatically added.</li>\n    <li>Internal linking suggestions</li>\n    <li>Inspect URL with Google Search Console: get details about crawling, indexing, mobile compatibility, schemas and more.</li>\n    <li><a href="https://www.seopress.org/features/google-search-console/"><strong>Search Console integration</strong></a>: get insights from your post / page / post type list with clicks, positions, CTR and impressions.</li>\n    <li><a href="https://www.seopress.org/features/google-suggest/"><strong>Google Suggestions in Content Analysis</strong></a> to find the top 10 Google suggestions instantly. This is useful if you want to work with the long tail technique.</li>\n    <li><a href="https://www.seopress.org/features/google-structured-data-types/"><strong>Google Structured Data types</strong> (schema.org)</a>:\n        <ol>\n            <li>article</li>\n            <li>local business</li>\n            <li>service</li>\n            <li>how-to</li>\n            <li>FAQ</li>\n            <li>course</li>\n            <li>recipe</li>\n            <li>software application</li>\n            <li>video</li>\n            <li>event</li>\n            <li>product</li>\n            <li>job</li>\n            <li>simple review</li>\n            <li>site navigation element</li>\n            <li>custom schema</li>\n        </ol>\n    </li><li><strong>Automatic Schemas</strong> with advanced conditions (AND, OR, Post types, taxonomies)</li>\n    <li><a href="https://www.seopress.org/features/breadcrumbs/"><strong>Breadcrumbs</strong></a> optimized with Schema.org, A11Y ready.</li>\n    <li><a href="https://www.seopress.org/features/google-analytics/"><strong>Google Analytics Stats in Dashboard</strong></a> to quickly see your metrics without leaving your site</li>\n    <li><a href="https://www.seopress.org/features/local-seo/"><strong>Google Local Business</strong></a> to boost your local store</li>\n    <li><strong>Broken link checker (SEOPress BOT)</strong>: scan all your links in content to find errors (e.g. 404...)</li>\n    <li><a href="https://www.seopress.org/features/woocommerce-seo/"><strong>WooCommerce</strong></a>: Product Global Identifiers type and Product Global Identifiers fields to WooCommerce metabox for product schema (barcode), disable crawling on cart page, checkout page, customer account pages, add OG Price / OG Currency for better sharing and more</li>\n    <li><strong>Easy Digital Downloads</strong>: add OG Price / OG Currency, remove EDD meta generator</li>\n    <li><a href="https://www.seopress.org/features/breadcrumbs/"><strong>Custom Breadcrumbs</strong></a> for single post types / term taxonomy</li>\n    <li><strong>Google Page Speed Insights</strong> to analyse your site performances on Mobile / Desktop + your Core Web Vitals</li>\n    <li><a href="https://www.seopress.org/features/google-analytics/"><strong>Google Enhanced Ecommerce for WooCommerce</strong></a>: measure purchases, singular product view details, additions to and removals from shopping carts</li>\n    <li><a href="https://www.seopress.org/features/htaccess-robots-txt/">Edit your <strong>robots.txt</strong></a> file from the admin (multisite / multidomain ready)</li>\n    <li><strong>Google News Sitemap</strong> to get your posts on Google News</li>\n    <li><strong>404 Monitoring</strong>: Monitor your 404 errors to improve user experience, performances and increase the crawl budget allocated by Google</li>\n    <li><strong>Redirect 404 to homepage/custom url automatically</strong> with custom status code (301, 302, 307, 410 or 451)</li>\n    <li>Email notifications on 404</li>\n    <li><a href="https://www.seopress.org/features/301-redirects/"><strong>Redirect manager</strong></a>: create unlimited 301, 302, 307, 410 and 451 redirections. Regular expressions supported. Import / export redirections to CSV or htaccess file.</li>\n    <li>Import redirections using CSV</li>\n    <li>Import redirections from Redirections plugin (via a JSON file)</li>\n    <li><a href="https://www.seopress.org/features/htaccess-robots-txt/">Edit your <strong>htaccess file</strong></a> from the admin</li>\n    <li>Easily customize your <strong>RSS feeds</strong></li>\n    <li>...</li>\n</ul>\n<p><a href="https://www.seopress.org/pricing/"><strong>Buy SEOPress PRO now!</strong></a></p>\n<h3>WooCommerce SEO (SEOPress PRO required)</h3>\nWe support WooCommerce and Easy Digital Downloads for e-commerce sites.\n<ul>\n    <li>Price and currency meta tags to improve social sharing</li>\n    <li>XML sitemaps for products</li>\n    <li>Support for WooCommerce product images and WooCommerce image galleries for the XML sitemap</li>\n    <li>Centralized way to set noindex meta robots tags on pages like cart, checkout...</li>\n    <li>Remove WooCommerce generator meta tag in the source code</li>\n    <li>Create manual and/or automatic "product" schemas in JSON-LD to increase visibility in Google search results</li>\n    <li>WooCommerce support for our breadcrumbs</li>\n    <li>Global dynamic tags to insert in your metas titles / descriptions</li>\n    <li>Product Global Identifiers type and Product Global Identifiers fields to WooCommerce metabox for product schema (barcode)</li>\n    <li>...</li>\n</ul>\n<p><a href="https://www.seopress.org/pricing/"><strong>Increase your sales now!</strong></a></p>\n<h3><a href="https://www.seopress.org/features/page-builders-integration/">Universal SEO metabox</a>: edit your metadata from any page builder / editor</h3>\n<p>[youtube <a href="https://youtu.be/sf0ocG7vQMM">https://youtu.be/sf0ocG7vQMM</a>]</p>\n<h3>Elementor + SEOPress: perfect combo!</h3>\nWe provide deep integration with Elementor page builder UI, see below:\n<p>[youtube <a href="https://www.youtube.com/watch?v=oC5QZ0_TH_g">https://www.youtube.com/watch?v=oC5QZ0_TH_g</a>]</p>\n<h3>SEOPress Insights: Off-site SEO plugin to track your rankings and backlinks in WordPress</h3>\n<ul>\n    <li><strong>Keyword rank tracker</strong>: 51 Google Search locations available</li>\n    <li>Track <strong>50 keywords</strong> per site daily</li>\n    <li>Track your <strong>competitors</strong>: who ranks first on your keywords</li>\n    <li>Monitor and analyze your <strong>backlinks</strong> weekly</li>\n    <li><strong>Google trends</strong> to find new and relevant ideas for your content marketing strategy</li>\n    <li>Your <strong>data accessible for life</strong>: export it to a CSV, PDF or Excel file. Sort, order, filter your data right from your WordPress.</li>\n    <li>Receive <strong>email and Slack alerts</strong> for your rankings to easily follow them</li>\n</ul>\n<p>[youtube <a href="https://youtu.be/p6v9Jd5lRIU">https://youtu.be/p6v9Jd5lRIU</a>]</p>\n<p><a href="https://www.seopress.org/pricing/"><strong>Buy SEOPress Insights now!</strong></a></p>\n<h3>Developers will love SEOPress!</h3>\n<ul>\n    <li>Hundreds of hooks are available to extend SEOPress. <a href="https://www.seopress.org/support/hooks/">Browse them all here</a>!</li>\n    <li>Plus we have a <a href="https://www.seopress.org/support/guides/get-started-with-the-seopress-rest-api/">REST API</a> to build static websites.</li>\n    <li>Finally, <a href="https://www.seopress.org/support/guides/seopress-wp-cli/"><strong>WP CLI commands</strong></a> are available.</li>\n</ul>";}s:9:"changelog";a:1:{i:0;s:1170:"<h4> 8.2 <a href="https://www.seopress.org/newsroom/product-news/seopress-8-2/">Read the blog post update</a> </h4>\n<ul>\n<li>NEW Site Audit: Detect technical issues that could harm your site\'s SEO directly from your WordPress administration (<a href="https://www.seopress.org/features/site-audit/">https://www.seopress.org/features/site-audit/</a>) &#x1f389;</li>\n<li>NEW Add a notice to the Block Editor on slug changes to quickly create a redirection to keep your SEO (PRO)</li>\n<li>INFO Table of contents Block: allow "paragraph" / "div" for the title of the block</li>\n<li>INFO Add notice to robots.txt settings tab if a physical file is already present on your server</li>\n<li>INFO Support robots.txt file for WP multisite (subdirectories installation with custom domains)</li>\n<li>INFO Strengthened security</li>\n<li>FIX Wizard redirect when updating SEOPress PRO</li>\n<li>FIX Internal links list in standard content analysis metabox</li>\n</ul>\n<p><a href="https://www.seopress.org/changelog/">View our complete changelog</a>\n<a href="https://www.seopress.org/support/guides/how-to-downgrade-seopress-pro-to-a-previous-version/">Need to downgrade/rollback?</a></p>";}s:12:"installation";a:1:{i:0;s:313:"<ol>\n<li>Upload \'wp-seopress\' to the \'/wp-content/plugins/\' directory</li>\n<li>Activate the plugin through the \'Plugins\' menu in WordPress</li>\n<li>Click on SEOPress and apply settings.</li>\n</ol>\n<p><a href="https://www.seopress.org/support/guides/get-started-seopress/">Requirements / Installation guide</a></p>";}s:6:"plugin";s:32:"wp-seopress-pro/seopress-pro.php";s:2:"id";s:32:"wp-seopress-pro/seopress-pro.php";}s:34:"advanced-custom-fields-pro/acf.php";O:8:"stdClass":12:{s:4:"slug";s:26:"advanced-custom-fields-pro";s:6:"plugin";s:34:"advanced-custom-fields-pro/acf.php";s:11:"new_version";s:5:"6.3.8";s:3:"url";s:36:"https://www.advancedcustomfields.com";s:6:"tested";s:5:"6.6.1";s:7:"package";s:361:"https://connect.advancedcustomfields.com/v2/plugins/download?version=6.3.8&token=eyJwIjoicHJvIiwiayI6ImIzSmtaWEpmYVdROU1UTTBNalV6ZkhSNWNHVTlaR1YyWld4dmNHVnlmR1JoZEdVOU1qQXhPQzB3Tnkwd01pQXdPVG94TnpvMU5BPT0iLCJ3cF91cmwiOiJodHRwczpcL1wvbnVtZXJpZGFuc2UubG9jYWwiLCJ3cF92ZXJzaW9uIjoiNi42LjIiLCJ3cF9tdWx0aXNpdGUiOjAsInBocF92ZXJzaW9uIjoiOC4xLjI5IiwiYmxvY2tfY291bnQiOjB9";s:5:"icons";a:1:{s:7:"default";s:75:"https://ps.w.org/advanced-custom-fields/assets/icon-256x256.png?rev=3079482";}s:7:"banners";a:2:{s:3:"low";s:77:"https://ps.w.org/advanced-custom-fields/assets/banner-772x250.jpg?rev=1729102";s:4:"high";s:78:"https://ps.w.org/advanced-custom-fields/assets/banner-1544x500.jpg?rev=1729099";}s:8:"requires";s:3:"6.0";s:12:"requires_php";s:3:"7.4";s:12:"release_date";s:8:"20241007";s:13:"license_valid";b:1;}}s:12:"translations";a:0:{}s:9:"no_update";a:12:{s:69:"add-descendants-as-submenu-items/add-descendants-as-submenu-items.php";O:8:"stdClass":10:{s:2:"id";s:46:"w.org/plugins/add-descendants-as-submenu-items";s:4:"slug";s:32:"add-descendants-as-submenu-items";s:6:"plugin";s:69:"add-descendants-as-submenu-items/add-descendants-as-submenu-items.php";s:11:"new_version";s:5:"1.2.2";s:3:"url";s:63:"https://wordpress.org/plugins/add-descendants-as-submenu-items/";s:7:"package";s:81:"https://downloads.wordpress.org/plugin/add-descendants-as-submenu-items.1.2.2.zip";s:5:"icons";a:1:{s:7:"default";s:76:"https://s.w.org/plugins/geopattern-icon/add-descendants-as-submenu-items.svg";}s:7:"banners";a:0:{}s:11:"banners_rtl";a:0:{}s:8:"requires";b:0;}s:45:"enable-media-replace/enable-media-replace.php";O:8:"stdClass":10:{s:2:"id";s:34:"w.org/plugins/enable-media-replace";s:4:"slug";s:20:"enable-media-replace";s:6:"plugin";s:45:"enable-media-replace/enable-media-replace.php";s:11:"new_version";s:5:"4.1.5";s:3:"url";s:51:"https://wordpress.org/plugins/enable-media-replace/";s:7:"package";s:69:"https://downloads.wordpress.org/plugin/enable-media-replace.4.1.5.zip";s:5:"icons";a:2:{s:2:"2x";s:73:"https://ps.w.org/enable-media-replace/assets/icon-256x256.png?rev=1940728";s:2:"1x";s:73:"https://ps.w.org/enable-media-replace/assets/icon-128x128.png?rev=1940728";}s:7:"banners";a:2:{s:2:"2x";s:76:"https://ps.w.org/enable-media-replace/assets/banner-1544x500.png?rev=2322194";s:2:"1x";s:75:"https://ps.w.org/enable-media-replace/assets/banner-772x250.png?rev=2322194";}s:11:"banners_rtl";a:0:{}s:8:"requires";s:5:"4.9.7";}s:59:"intuitive-custom-post-order/intuitive-custom-post-order.php";O:8:"stdClass":10:{s:2:"id";s:41:"w.org/plugins/intuitive-custom-post-order";s:4:"slug";s:27:"intuitive-custom-post-order";s:6:"plugin";s:59:"intuitive-custom-post-order/intuitive-custom-post-order.php";s:11:"new_version";s:7:"*******";s:3:"url";s:58:"https://wordpress.org/plugins/intuitive-custom-post-order/";s:7:"package";s:78:"https://downloads.wordpress.org/plugin/intuitive-custom-post-order.*******.zip";s:5:"icons";a:2:{s:2:"2x";s:80:"https://ps.w.org/intuitive-custom-post-order/assets/icon-256x256.png?rev=1078797";s:2:"1x";s:80:"https://ps.w.org/intuitive-custom-post-order/assets/icon-128x128.png?rev=1078797";}s:7:"banners";a:2:{s:2:"2x";s:83:"https://ps.w.org/intuitive-custom-post-order/assets/banner-1544x500.png?rev=1209666";s:2:"1x";s:82:"https://ps.w.org/intuitive-custom-post-order/assets/banner-772x250.png?rev=1078755";}s:11:"banners_rtl";a:0:{}s:8:"requires";s:5:"3.5.0";}s:53:"manage-privacy-options/baw-manage-privacy-options.php";O:8:"stdClass":10:{s:2:"id";s:36:"w.org/plugins/manage-privacy-options";s:4:"slug";s:22:"manage-privacy-options";s:6:"plugin";s:53:"manage-privacy-options/baw-manage-privacy-options.php";s:11:"new_version";s:3:"1.1";s:3:"url";s:53:"https://wordpress.org/plugins/manage-privacy-options/";s:7:"package";s:69:"https://downloads.wordpress.org/plugin/manage-privacy-options.1.1.zip";s:5:"icons";a:1:{s:2:"1x";s:75:"https://ps.w.org/manage-privacy-options/assets/icon-128x128.png?rev=1879768";}s:7:"banners";a:1:{s:2:"1x";s:77:"https://ps.w.org/manage-privacy-options/assets/banner-772x250.jpg?rev=1879767";}s:11:"banners_rtl";a:0:{}s:8:"requires";s:3:"4.8";}s:31:"wp-nested-pages/nestedpages.php";O:8:"stdClass":10:{s:2:"id";s:29:"w.org/plugins/wp-nested-pages";s:4:"slug";s:15:"wp-nested-pages";s:6:"plugin";s:31:"wp-nested-pages/nestedpages.php";s:11:"new_version";s:5:"3.2.9";s:3:"url";s:46:"https://wordpress.org/plugins/wp-nested-pages/";s:7:"package";s:64:"https://downloads.wordpress.org/plugin/wp-nested-pages.3.2.9.zip";s:5:"icons";a:1:{s:2:"1x";s:68:"https://ps.w.org/wp-nested-pages/assets/icon-128x128.png?rev=1690043";}s:7:"banners";a:1:{s:2:"1x";s:70:"https://ps.w.org/wp-nested-pages/assets/banner-772x250.png?rev=1690043";}s:11:"banners_rtl";a:0:{}s:8:"requires";s:3:"3.8";}s:41:"password-protected/password-protected.php";O:8:"stdClass":10:{s:2:"id";s:32:"w.org/plugins/password-protected";s:4:"slug";s:18:"password-protected";s:6:"plugin";s:41:"password-protected/password-protected.php";s:11:"new_version";s:5:"2.7.3";s:3:"url";s:49:"https://wordpress.org/plugins/password-protected/";s:7:"package";s:61:"https://downloads.wordpress.org/plugin/password-protected.zip";s:5:"icons";a:1:{s:2:"1x";s:71:"https://ps.w.org/password-protected/assets/icon-128x128.gif?rev=2824217";}s:7:"banners";a:2:{s:2:"2x";s:74:"https://ps.w.org/password-protected/assets/banner-1544x500.jpg?rev=3107315";s:2:"1x";s:73:"https://ps.w.org/password-protected/assets/banner-772x250.jpg?rev=3107315";}s:11:"banners_rtl";a:0:{}s:8:"requires";s:3:"4.6";}s:21:"safe-svg/safe-svg.php";O:8:"stdClass":10:{s:2:"id";s:22:"w.org/plugins/safe-svg";s:4:"slug";s:8:"safe-svg";s:6:"plugin";s:21:"safe-svg/safe-svg.php";s:11:"new_version";s:5:"2.2.6";s:3:"url";s:39:"https://wordpress.org/plugins/safe-svg/";s:7:"package";s:57:"https://downloads.wordpress.org/plugin/safe-svg.2.2.6.zip";s:5:"icons";a:2:{s:2:"1x";s:53:"https://ps.w.org/safe-svg/assets/icon.svg?rev=2779013";s:3:"svg";s:53:"https://ps.w.org/safe-svg/assets/icon.svg?rev=2779013";}s:7:"banners";a:2:{s:2:"2x";s:64:"https://ps.w.org/safe-svg/assets/banner-1544x500.png?rev=2683939";s:2:"1x";s:63:"https://ps.w.org/safe-svg/assets/banner-772x250.png?rev=2683939";}s:11:"banners_rtl";a:0:{}s:8:"requires";s:3:"6.4";}s:24:"wp-seopress/seopress.php";O:8:"stdClass":10:{s:2:"id";s:25:"w.org/plugins/wp-seopress";s:4:"slug";s:11:"wp-seopress";s:6:"plugin";s:24:"wp-seopress/seopress.php";s:11:"new_version";s:3:"8.2";s:3:"url";s:42:"https://wordpress.org/plugins/wp-seopress/";s:7:"package";s:58:"https://downloads.wordpress.org/plugin/wp-seopress.8.2.zip";s:5:"icons";a:1:{s:2:"1x";s:64:"https://ps.w.org/wp-seopress/assets/icon-128x128.gif?rev=3158903";}s:7:"banners";a:2:{s:2:"2x";s:67:"https://ps.w.org/wp-seopress/assets/banner-1544x500.png?rev=3151281";s:2:"1x";s:66:"https://ps.w.org/wp-seopress/assets/banner-772x250.png?rev=3151281";}s:11:"banners_rtl";a:0:{}s:8:"requires";s:3:"5.0";}s:21:"spatie-ray/wp-ray.php";O:8:"stdClass":10:{s:2:"id";s:24:"w.org/plugins/spatie-ray";s:4:"slug";s:10:"spatie-ray";s:6:"plugin";s:21:"spatie-ray/wp-ray.php";s:11:"new_version";s:5:"1.7.6";s:3:"url";s:41:"https://wordpress.org/plugins/spatie-ray/";s:7:"package";s:59:"https://downloads.wordpress.org/plugin/spatie-ray.1.7.6.zip";s:5:"icons";a:2:{s:2:"1x";s:55:"https://ps.w.org/spatie-ray/assets/icon.svg?rev=2796507";s:3:"svg";s:55:"https://ps.w.org/spatie-ray/assets/icon.svg?rev=2796507";}s:7:"banners";a:2:{s:2:"2x";s:66:"https://ps.w.org/spatie-ray/assets/banner-1544x500.png?rev=2455717";s:2:"1x";s:65:"https://ps.w.org/spatie-ray/assets/banner-772x250.png?rev=2455717";}s:11:"banners_rtl";a:0:{}s:8:"requires";s:3:"5.5";}s:33:"user-switching/user-switching.php";O:8:"stdClass":10:{s:2:"id";s:28:"w.org/plugins/user-switching";s:4:"slug";s:14:"user-switching";s:6:"plugin";s:33:"user-switching/user-switching.php";s:11:"new_version";s:5:"1.8.0";s:3:"url";s:45:"https://wordpress.org/plugins/user-switching/";s:7:"package";s:63:"https://downloads.wordpress.org/plugin/user-switching.1.8.0.zip";s:5:"icons";a:2:{s:2:"1x";s:59:"https://ps.w.org/user-switching/assets/icon.svg?rev=2032062";s:3:"svg";s:59:"https://ps.w.org/user-switching/assets/icon.svg?rev=2032062";}s:7:"banners";a:2:{s:2:"2x";s:70:"https://ps.w.org/user-switching/assets/banner-1544x500.png?rev=2204929";s:2:"1x";s:69:"https://ps.w.org/user-switching/assets/banner-772x250.png?rev=2204929";}s:11:"banners_rtl";a:0:{}s:8:"requires";s:3:"5.8";}s:53:"admin-bar-user-switching/admin-bar-user-switching.php";O:8:"stdClass":10:{s:2:"id";s:38:"w.org/plugins/admin-bar-user-switching";s:4:"slug";s:24:"admin-bar-user-switching";s:6:"plugin";s:53:"admin-bar-user-switching/admin-bar-user-switching.php";s:11:"new_version";s:3:"1.4";s:3:"url";s:55:"https://wordpress.org/plugins/admin-bar-user-switching/";s:7:"package";s:71:"https://downloads.wordpress.org/plugin/admin-bar-user-switching.1.4.zip";s:5:"icons";a:1:{s:2:"1x";s:77:"https://ps.w.org/admin-bar-user-switching/assets/icon-128x128.png?rev=1419846";}s:7:"banners";a:1:{s:2:"1x";s:79:"https://ps.w.org/admin-bar-user-switching/assets/banner-772x250.png?rev=1419846";}s:11:"banners_rtl";a:0:{}s:8:"requires";s:3:"3.1";}s:23:"wp-sort-order/index.php";O:8:"stdClass":10:{s:2:"id";s:27:"w.org/plugins/wp-sort-order";s:4:"slug";s:13:"wp-sort-order";s:6:"plugin";s:23:"wp-sort-order/index.php";s:11:"new_version";s:5:"1.3.3";s:3:"url";s:44:"https://wordpress.org/plugins/wp-sort-order/";s:7:"package";s:62:"https://downloads.wordpress.org/plugin/wp-sort-order.1.3.3.zip";s:5:"icons";a:2:{s:2:"2x";s:66:"https://ps.w.org/wp-sort-order/assets/icon-256x256.png?rev=1362889";s:2:"1x";s:66:"https://ps.w.org/wp-sort-order/assets/icon-128x128.png?rev=1361267";}s:7:"banners";a:2:{s:2:"2x";s:69:"https://ps.w.org/wp-sort-order/assets/banner-1544x500.png?rev=2043131";s:2:"1x";s:68:"https://ps.w.org/wp-sort-order/assets/banner-772x250.png?rev=2597136";}s:11:"banners_rtl";a:0:{}s:8:"requires";s:5:"3.5.0";}}s:7:"checked";a:1:{s:32:"wp-seopress-pro/seopress-pro.php";s:5:"8.2.1";}}', 'off'),
(3171, '_transient_timeout_acf_pro_validating_license', '**********', 'off'),
(3172, '_transient_acf_pro_validating_license', '1', 'off'),
(3178, '_transient_doing_cron', '**********.9066898822784423828125', 'on'),
(3181, '_site_transient_timeout_wpmdb_upgrade_data', '**********', 'off'),
(3182, '_site_transient_wpmdb_upgrade_data', 'a:5:{s:17:"wp-migrate-db-pro";a:4:{s:7:"version";s:5:"2.7.0";s:6:"tested";s:3:"6.6";s:8:"icon_url";s:60:"https://deliciousbrains.com/assets/images/icons/mdb/icon.svg";s:12:"requires_php";s:3:"5.6";}s:29:"wp-migrate-db-pro-media-files";a:4:{s:7:"version";s:5:"2.1.0";s:6:"tested";s:3:"5.8";s:8:"icon_url";s:60:"https://deliciousbrains.com/assets/images/icons/mdb/icon.svg";s:12:"requires_php";s:3:"5.6";}s:21:"wp-migrate-db-pro-cli";a:4:{s:7:"version";s:5:"1.6.0";s:6:"tested";s:3:"5.8";s:8:"icon_url";s:60:"https://deliciousbrains.com/assets/images/icons/mdb/icon.svg";s:12:"requires_php";s:3:"5.6";}s:33:"wp-migrate-db-pro-multisite-tools";a:4:{s:7:"version";s:5:"1.4.1";s:6:"tested";s:3:"5.8";s:8:"icon_url";s:60:"https://deliciousbrains.com/assets/images/icons/mdb/icon.svg";s:12:"requires_php";s:3:"5.6";}s:36:"wp-migrate-db-pro-theme-plugin-files";a:4:{s:7:"version";s:5:"1.2.0";s:6:"tested";s:3:"5.8";s:8:"icon_url";s:60:"https://deliciousbrains.com/assets/images/icons/mdb/icon.svg";s:12:"requires_php";s:3:"5.6";}}', 'off'),
(3185, '_site_transient_timeout_wp_theme_files_patterns-b06994ae842c04bd7d6ea8be3b734b21', '1728379441', 'off'),
(3186, '_site_transient_wp_theme_files_patterns-b06994ae842c04bd7d6ea8be3b734b21', 'a:2:{s:7:"version";s:5:"4.0.0";s:8:"patterns";a:0:{}}', 'off'),
(3188, '_site_transient_timeout_theme_roots', '1728379471', 'off'),
(3189, '_site_transient_theme_roots', 'a:1:{s:10:"lesanimals";s:7:"/themes";}', 'off'),
(3191, '_site_transient_timeout_wpmdb_licence_response', '1728420871', 'off') ;
INSERT INTO `nd_options` ( `option_id`, `option_name`, `option_value`, `autoload`) VALUES
(3192, '_site_transient_wpmdb_licence_response', '{"features":[],"addons_available":"1","addons_available_list":{"wp-migrate-db-pro-media-files":2351,"wp-migrate-db-pro-cli":3948,"wp-migrate-db-pro-multisite-tools":7999,"wp-migrate-db-pro-theme-plugin-files":36287},"addon_list":{"wp-migrate-db-pro-media-files":{"type":"feature","name":"Media Files","desc":"Allows you to push and pull your files in the Media Library between two WordPress installs. It can compare both libraries and only migrate those missing or updated, or it can do a complete copy of one site\\u2019s library to another. <a href=\\"https:\\/\\/deliciousbrains.com\\/wp-migrate-db-pro\\/doc\\/media-files-addon\\/?utm_campaign=addons%252Binstall&utm_source=MDB%252BPaid&utm_medium=insideplugin\\">More Details &rarr;<\\/a>","version":"2.1.0","beta_version":false,"tested":"6.6.1"},"wp-migrate-db-pro-cli":{"type":"feature","name":"CLI","desc":"Integrates WP Migrate with WP-CLI allowing you to run migrations from the command line: <code>wp migratedb &lt;push|pull&gt; &lt;url&gt; &lt;secret-key&gt;<\\/code> <code>[--find=&lt;strings&gt;] [--replace=&lt;strings&gt;] ...<\\/code> <a href=\\"https:\\/\\/deliciousbrains.com\\/wp-migrate-db-pro\\/doc\\/cli-addon\\/?utm_campaign=addons%252Binstall&utm_source=MDB%252BPaid&utm_medium=insideplugin\\">More Details &rarr;<\\/a>","required":"1.4b1","version":"1.6.0","beta_version":false,"tested":"6.6.1"},"wp-migrate-db-pro-multisite-tools":{"type":"feature","name":"Multisite Tools","desc":"Export a subsite as an SQL file that can then be imported as a single site install. <a href=\\"https:\\/\\/deliciousbrains.com\\/wp-migrate-db-pro\\/doc\\/multisite-tools-addon\\/?utm_campaign=addons%252Binstall&utm_source=MDB%252BPaid&utm_medium=insideplugin\\">More Details &rarr;<\\/a>","required":"1.5-dev","version":"1.4.1","beta_version":false,"tested":"6.6.1"},"wp-migrate-db-pro-theme-plugin-files":{"type":"feature","name":"Theme & Plugin Files","desc":"Allows you to push and pull your theme and plugin files between two WordPress installs. <a href=\\"https:\\/\\/deliciousbrains.com\\/wp-migrate-db-pro\\/doc\\/theme-plugin-files-addon\\/?utm_campaign=addons%252Binstall&utm_source=MDB%252BPaid&utm_medium=insideplugin\\">More Details &rarr;<\\/a>","required":"1.8.2b1","version":"1.2.0","beta_version":false,"tested":"6.6.1"}},"form_url":"https:\\/\\/api.deliciousbrains.com\\/?wc-api=delicious-brains&request=submit_support_request&licence_key=31f7c2a2-20a6-4c34-bf4b-7c7ae7c057e0&product=wp-migrate-db-pro","license_name":"Developer&nbsp;(Legacy)","display_name":"jb4","user_email":"<EMAIL>","upgrade_url":"https:\\/\\/deliciousbrains.com\\/my-account\\/license-upgrade\\/15521","support_contacts":["<EMAIL>"],"support_email":"<EMAIL>"}', 'off'),
(3194, 'wpmdb_migration_state', 'a:23:{s:12:"site_details";a:2:{s:5:"local";a:26:{s:12:"is_multisite";s:5:"false";s:8:"site_url";s:28:"https://numeridanse.local/wp";s:8:"home_url";s:25:"https://numeridanse.local";s:6:"prefix";s:3:"nd_";s:15:"uploads_baseurl";s:38:"https://numeridanse.local/app/uploads/";s:7:"uploads";a:7:{s:4:"path";s:68:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/uploads/2024/10";s:3:"url";s:45:"https://numeridanse.local/app/uploads/2024/10";s:6:"subdir";s:8:"/2024/10";s:7:"basedir";s:60:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/uploads";s:7:"baseurl";s:37:"https://numeridanse.local/app/uploads";s:5:"error";b:0;s:8:"relative";s:12:"/app/uploads";}s:11:"uploads_dir";s:26:"app/uploads/wp-migrate-db/";s:8:"subsites";a:0:{}s:13:"subsites_info";a:0:{}s:20:"is_subdomain_install";s:5:"false";s:26:"high_performance_transfers";b:0;s:29:"theoreticalTransferBottleneck";i:1847152;s:16:"firewall_plugins";a:0:{}s:8:"platform";N;s:11:"content_dir";s:52:"/Users/<USER>/www/numeridanse.tv/app/public/web/app";s:19:"transfer_bottleneck";i:1847152;s:16:"max_request_size";i:1048576;s:6:"php_os";s:6:"Darwin";s:7:"plugins";a:17:{s:69:"add-descendants-as-submenu-items/add-descendants-as-submenu-items.php";a:1:{i:0;a:4:{s:4:"name";s:32:"Add Descendants As Submenu Items";s:6:"active";b:0;s:4:"path";s:93:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/add-descendants-as-submenu-items";s:7:"version";s:5:"1.2.2";}}s:33:"acf-extended-pro/acf-extended.php";a:1:{i:0;a:4:{s:4:"name";s:36:"Advanced Custom Fields: Extended PRO";s:6:"active";b:1;s:4:"path";s:77:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/acf-extended-pro";s:7:"version";s:7:"*******";}}s:34:"advanced-custom-fields-pro/acf.php";a:1:{i:0;a:4:{s:4:"name";s:26:"Advanced Custom Fields PRO";s:6:"active";b:1;s:4:"path";s:87:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/advanced-custom-fields-pro";s:7:"version";s:5:"6.3.7";}}s:45:"enable-media-replace/enable-media-replace.php";a:1:{i:0;a:4:{s:4:"name";s:20:"Enable Media Replace";s:6:"active";b:1;s:4:"path";s:81:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/enable-media-replace";s:7:"version";s:5:"4.1.5";}}s:59:"intuitive-custom-post-order/intuitive-custom-post-order.php";a:1:{i:0;a:4:{s:4:"name";s:27:"Intuitive Custom Post Order";s:6:"active";b:0;s:4:"path";s:88:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/intuitive-custom-post-order";s:7:"version";s:7:"*******";}}s:53:"manage-privacy-options/baw-manage-privacy-options.php";a:1:{i:0;a:4:{s:4:"name";s:22:"Manage Privacy Options";s:6:"active";b:1;s:4:"path";s:83:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/manage-privacy-options";s:7:"version";s:3:"1.1";}}s:31:"wp-nested-pages/nestedpages.php";a:1:{i:0;a:4:{s:4:"name";s:12:"Nested Pages";s:6:"active";b:1;s:4:"path";s:76:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/wp-nested-pages";s:7:"version";s:5:"3.2.9";}}s:41:"password-protected/password-protected.php";a:1:{i:0;a:4:{s:4:"name";s:18:"Password Protected";s:6:"active";b:0;s:4:"path";s:79:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/password-protected";s:7:"version";s:5:"2.7.3";}}s:21:"safe-svg/safe-svg.php";a:1:{i:0;a:4:{s:4:"name";s:8:"Safe SVG";s:6:"active";b:1;s:4:"path";s:69:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/safe-svg";s:7:"version";s:5:"2.2.6";}}s:24:"wp-seopress/seopress.php";a:1:{i:0;a:4:{s:4:"name";s:8:"SEOPress";s:6:"active";b:1;s:4:"path";s:72:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/wp-seopress";s:7:"version";s:3:"8.2";}}s:32:"wp-seopress-pro/seopress-pro.php";a:1:{i:0;a:4:{s:4:"name";s:12:"SEOPress PRO";s:6:"active";b:1;s:4:"path";s:76:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/wp-seopress-pro";s:7:"version";s:5:"8.2.1";}}s:21:"spatie-ray/wp-ray.php";a:1:{i:0;a:4:{s:4:"name";s:10:"Spatie Ray";s:6:"active";b:1;s:4:"path";s:71:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/spatie-ray";s:7:"version";s:5:"1.7.6";}}s:33:"user-switching/user-switching.php";a:1:{i:0;a:4:{s:4:"name";s:14:"User Switching";s:6:"active";b:1;s:4:"path";s:75:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/user-switching";s:7:"version";s:5:"1.8.0";}}s:53:"admin-bar-user-switching/admin-bar-user-switching.php";a:1:{i:0;a:4:{s:4:"name";s:27:"User Switching in Admin Bar";s:6:"active";b:1;s:4:"path";s:85:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/admin-bar-user-switching";s:7:"version";s:3:"1.4";}}s:39:"wp-migrate-db-pro/wp-migrate-db-pro.php";a:1:{i:0;a:4:{s:4:"name";s:10:"WP Migrate";s:6:"active";b:1;s:4:"path";s:78:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/wp-migrate-db-pro";s:7:"version";s:5:"2.7.0";}}s:23:"wp-rocket/wp-rocket.php";a:1:{i:0;a:4:{s:4:"name";s:9:"WP Rocket";s:6:"active";b:0;s:4:"path";s:70:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/wp-rocket";s:7:"version";s:4:"3.16";}}s:23:"wp-sort-order/index.php";a:1:{i:0;a:4:{s:4:"name";s:13:"WP Sort Order";s:6:"active";b:0;s:4:"path";s:74:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins/wp-sort-order";s:7:"version";s:5:"1.3.3";}}}s:12:"plugins_path";s:60:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/plugins";s:9:"muplugins";a:2:{s:22:"bedrock-autoloader.php";a:1:{i:0;a:2:{s:4:"name";s:22:"bedrock-autoloader.php";s:4:"path";s:86:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/mu-plugins/bedrock-autoloader.php";}}s:25:"bedrock-disallow-indexing";a:1:{i:0;a:2:{s:4:"name";s:25:"bedrock-disallow-indexing";s:4:"path";s:89:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/mu-plugins/bedrock-disallow-indexing";}}}s:14:"muplugins_path";s:63:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/mu-plugins";s:6:"themes";a:1:{s:10:"lesanimals";a:1:{i:0;a:4:{s:4:"name";s:11:"Les Animals";s:6:"active";b:1;s:7:"version";s:5:"4.0.0";s:4:"path";s:70:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/themes/lesanimals";}}}s:11:"themes_path";s:60:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/themes/";s:6:"others";a:2:{s:9:"languages";a:1:{i:0;a:2:{s:4:"name";s:9:"languages";s:4:"path";s:62:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/languages";}}s:15:"maintenance.php";a:1:{i:0;a:2:{s:4:"name";s:15:"maintenance.php";s:4:"path";s:68:"/Users/<USER>/www/numeridanse.tv/app/public/web/app/maintenance.php";}}}s:4:"core";a:19:{s:9:"index.php";a:1:{i:0;a:2:{s:4:"name";s:9:"index.php";s:4:"path";s:62:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//index.php";}}s:11:"license.txt";a:1:{i:0;a:2:{s:4:"name";s:11:"license.txt";s:4:"path";s:64:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//license.txt";}}s:11:"readme.html";a:1:{i:0;a:2:{s:4:"name";s:11:"readme.html";s:4:"path";s:64:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//readme.html";}}s:15:"wp-activate.php";a:1:{i:0;a:2:{s:4:"name";s:15:"wp-activate.php";s:4:"path";s:68:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-activate.php";}}s:8:"wp-admin";a:1:{i:0;a:2:{s:4:"name";s:8:"wp-admin";s:4:"path";s:61:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-admin";}}s:18:"wp-blog-header.php";a:1:{i:0;a:2:{s:4:"name";s:18:"wp-blog-header.php";s:4:"path";s:71:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-blog-header.php";}}s:20:"wp-comments-post.php";a:1:{i:0;a:2:{s:4:"name";s:20:"wp-comments-post.php";s:4:"path";s:73:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-comments-post.php";}}s:20:"wp-config-sample.php";a:1:{i:0;a:2:{s:4:"name";s:20:"wp-config-sample.php";s:4:"path";s:73:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-config-sample.php";}}s:13:"wp-config.php";a:1:{i:0;a:2:{s:4:"name";s:13:"wp-config.php";s:4:"path";s:66:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-config.php";}}s:11:"wp-cron.php";a:1:{i:0;a:2:{s:4:"name";s:11:"wp-cron.php";s:4:"path";s:64:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-cron.php";}}s:11:"wp-includes";a:1:{i:0;a:2:{s:4:"name";s:11:"wp-includes";s:4:"path";s:64:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-includes";}}s:17:"wp-links-opml.php";a:1:{i:0;a:2:{s:4:"name";s:17:"wp-links-opml.php";s:4:"path";s:70:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-links-opml.php";}}s:11:"wp-load.php";a:1:{i:0;a:2:{s:4:"name";s:11:"wp-load.php";s:4:"path";s:64:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-load.php";}}s:12:"wp-login.php";a:1:{i:0;a:2:{s:4:"name";s:12:"wp-login.php";s:4:"path";s:65:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-login.php";}}s:11:"wp-mail.php";a:1:{i:0;a:2:{s:4:"name";s:11:"wp-mail.php";s:4:"path";s:64:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-mail.php";}}s:15:"wp-settings.php";a:1:{i:0;a:2:{s:4:"name";s:15:"wp-settings.php";s:4:"path";s:68:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-settings.php";}}s:13:"wp-signup.php";a:1:{i:0;a:2:{s:4:"name";s:13:"wp-signup.php";s:4:"path";s:66:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-signup.php";}}s:16:"wp-trackback.php";a:1:{i:0;a:2:{s:4:"name";s:16:"wp-trackback.php";s:4:"path";s:69:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//wp-trackback.php";}}s:10:"xmlrpc.php";a:1:{i:0;a:2:{s:4:"name";s:10:"xmlrpc.php";s:4:"path";s:63:"/Users/<USER>/www/numeridanse.tv/app/public/web/wp//xmlrpc.php";}}}}s:6:"remote";a:26:{s:12:"is_multisite";s:5:"false";s:8:"site_url";s:30:"https://dev.numeridanse.com/wp";s:8:"home_url";s:27:"https://dev.numeridanse.com";s:6:"prefix";s:3:"nd_";s:15:"uploads_baseurl";s:40:"https://dev.numeridanse.com/app/uploads/";s:7:"uploads";a:7:{s:4:"path";s:41:"/home/<USER>/dev/web/app/uploads/2024/10";s:3:"url";s:47:"https://dev.numeridanse.com/app/uploads/2024/10";s:6:"subdir";s:8:"/2024/10";s:7:"basedir";s:33:"/home/<USER>/dev/web/app/uploads";s:7:"baseurl";s:39:"https://dev.numeridanse.com/app/uploads";s:5:"error";b:0;s:8:"relative";s:12:"/app/uploads";}s:11:"uploads_dir";s:26:"app/uploads/wp-migrate-db/";s:8:"subsites";a:0:{}s:13:"subsites_info";a:0:{}s:20:"is_subdomain_install";s:5:"false";s:26:"high_performance_transfers";b:0;s:29:"theoreticalTransferBottleneck";i:133967728;s:16:"firewall_plugins";a:0:{}s:8:"platform";N;s:11:"content_dir";s:25:"/home/<USER>/dev/web/app";s:19:"transfer_bottleneck";i:133967728;s:16:"max_request_size";i:1048576;s:6:"php_os";s:5:"Linux";s:7:"plugins";a:16:{s:69:"add-descendants-as-submenu-items/add-descendants-as-submenu-items.php";a:1:{i:0;a:4:{s:4:"name";s:32:"Add Descendants As Submenu Items";s:6:"active";b:0;s:4:"path";s:66:"/home/<USER>/dev/web/app/plugins/add-descendants-as-submenu-items";s:7:"version";s:5:"1.2.2";}}s:33:"acf-extended-pro/acf-extended.php";a:1:{i:0;a:4:{s:4:"name";s:36:"Advanced Custom Fields: Extended PRO";s:6:"active";b:1;s:4:"path";s:50:"/home/<USER>/dev/web/app/plugins/acf-extended-pro";s:7:"version";s:7:"*******";}}s:34:"advanced-custom-fields-pro/acf.php";a:1:{i:0;a:4:{s:4:"name";s:26:"Advanced Custom Fields PRO";s:6:"active";b:1;s:4:"path";s:60:"/home/<USER>/dev/web/app/plugins/advanced-custom-fields-pro";s:7:"version";s:5:"6.3.8";}}s:45:"enable-media-replace/enable-media-replace.php";a:1:{i:0;a:4:{s:4:"name";s:20:"Enable Media Replace";s:6:"active";b:1;s:4:"path";s:54:"/home/<USER>/dev/web/app/plugins/enable-media-replace";s:7:"version";s:5:"4.1.5";}}s:53:"manage-privacy-options/baw-manage-privacy-options.php";a:1:{i:0;a:4:{s:4:"name";s:22:"Manage Privacy Options";s:6:"active";b:1;s:4:"path";s:56:"/home/<USER>/dev/web/app/plugins/manage-privacy-options";s:7:"version";s:3:"1.1";}}s:31:"wp-nested-pages/nestedpages.php";a:1:{i:0;a:4:{s:4:"name";s:12:"Nested Pages";s:6:"active";b:1;s:4:"path";s:49:"/home/<USER>/dev/web/app/plugins/wp-nested-pages";s:7:"version";s:5:"3.2.9";}}s:41:"password-protected/password-protected.php";a:1:{i:0;a:4:{s:4:"name";s:18:"Password Protected";s:6:"active";b:1;s:4:"path";s:52:"/home/<USER>/dev/web/app/plugins/password-protected";s:7:"version";s:5:"2.7.3";}}s:21:"safe-svg/safe-svg.php";a:1:{i:0;a:4:{s:4:"name";s:8:"Safe SVG";s:6:"active";b:1;s:4:"path";s:42:"/home/<USER>/dev/web/app/plugins/safe-svg";s:7:"version";s:5:"2.2.6";}}s:24:"wp-seopress/seopress.php";a:1:{i:0;a:4:{s:4:"name";s:8:"SEOPress";s:6:"active";b:1;s:4:"path";s:45:"/home/<USER>/dev/web/app/plugins/wp-seopress";s:7:"version";s:3:"8.2";}}s:32:"wp-seopress-pro/seopress-pro.php";a:1:{i:0;a:4:{s:4:"name";s:12:"SEOPress PRO";s:6:"active";b:1;s:4:"path";s:49:"/home/<USER>/dev/web/app/plugins/wp-seopress-pro";s:7:"version";s:5:"8.2.2";}}s:21:"spatie-ray/wp-ray.php";a:1:{i:0;a:4:{s:4:"name";s:10:"Spatie Ray";s:6:"active";b:1;s:4:"path";s:44:"/home/<USER>/dev/web/app/plugins/spatie-ray";s:7:"version";s:5:"1.7.6";}}s:33:"user-switching/user-switching.php";a:1:{i:0;a:4:{s:4:"name";s:14:"User Switching";s:6:"active";b:1;s:4:"path";s:48:"/home/<USER>/dev/web/app/plugins/user-switching";s:7:"version";s:5:"1.8.0";}}s:53:"admin-bar-user-switching/admin-bar-user-switching.php";a:1:{i:0;a:4:{s:4:"name";s:27:"User Switching in Admin Bar";s:6:"active";b:1;s:4:"path";s:58:"/home/<USER>/dev/web/app/plugins/admin-bar-user-switching";s:7:"version";s:3:"1.4";}}s:39:"wp-migrate-db-pro/wp-migrate-db-pro.php";a:1:{i:0;a:4:{s:4:"name";s:10:"WP Migrate";s:6:"active";b:1;s:4:"path";s:51:"/home/<USER>/dev/web/app/plugins/wp-migrate-db-pro";s:7:"version";s:5:"2.7.0";}}s:23:"wp-rocket/wp-rocket.php";a:1:{i:0;a:4:{s:4:"name";s:9:"WP Rocket";s:6:"active";b:0;s:4:"path";s:43:"/home/<USER>/dev/web/app/plugins/wp-rocket";s:7:"version";s:4:"3.16";}}s:23:"wp-sort-order/index.php";a:1:{i:0;a:4:{s:4:"name";s:13:"WP Sort Order";s:6:"active";b:1;s:4:"path";s:47:"/home/<USER>/dev/web/app/plugins/wp-sort-order";s:7:"version";s:5:"1.3.3";}}}s:12:"plugins_path";s:33:"/home/<USER>/dev/web/app/plugins";s:9:"muplugins";a:2:{s:22:"bedrock-autoloader.php";a:1:{i:0;a:2:{s:4:"name";s:22:"bedrock-autoloader.php";s:4:"path";s:59:"/home/<USER>/dev/web/app/mu-plugins/bedrock-autoloader.php";}}s:25:"bedrock-disallow-indexing";a:1:{i:0;a:2:{s:4:"name";s:25:"bedrock-disallow-indexing";s:4:"path";s:62:"/home/<USER>/dev/web/app/mu-plugins/bedrock-disallow-indexing";}}}s:14:"muplugins_path";s:36:"/home/<USER>/dev/web/app/mu-plugins";s:6:"themes";a:1:{s:10:"lesanimals";a:1:{i:0;a:4:{s:4:"name";s:11:"Les Animals";s:6:"active";b:1;s:7:"version";s:5:"4.0.0";s:4:"path";s:43:"/home/<USER>/dev/web/app/themes/lesanimals";}}}s:11:"themes_path";s:33:"/home/<USER>/dev/web/app/themes/";s:6:"others";a:3:{s:9:"languages";a:1:{i:0;a:2:{s:4:"name";s:9:"languages";s:4:"path";s:35:"/home/<USER>/dev/web/app/languages";}}s:15:"maintenance.php";a:1:{i:0;a:2:{s:4:"name";s:15:"maintenance.php";s:4:"path";s:41:"/home/<USER>/dev/web/app/maintenance.php";}}s:19:"upgrade-temp-backup";a:1:{i:0;a:2:{s:4:"name";s:19:"upgrade-temp-backup";s:4:"path";s:45:"/home/<USER>/dev/web/app/upgrade-temp-backup";}}}s:4:"core";a:19:{s:9:"index.php";a:1:{i:0;a:2:{s:4:"name";s:9:"index.php";s:4:"path";s:35:"/home/<USER>/dev/web/wp//index.php";}}s:11:"license.txt";a:1:{i:0;a:2:{s:4:"name";s:11:"license.txt";s:4:"path";s:37:"/home/<USER>/dev/web/wp//license.txt";}}s:11:"readme.html";a:1:{i:0;a:2:{s:4:"name";s:11:"readme.html";s:4:"path";s:37:"/home/<USER>/dev/web/wp//readme.html";}}s:15:"wp-activate.php";a:1:{i:0;a:2:{s:4:"name";s:15:"wp-activate.php";s:4:"path";s:41:"/home/<USER>/dev/web/wp//wp-activate.php";}}s:8:"wp-admin";a:1:{i:0;a:2:{s:4:"name";s:8:"wp-admin";s:4:"path";s:34:"/home/<USER>/dev/web/wp//wp-admin";}}s:18:"wp-blog-header.php";a:1:{i:0;a:2:{s:4:"name";s:18:"wp-blog-header.php";s:4:"path";s:44:"/home/<USER>/dev/web/wp//wp-blog-header.php";}}s:20:"wp-comments-post.php";a:1:{i:0;a:2:{s:4:"name";s:20:"wp-comments-post.php";s:4:"path";s:46:"/home/<USER>/dev/web/wp//wp-comments-post.php";}}s:20:"wp-config-sample.php";a:1:{i:0;a:2:{s:4:"name";s:20:"wp-config-sample.php";s:4:"path";s:46:"/home/<USER>/dev/web/wp//wp-config-sample.php";}}s:13:"wp-config.php";a:1:{i:0;a:2:{s:4:"name";s:13:"wp-config.php";s:4:"path";s:39:"/home/<USER>/dev/web/wp//wp-config.php";}}s:11:"wp-cron.php";a:1:{i:0;a:2:{s:4:"name";s:11:"wp-cron.php";s:4:"path";s:37:"/home/<USER>/dev/web/wp//wp-cron.php";}}s:11:"wp-includes";a:1:{i:0;a:2:{s:4:"name";s:11:"wp-includes";s:4:"path";s:37:"/home/<USER>/dev/web/wp//wp-includes";}}s:17:"wp-links-opml.php";a:1:{i:0;a:2:{s:4:"name";s:17:"wp-links-opml.php";s:4:"path";s:43:"/home/<USER>/dev/web/wp//wp-links-opml.php";}}s:11:"wp-load.php";a:1:{i:0;a:2:{s:4:"name";s:11:"wp-load.php";s:4:"path";s:37:"/home/<USER>/dev/web/wp//wp-load.php";}}s:12:"wp-login.php";a:1:{i:0;a:2:{s:4:"name";s:12:"wp-login.php";s:4:"path";s:38:"/home/<USER>/dev/web/wp//wp-login.php";}}s:11:"wp-mail.php";a:1:{i:0;a:2:{s:4:"name";s:11:"wp-mail.php";s:4:"path";s:37:"/home/<USER>/dev/web/wp//wp-mail.php";}}s:15:"wp-settings.php";a:1:{i:0;a:2:{s:4:"name";s:15:"wp-settings.php";s:4:"path";s:41:"/home/<USER>/dev/web/wp//wp-settings.php";}}s:13:"wp-signup.php";a:1:{i:0;a:2:{s:4:"name";s:13:"wp-signup.php";s:4:"path";s:39:"/home/<USER>/dev/web/wp//wp-signup.php";}}s:16:"wp-trackback.php";a:1:{i:0;a:2:{s:4:"name";s:16:"wp-trackback.php";s:4:"path";s:42:"/home/<USER>/dev/web/wp//wp-trackback.php";}}s:10:"xmlrpc.php";a:1:{i:0;a:2:{s:4:"name";s:10:"xmlrpc.php";s:4:"path";s:36:"/home/<USER>/dev/web/wp//xmlrpc.php";}}}}}s:6:"intent";s:4:"pull";s:3:"url";s:30:"https://dev.numeridanse.com/wp";s:3:"key";s:40:"YjN/0F24tAdyMY+zROM0tDFAzEjkkyOizrD+uiCo";s:11:"temp_prefix";s:5:"_mig_";s:9:"form_data";s:4729:"{"current_migration":{"connected":true,"intent":"pull","tables_option":"all","tables_selected":["nd_commentmeta","nd_comments","nd_links","nd_options","nd_postmeta","nd_posts","nd_pp_activity_logs","nd_seopress_content_analysis","nd_seopress_seo_issues","nd_seopress_significant_keywords","nd_term_relationships","nd_term_taxonomy","nd_termmeta","nd_terms","nd_usermeta","nd_users"],"backup_option":"backup_only_with_prefix","backup_tables_selected":["nd_commentmeta","nd_comments","nd_links","nd_options","nd_postmeta","nd_posts","nd_seopress_content_analysis","nd_seopress_significant_keywords","nd_term_relationships","nd_term_taxonomy","nd_termmeta","nd_terms","nd_usermeta","nd_users"],"post_types_option":"all","post_types_selected":[],"advanced_options_selected":["replace_guids","exclude_transients","exclude_spam","keep_active_plugins","exclude_post_revisions"],"profile_name":"pulldev","selected_existing_profile":null,"profile_type":null,"status":"","stages":["media_files"],"current_stage":"","stages_complete":[],"running":false,"migration_enabled":false,"migration_id":"342c270c-3797-4f23-a8cb-725c2e3e108b","source_prefix":"nd_","destination_prefix":"nd_","preview":false,"selectedComboOption":"preview","twoMultisites":false,"localSource":false,"databaseEnabled":true,"currentPayloadSize":0,"currentMaxPayloadSize":null,"fileTransferRequests":0,"payloadSizeHistory":[],"fileTransferStats":[],"forceHighPerformanceTransfers":true,"fseDumpFilename":null,"highPerformanceTransfersStatus":false},"connection_info":"https:\\/\\/dev.numeridanse.com\\/wp\\nYjN\\/0F24tAdyMY+zROM0tDFAzEjkkyOizrD+uiCo","search_replace":{"standard_search_replace":{"domain":{"search":"\\/\\/dev.numeridanse.com","replace":"\\/\\/numeridanse.local","enabled":true},"path":{"search":"\\/home\\/<USER>\\/dev\\/web","replace":"\\/Users\\/<USER>\\/www\\/numeridanse.tv\\/app\\/public\\/web","enabled":true}},"standard_options_enabled":["domain","path"],"standard_search_visible":true,"custom_search_replace":[{"replace_old":"","replace_new":"","focus":false,"regex":false,"isValidRegex":null,"replace_old_placeholder":null,"replace_new_placeholder":null,"id":"3776263b-a59f-4875-adb6-0518ead5136a"}],"custom_search_domain_locked":false},"media_files":{"enabled":true,"option":"new_subsequent","available":true,"is_licensed":true,"message":"<b>Addon Missing<\\/b> - The Media Files addon is inactive on the <strong>remote site<\\/strong>. Please install and activate it to enable media file migration.","excludes":".DS_Store\\n*.log\\n*backup*\\/\\n*cache*\\/","last_migration":"2024-10-07T19:26:25+00:00","date":"2024-10-03T15:12:42.245Z"},"theme_plugin_files":{"available":true,"is_licensed":true,"message":"<b>Addon Missing<\\/b> - The Theme & Plugin Files addon is inactive on the <strong>remote site<\\/strong>. Please install and activate it to enable Theme & Plugin Files migration.","theme_files":{"enabled":false},"themes_option":"all","themes_selected":[],"themes_excluded":[],"themes_excludes":".DS_Store\\n.git\\nnode_modules","plugin_files":{"enabled":false},"plugins_option":"all","plugins_selected":[],"plugins_excluded":[],"plugins_excludes":".DS_Store\\n.git\\nnode_modules","muplugin_files":{"enabled":false},"muplugins_option":"selected","muplugins_selected":[],"muplugins_excludes":".DS_Store\\n.git\\nnode_modules","other_files":{"enabled":false},"others_option":"selected","others_selected":[],"others_excludes":".DS_Store\\n.git\\nnode_modules","core_files":{"enabled":false},"core_option":"all","core_selected":[],"core_excludes":".DS_Store\\n.git\\nnode_modules","state":{"status":""}},"multisite_tools":{"enabled":false,"available":true,"is_licensed":true,"selected_subsite":0,"destination_subsite":0,"new_prefix":"","message":""},"action":"pull","select_tables":["nd_commentmeta","nd_comments","nd_links","nd_options","nd_postmeta","nd_posts","nd_pp_activity_logs","nd_seopress_content_analysis","nd_seopress_seo_issues","nd_seopress_significant_keywords","nd_term_relationships","nd_term_taxonomy","nd_termmeta","nd_terms","nd_usermeta","nd_users"],"table_migrate_option":"all","create_backup":1,"backup_option":"backup_only_with_prefix","select_backup":["nd_commentmeta","nd_comments","nd_links","nd_options","nd_postmeta","nd_posts","nd_seopress_content_analysis","nd_seopress_significant_keywords","nd_term_relationships","nd_term_taxonomy","nd_termmeta","nd_terms","nd_usermeta","nd_users"],"select_post_types":{"1":"acf-field","2":"attachment","3":"collection","4":"nav_menu_item","5":"page","6":"playlist","7":"profile","8":"publication"},"exclude_post_revisions":"1","replace_guids":"1","compatibility_older_mysql":"0","exclude_transients":"1","exclude_spam":"1","keep_active_plugins":"1","gzip_file":"0","exclude_post_types":1,"new_prefix":"nd_"}";s:5:"stage";s:6:"backup";s:10:"new_prefix";s:3:"nd_";s:13:"dump_filename";s:41:"numeridansetv-backup-20241008085433-tsqkf";s:8:"dump_url";s:97:"https://numeridanse.local/app/uploads/wp-migrate-db/numeridansetv-backup-20241008085433-qutkn.sql";s:10:"db_version";s:6:"8.0.16";s:8:"site_url";s:28:"https://numeridanse.local/wp";s:18:"find_replace_pairs";a:4:{s:5:"regex";a:0:{}s:14:"case_sensitive";a:0:{}s:11:"replace_old";a:2:{i:1;s:21:"//dev.numeridanse.com";i:2;s:21:"/home/<USER>/dev/web";}s:11:"replace_new";a:2:{i:1;s:19:"//numeridanse.local";i:2;s:48:"/Users/<USER>/www/numeridanse.tv/app/public/web";}}s:13:"source_prefix";s:3:"nd_";s:18:"destination_prefix";s:3:"nd_";s:4:"gzip";i:1;s:10:"bottleneck";d:1048576;s:6:"prefix";s:3:"nd_";s:5:"table";s:10:"nd_options";s:10:"last_table";s:1:"0";s:11:"current_row";i:-1;s:12:"primary_keys";s:0:"";s:16:"dumpfile_created";b:1;}', 'off'),
(3195, 'wpmdb_migration_options', 'a:22:{s:17:"current_migration";a:34:{s:9:"connected";b:1;s:6:"intent";s:4:"pull";s:13:"tables_option";s:3:"all";s:15:"tables_selected";a:16:{i:0;s:14:"nd_commentmeta";i:1;s:11:"nd_comments";i:2;s:8:"nd_links";i:3;s:10:"nd_options";i:4;s:11:"nd_postmeta";i:5;s:8:"nd_posts";i:6;s:19:"nd_pp_activity_logs";i:7;s:28:"nd_seopress_content_analysis";i:8;s:22:"nd_seopress_seo_issues";i:9;s:32:"nd_seopress_significant_keywords";i:10;s:21:"nd_term_relationships";i:11;s:16:"nd_term_taxonomy";i:12;s:11:"nd_termmeta";i:13;s:8:"nd_terms";i:14;s:11:"nd_usermeta";i:15;s:8:"nd_users";}s:13:"backup_option";s:23:"backup_only_with_prefix";s:22:"backup_tables_selected";a:14:{i:0;s:14:"nd_commentmeta";i:1;s:11:"nd_comments";i:2;s:8:"nd_links";i:3;s:10:"nd_options";i:4;s:11:"nd_postmeta";i:5;s:8:"nd_posts";i:6;s:28:"nd_seopress_content_analysis";i:7;s:32:"nd_seopress_significant_keywords";i:8;s:21:"nd_term_relationships";i:9;s:16:"nd_term_taxonomy";i:10;s:11:"nd_termmeta";i:11;s:8:"nd_terms";i:12;s:11:"nd_usermeta";i:13;s:8:"nd_users";}s:17:"post_types_option";s:3:"all";s:19:"post_types_selected";a:0:{}s:25:"advanced_options_selected";a:5:{i:0;s:13:"replace_guids";i:1;s:18:"exclude_transients";i:2;s:12:"exclude_spam";i:3;s:19:"keep_active_plugins";i:4;s:22:"exclude_post_revisions";}s:12:"profile_name";s:7:"pulldev";s:25:"selected_existing_profile";N;s:12:"profile_type";N;s:6:"status";s:0:"";s:6:"stages";a:1:{i:0;s:11:"media_files";}s:13:"current_stage";s:0:"";s:15:"stages_complete";a:0:{}s:7:"running";b:0;s:17:"migration_enabled";b:0;s:12:"migration_id";s:36:"342c270c-3797-4f23-a8cb-725c2e3e108b";s:13:"source_prefix";s:3:"nd_";s:18:"destination_prefix";s:3:"nd_";s:7:"preview";b:0;s:19:"selectedComboOption";s:7:"preview";s:13:"twoMultisites";b:0;s:11:"localSource";b:0;s:15:"databaseEnabled";b:1;s:18:"currentPayloadSize";i:0;s:21:"currentMaxPayloadSize";N;s:20:"fileTransferRequests";i:0;s:18:"payloadSizeHistory";a:0:{}s:17:"fileTransferStats";a:0:{}s:29:"forceHighPerformanceTransfers";b:1;s:15:"fseDumpFilename";N;s:30:"highPerformanceTransfersStatus";b:0;}s:15:"connection_info";s:0:"";s:14:"search_replace";a:5:{s:23:"standard_search_replace";a:2:{s:6:"domain";a:3:{s:6:"search";s:21:"//dev.numeridanse.com";s:7:"replace";s:19:"//numeridanse.local";s:7:"enabled";b:1;}s:4:"path";a:3:{s:6:"search";s:21:"/home/<USER>/dev/web";s:7:"replace";s:48:"/Users/<USER>/www/numeridanse.tv/app/public/web";s:7:"enabled";b:1;}}s:24:"standard_options_enabled";a:2:{i:0;s:6:"domain";i:1;s:4:"path";}s:23:"standard_search_visible";b:1;s:21:"custom_search_replace";a:1:{i:0;a:8:{s:11:"replace_old";s:0:"";s:11:"replace_new";s:0:"";s:5:"focus";b:0;s:5:"regex";b:0;s:12:"isValidRegex";N;s:23:"replace_old_placeholder";N;s:23:"replace_new_placeholder";N;s:2:"id";s:36:"3776263b-a59f-4875-adb6-0518ead5136a";}}s:27:"custom_search_domain_locked";b:0;}s:11:"media_files";a:8:{s:7:"enabled";b:1;s:6:"option";s:14:"new_subsequent";s:9:"available";b:1;s:11:"is_licensed";b:1;s:7:"message";s:156:"<b>Addon Missing</b> - The Media Files addon is inactive on the <strong>remote site</strong>. Please install and activate it to enable media file migration.";s:8:"excludes";s:34:".DS_Store\n*.log\n*backup*/\n*cache*/";s:14:"last_migration";s:25:"2024-10-07T19:26:25+00:00";s:4:"date";s:24:"2024-10-03T15:12:42.245Z";}s:18:"theme_plugin_files";a:26:{s:9:"available";b:1;s:11:"is_licensed";b:1;s:7:"message";s:175:"<b>Addon Missing</b> - The Theme & Plugin Files addon is inactive on the <strong>remote site</strong>. Please install and activate it to enable Theme & Plugin Files migration.";s:11:"theme_files";a:1:{s:7:"enabled";b:0;}s:13:"themes_option";s:3:"all";s:15:"themes_selected";a:0:{}s:15:"themes_excluded";a:0:{}s:15:"themes_excludes";s:27:".DS_Store\n.git\nnode_modules";s:12:"plugin_files";a:1:{s:7:"enabled";b:0;}s:14:"plugins_option";s:3:"all";s:16:"plugins_selected";a:0:{}s:16:"plugins_excluded";a:0:{}s:16:"plugins_excludes";s:27:".DS_Store\n.git\nnode_modules";s:14:"muplugin_files";a:1:{s:7:"enabled";b:0;}s:16:"muplugins_option";s:8:"selected";s:18:"muplugins_selected";a:0:{}s:18:"muplugins_excludes";s:27:".DS_Store\n.git\nnode_modules";s:11:"other_files";a:1:{s:7:"enabled";b:0;}s:13:"others_option";s:8:"selected";s:15:"others_selected";a:0:{}s:15:"others_excludes";s:27:".DS_Store\n.git\nnode_modules";s:10:"core_files";a:1:{s:7:"enabled";b:0;}s:11:"core_option";s:3:"all";s:13:"core_selected";a:0:{}s:13:"core_excludes";s:27:".DS_Store\n.git\nnode_modules";s:5:"state";a:1:{s:6:"status";s:0:"";}}s:15:"multisite_tools";a:7:{s:7:"enabled";b:0;s:9:"available";b:1;s:11:"is_licensed";b:1;s:16:"selected_subsite";i:0;s:19:"destination_subsite";i:0;s:10:"new_prefix";s:0:"";s:7:"message";s:0:"";}s:6:"action";s:4:"pull";s:13:"select_tables";a:16:{i:0;s:14:"nd_commentmeta";i:1;s:11:"nd_comments";i:2;s:8:"nd_links";i:3;s:10:"nd_options";i:4;s:11:"nd_postmeta";i:5;s:8:"nd_posts";i:6;s:19:"nd_pp_activity_logs";i:7;s:28:"nd_seopress_content_analysis";i:8;s:22:"nd_seopress_seo_issues";i:9;s:32:"nd_seopress_significant_keywords";i:10;s:21:"nd_term_relationships";i:11;s:16:"nd_term_taxonomy";i:12;s:11:"nd_termmeta";i:13;s:8:"nd_terms";i:14;s:11:"nd_usermeta";i:15;s:8:"nd_users";}s:20:"table_migrate_option";s:3:"all";s:13:"create_backup";i:1;s:13:"backup_option";s:23:"backup_only_with_prefix";s:13:"select_backup";a:14:{i:0;s:14:"nd_commentmeta";i:1;s:11:"nd_comments";i:2;s:8:"nd_links";i:3;s:10:"nd_options";i:4;s:11:"nd_postmeta";i:5;s:8:"nd_posts";i:6;s:28:"nd_seopress_content_analysis";i:7;s:32:"nd_seopress_significant_keywords";i:8;s:21:"nd_term_relationships";i:9;s:16:"nd_term_taxonomy";i:10;s:11:"nd_termmeta";i:11;s:8:"nd_terms";i:12;s:11:"nd_usermeta";i:13;s:8:"nd_users";}s:17:"select_post_types";a:8:{i:1;s:9:"acf-field";i:2;s:10:"attachment";i:3;s:10:"collection";i:4;s:13:"nav_menu_item";i:5;s:4:"page";i:6;s:8:"playlist";i:7;s:7:"profile";i:8;s:11:"publication";}s:22:"exclude_post_revisions";s:1:"1";s:13:"replace_guids";s:1:"1";s:25:"compatibility_older_mysql";s:1:"0";s:18:"exclude_transients";s:1:"1";s:12:"exclude_spam";s:1:"1";s:19:"keep_active_plugins";s:1:"1";s:9:"gzip_file";s:1:"0";s:18:"exclude_post_types";i:1;s:10:"new_prefix";s:3:"nd_";}', 'off') ;

#
# End of data contents of table `nd_options`
# --------------------------------------------------------



#
# Delete any existing table `nd_postmeta`
#

DROP TABLE IF EXISTS `nd_postmeta`;


#
# Table structure of table `nd_postmeta`
#

CREATE TABLE `nd_postmeta` (
  `meta_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `post_id` bigint(20) unsigned NOT NULL DEFAULT '0',
  `meta_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`meta_id`),
  KEY `post_id` (`post_id`),
  KEY `meta_key` (`meta_key`(191))
) ENGINE=InnoDB AUTO_INCREMENT=1102 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_postmeta`
#
INSERT INTO `nd_postmeta` ( `meta_id`, `post_id`, `meta_key`, `meta_value`) VALUES
(1, 2, '_wp_page_template', 'default'),
(2, 3, '_wp_page_template', 'default'),
(7, 7, '_wp_attached_file', '2024/07/Favicon.png'),
(8, 7, '_wp_attachment_metadata', 'a:6:{s:5:"width";i:512;s:6:"height";i:512;s:4:"file";s:19:"2024/07/Favicon.png";s:8:"filesize";i:18808;s:5:"sizes";a:1:{s:9:"thumbnail";a:5:{s:4:"file";s:19:"Favicon-256x256.png";s:5:"width";i:256;s:6:"height";i:256;s:9:"mime-type";s:9:"image/png";s:8:"filesize";i:6749;}}s:10:"image_meta";a:12:{s:8:"aperture";s:1:"0";s:6:"credit";s:0:"";s:6:"camera";s:0:"";s:7:"caption";s:0:"";s:17:"created_timestamp";s:1:"0";s:9:"copyright";s:0:"";s:12:"focal_length";s:1:"0";s:3:"iso";s:1:"0";s:13:"shutter_speed";s:1:"0";s:5:"title";s:0:"";s:11:"orientation";s:1:"0";s:8:"keywords";a:0:{}}}'),
(9, 8, '_wp_attached_file', '2024/07/cropped-Favicon.png'),
(10, 8, '_wp_attachment_context', 'site-icon'),
(11, 8, '_wp_attachment_metadata', 'a:6:{s:5:"width";i:512;s:6:"height";i:512;s:4:"file";s:27:"2024/07/cropped-Favicon.png";s:8:"filesize";i:15129;s:5:"sizes";a:5:{s:9:"thumbnail";a:5:{s:4:"file";s:27:"cropped-Favicon-256x256.png";s:5:"width";i:256;s:6:"height";i:256;s:9:"mime-type";s:9:"image/png";s:8:"filesize";i:6749;}s:13:"site_icon-270";a:5:{s:4:"file";s:27:"cropped-Favicon-270x270.png";s:5:"width";i:270;s:6:"height";i:270;s:9:"mime-type";s:9:"image/png";s:8:"filesize";i:7275;}s:13:"site_icon-192";a:5:{s:4:"file";s:27:"cropped-Favicon-192x192.png";s:5:"width";i:192;s:6:"height";i:192;s:9:"mime-type";s:9:"image/png";s:8:"filesize";i:5051;}s:13:"site_icon-180";a:5:{s:4:"file";s:27:"cropped-Favicon-180x180.png";s:5:"width";i:180;s:6:"height";i:180;s:9:"mime-type";s:9:"image/png";s:8:"filesize";i:4607;}s:12:"site_icon-32";a:5:{s:4:"file";s:25:"cropped-Favicon-32x32.png";s:5:"width";i:32;s:6:"height";i:32;s:9:"mime-type";s:9:"image/png";s:8:"filesize";i:806;}}s:10:"image_meta";a:12:{s:8:"aperture";s:1:"0";s:6:"credit";s:0:"";s:6:"camera";s:0:"";s:7:"caption";s:0:"";s:17:"created_timestamp";s:1:"0";s:9:"copyright";s:0:"";s:12:"focal_length";s:1:"0";s:3:"iso";s:1:"0";s:13:"shutter_speed";s:1:"0";s:5:"title";s:0:"";s:11:"orientation";s:1:"0";s:8:"keywords";a:0:{}}}'),
(39, 15, '_menu_item_type', 'custom'),
(40, 15, '_menu_item_menu_item_parent', '0'),
(41, 15, '_menu_item_object_id', '15'),
(42, 15, '_menu_item_object', 'custom'),
(43, 15, '_menu_item_target', ''),
(44, 15, '_menu_item_classes', 'a:1:{i:0;s:0:"";}'),
(45, 15, '_menu_item_xfn', ''),
(46, 15, '_menu_item_url', 'https://numeridanse.local/'),
(47, 15, '_menu_item_orphaned', '1721299036'),
(48, 16, '_menu_item_type', 'post_type'),
(49, 16, '_menu_item_menu_item_parent', '0'),
(50, 16, '_menu_item_object_id', '2'),
(51, 16, '_menu_item_object', 'page'),
(52, 16, '_menu_item_target', ''),
(53, 16, '_menu_item_classes', 'a:1:{i:0;s:0:"";}'),
(54, 16, '_menu_item_xfn', ''),
(55, 16, '_menu_item_url', ''),
(56, 16, '_menu_item_orphaned', '1721299036'),
(57, 2, '_edit_lock', '1727969737:1'),
(58, 2, '_edit_last', '1'),
(59, 2, 'acf', 'a:42:{s:13:"flexible_main";a:8:{i:0;s:24:"slider-publication-cards";i:1;s:12:"push-content";i:2;s:12:"push-content";i:3;s:24:"slider-publication-cards";i:4;s:24:"slider-publication-cards";i:5;s:12:"push-content";i:6;s:16:"push-publication";i:7;s:16:"push-publication";}s:14:"_flexible_main";s:19:"field_flexible_main";s:36:"flexible_main_0_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_0_acfe_flexible_toggle";s:44:"field_layout_slider-publication-cards_toggle";s:20:"flexible_main_0_type";s:4:"news";s:21:"_flexible_main_0_type";s:56:"field_clone_slider-publication-cards_field_66b1f96081f19";s:36:"flexible_main_1_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_1_acfe_flexible_toggle";s:32:"field_layout_push-content_toggle";s:23:"flexible_main_1_display";s:20:"devenez-contributeur";s:24:"_flexible_main_1_display";s:44:"field_clone_push-content_field_66d6eca69ad4b";s:36:"flexible_main_3_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_3_acfe_flexible_toggle";s:44:"field_layout_slider-publication-cards_toggle";s:20:"flexible_main_3_type";s:5:"honor";s:21:"_flexible_main_3_type";s:56:"field_clone_slider-publication-cards_field_66b1f96081f19";s:31:"flexible_main_3_publication_ids";a:5:{i:0;s:3:"404";i:1;s:3:"406";i:2;s:3:"224";i:3;s:3:"411";i:4;s:3:"407";}s:32:"_flexible_main_3_publication_ids";s:56:"field_clone_slider-publication-cards_field_66b4a0c9ea938";s:36:"flexible_main_2_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_2_acfe_flexible_toggle";s:32:"field_layout_push-content_toggle";s:23:"flexible_main_2_display";s:82:"voluptate-proident-ad-officia-ullamco-laboris-nulla-nisi-dolore-incididunt-ullamco";s:24:"_flexible_main_2_display";s:44:"field_clone_push-content_field_66d6eca69ad4b";s:36:"flexible_main_4_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_4_acfe_flexible_toggle";s:44:"field_layout_slider-publication-cards_toggle";s:20:"flexible_main_4_type";s:6:"manual";s:21:"_flexible_main_4_type";s:56:"field_clone_slider-publication-cards_field_66b1f96081f19";s:31:"flexible_main_4_publication_ids";a:7:{i:0;s:3:"404";i:1;s:3:"395";i:2;s:3:"405";i:3;s:3:"406";i:4;s:3:"224";i:5;s:3:"411";i:6;s:3:"407";}s:32:"_flexible_main_4_publication_ids";s:56:"field_clone_slider-publication-cards_field_66b4a0c9ea938";s:36:"flexible_main_6_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_6_acfe_flexible_toggle";s:36:"field_layout_push-publication_toggle";s:22:"flexible_main_6_pub_id";i:396;s:23:"_flexible_main_6_pub_id";s:48:"field_clone_push-publication_field_66d86c070638c";s:22:"flexible_main_6_img_id";i:492;s:23:"_flexible_main_6_img_id";s:48:"field_clone_push-publication_field_66d86d450638d";s:36:"flexible_main_5_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_5_acfe_flexible_toggle";s:32:"field_layout_push-content_toggle";s:23:"flexible_main_5_display";s:82:"voluptate-proident-ad-officia-ullamco-laboris-nulla-nisi-dolore-incididunt-ullamco";s:24:"_flexible_main_5_display";s:44:"field_clone_push-content_field_66d6eca69ad4b";s:36:"flexible_main_7_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_7_acfe_flexible_toggle";s:36:"field_layout_push-publication_toggle";s:22:"flexible_main_7_pub_id";i:395;s:23:"_flexible_main_7_pub_id";s:48:"field_clone_push-publication_field_66d86c070638c";s:22:"flexible_main_7_img_id";i:486;s:23:"_flexible_main_7_img_id";s:48:"field_clone_push-publication_field_66d86d450638d";}'),
(60, 2, '_seopress_redirections_type', '301'),
(61, 2, '_seopress_redirections_logged_status', 'both'),
(62, 2, '_seopress_analysis_target_kw', ''),
(64, 3, '_edit_lock', '1721310964:1'),
(65, 3, '_edit_last', '1'),
(66, 3, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(67, 3, '_seopress_redirections_type', '301'),
(68, 3, '_seopress_redirections_logged_status', 'both'),
(69, 3, '_seopress_analysis_target_kw', ''),
(114, 33, '_menu_item_type', 'custom'),
(115, 33, '_menu_item_menu_item_parent', '0'),
(116, 33, '_menu_item_object_id', '33'),
(117, 33, '_menu_item_object', 'custom'),
(118, 33, '_menu_item_target', ''),
(119, 33, '_menu_item_classes', 'a:1:{i:0;s:0:"";}'),
(120, 33, '_menu_item_xfn', ''),
(121, 33, '_menu_item_url', 'https://numeridanse.local/#moncompte'),
(141, 36, '_menu_item_type', 'custom'),
(142, 36, '_menu_item_menu_item_parent', '0'),
(143, 36, '_menu_item_object_id', '36'),
(144, 36, '_menu_item_object', 'custom'),
(145, 36, '_menu_item_target', ''),
(146, 36, '_menu_item_classes', 'a:1:{i:0;s:0:"";}'),
(147, 36, '_menu_item_xfn', ''),
(148, 36, '_menu_item_url', 'https://data-danse.numeridanse.tv/'),
(150, 37, '_menu_item_type', 'post_type'),
(151, 37, '_menu_item_menu_item_parent', '0'),
(152, 37, '_menu_item_object_id', '3'),
(153, 37, '_menu_item_object', 'page'),
(154, 37, '_menu_item_target', ''),
(155, 37, '_menu_item_classes', 'a:1:{i:0;s:0:"";}'),
(156, 37, '_menu_item_xfn', ''),
(157, 37, '_menu_item_url', ''),
(225, 141, '_edit_last', '1'),
(226, 141, '_wp_page_template', 'default'),
(227, 141, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(228, 141, '_seopress_redirections_type', '301'),
(229, 141, '_seopress_redirections_logged_status', 'both'),
(230, 141, '_seopress_analysis_target_kw', ''),
(232, 141, '_edit_lock', '1722331405:1'),
(233, 145, '_edit_last', '1'),
(234, 145, '_wp_page_template', 'template-redirect.php'),
(235, 145, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(236, 145, '_seopress_redirections_type', '301'),
(237, 145, '_seopress_redirections_logged_status', 'both'),
(238, 145, '_seopress_analysis_target_kw', ''),
(240, 145, '_edit_lock', '1722331537:1'),
(241, 149, '_edit_last', '1'),
(242, 149, '_edit_lock', '1722331545:1'),
(243, 149, '_wp_page_template', 'default'),
(244, 149, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(245, 149, '_seopress_redirections_type', '301'),
(246, 149, '_seopress_redirections_logged_status', 'both'),
(247, 149, '_seopress_analysis_target_kw', ''),
(249, 150, '_edit_last', '1'),
(250, 150, '_edit_lock', '1722331547:1'),
(251, 150, '_wp_page_template', 'default'),
(252, 150, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(253, 150, '_seopress_redirections_type', '301'),
(254, 150, '_seopress_redirections_logged_status', 'both'),
(255, 150, '_seopress_analysis_target_kw', ''),
(257, 157, '_edit_last', '1'),
(258, 157, '_edit_lock', '1722331606:1'),
(259, 157, '_wp_page_template', 'template-redirect.php'),
(260, 157, '_seopress_redirections_type', '301'),
(261, 157, '_seopress_redirections_logged_status', 'both'),
(262, 157, '_seopress_analysis_target_kw', ''),
(263, 161, '_edit_last', '1'),
(264, 161, '_wp_page_template', 'default'),
(265, 161, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(266, 161, '_seopress_redirections_type', '301'),
(267, 161, '_seopress_redirections_logged_status', 'both') ;
INSERT INTO `nd_postmeta` ( `meta_id`, `post_id`, `meta_key`, `meta_value`) VALUES
(268, 161, '_seopress_analysis_target_kw', ''),
(270, 161, '_edit_lock', '1722331634:1'),
(271, 165, '_edit_last', '1'),
(272, 165, '_edit_lock', '1722331647:1'),
(273, 165, '_wp_page_template', 'default'),
(274, 165, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(275, 165, '_seopress_redirections_type', '301'),
(276, 165, '_seopress_redirections_logged_status', 'both'),
(277, 165, '_seopress_analysis_target_kw', ''),
(279, 169, '_edit_last', '1'),
(280, 169, '_edit_lock', '1722331659:1'),
(281, 169, '_wp_page_template', 'default'),
(282, 169, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(283, 169, '_seopress_redirections_type', '301'),
(284, 169, '_seopress_redirections_logged_status', 'both'),
(285, 169, '_seopress_analysis_target_kw', ''),
(287, 173, '_edit_last', '1'),
(288, 173, '_edit_lock', '1722331704:1'),
(289, 173, '_wp_page_template', 'default'),
(290, 173, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(291, 173, '_seopress_redirections_type', '301'),
(292, 173, '_seopress_redirections_logged_status', 'both'),
(293, 173, '_seopress_analysis_target_kw', ''),
(295, 177, '_edit_last', '1'),
(296, 177, '_edit_lock', '1722331926:1'),
(297, 177, '_wp_page_template', 'template-redirect.php'),
(298, 177, '_seopress_redirections_type', '301'),
(299, 177, '_seopress_redirections_logged_status', 'both'),
(300, 177, '_seopress_analysis_target_kw', ''),
(301, 181, '_edit_last', '1'),
(302, 181, '_wp_page_template', 'default'),
(303, 181, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(304, 181, '_seopress_redirections_type', '301'),
(305, 181, '_seopress_redirections_logged_status', 'both'),
(306, 181, '_seopress_analysis_target_kw', ''),
(308, 181, '_edit_lock', '1722331940:1'),
(309, 185, '_edit_last', '1'),
(310, 185, '_edit_lock', '1722332054:1'),
(311, 185, '_wp_page_template', 'default'),
(312, 185, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(313, 185, '_seopress_redirections_type', '301'),
(314, 185, '_seopress_redirections_logged_status', 'both'),
(315, 185, '_seopress_analysis_target_kw', ''),
(317, 189, '_edit_last', '1'),
(318, 189, '_edit_lock', '1722332065:1'),
(319, 189, '_wp_page_template', 'default'),
(320, 189, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(321, 189, '_seopress_redirections_type', '301'),
(322, 189, '_seopress_redirections_logged_status', 'both'),
(323, 189, '_seopress_analysis_target_kw', ''),
(325, 193, '_edit_last', '1'),
(326, 193, '_edit_lock', '1725447552:1'),
(327, 193, '_wp_page_template', 'default'),
(328, 193, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(329, 193, '_seopress_redirections_type', '301'),
(330, 193, '_seopress_redirections_logged_status', 'both'),
(331, 193, '_seopress_analysis_target_kw', ''),
(333, 197, '_edit_last', '1'),
(334, 197, '_wp_page_template', 'default'),
(335, 197, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(336, 197, '_seopress_redirections_type', '301'),
(337, 197, '_seopress_redirections_logged_status', 'both'),
(338, 197, '_seopress_analysis_target_kw', ''),
(340, 197, '_edit_lock', '1722332134:1'),
(341, 198, '_edit_last', '1'),
(342, 198, '_wp_page_template', 'default'),
(343, 198, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(344, 198, '_seopress_redirections_type', '301'),
(345, 198, '_seopress_redirections_logged_status', 'both'),
(346, 198, '_seopress_analysis_target_kw', ''),
(348, 198, '_edit_lock', '1722332141:1'),
(349, 205, '_menu_item_type', 'post_type'),
(350, 205, '_menu_item_menu_item_parent', '0'),
(351, 205, '_menu_item_object_id', '198'),
(352, 205, '_menu_item_object', 'page'),
(353, 205, '_menu_item_target', ''),
(354, 205, '_menu_item_classes', 'a:1:{i:0;s:0:"";}'),
(355, 205, '_menu_item_xfn', ''),
(356, 205, '_menu_item_url', ''),
(358, 206, '_menu_item_type', 'post_type'),
(359, 206, '_menu_item_menu_item_parent', '0'),
(360, 206, '_menu_item_object_id', '197'),
(361, 206, '_menu_item_object', 'page'),
(362, 206, '_menu_item_target', ''),
(363, 206, '_menu_item_classes', 'a:1:{i:0;s:0:"";}'),
(364, 206, '_menu_item_xfn', ''),
(365, 206, '_menu_item_url', ''),
(367, 37, '_wp_old_date', '2024-07-22'),
(368, 207, '_edit_last', '1'),
(369, 207, '_wp_page_template', 'default'),
(370, 207, 'acf', 'a:2:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(371, 207, '_seopress_redirections_type', '301'),
(372, 207, '_seopress_redirections_logged_status', 'both'),
(373, 207, '_seopress_analysis_target_kw', ''),
(375, 207, '_edit_lock', '1722332660:1'),
(376, 211, '_menu_item_type', 'post_type'),
(377, 211, '_menu_item_menu_item_parent', '0'),
(378, 211, '_menu_item_object_id', '157'),
(379, 211, '_menu_item_object', 'page'),
(380, 211, '_menu_item_target', '') ;
INSERT INTO `nd_postmeta` ( `meta_id`, `post_id`, `meta_key`, `meta_value`) VALUES
(381, 211, '_menu_item_classes', 'a:1:{i:0;s:0:"";}'),
(382, 211, '_menu_item_xfn', ''),
(383, 211, '_menu_item_url', ''),
(385, 212, '_menu_item_type', 'post_type'),
(386, 212, '_menu_item_menu_item_parent', '0'),
(387, 212, '_menu_item_object_id', '173'),
(388, 212, '_menu_item_object', 'page'),
(389, 212, '_menu_item_target', ''),
(390, 212, '_menu_item_classes', 'a:1:{i:0;s:0:"";}'),
(391, 212, '_menu_item_xfn', ''),
(392, 212, '_menu_item_url', ''),
(394, 33, '_wp_old_date', '2024-07-22'),
(395, 213, '_menu_item_type', 'post_type'),
(396, 213, '_menu_item_menu_item_parent', '0'),
(397, 213, '_menu_item_object_id', '207'),
(398, 213, '_menu_item_object', 'page'),
(399, 213, '_menu_item_target', ''),
(400, 213, '_menu_item_classes', 'a:1:{i:0;s:0:"";}'),
(401, 213, '_menu_item_xfn', ''),
(402, 213, '_menu_item_url', ''),
(404, 214, '_menu_item_type', 'post_type'),
(405, 214, '_menu_item_menu_item_parent', '0'),
(406, 214, '_menu_item_object_id', '145'),
(407, 214, '_menu_item_object', 'page'),
(408, 214, '_menu_item_target', ''),
(409, 214, '_menu_item_classes', 'a:1:{i:0;s:0:"";}'),
(410, 214, '_menu_item_xfn', ''),
(411, 214, '_menu_item_url', ''),
(413, 36, '_wp_old_date', '2024-07-22'),
(428, 224, '_edit_last', '1'),
(430, 224, '_edit_lock', '1727946954:1'),
(464, 224, '_wp_old_slug', 'test'),
(469, 284, '_edit_lock', '1727259775:1'),
(470, 284, '_edit_last', '1'),
(471, 284, 'acf', 'a:6:{s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";s:12:"intro_img_id";i:8;s:13:"_intro_img_id";s:19:"field_66ab762b3bc67";s:12:"playlist_ids";a:3:{i:0;s:3:"299";i:1;s:3:"300";i:2;s:3:"301";}s:13:"_playlist_ids";s:19:"field_66ab76713bc69";}'),
(498, 299, '_edit_lock', '1727257479:1'),
(499, 299, '_edit_last', '1'),
(501, 300, '_edit_lock', '1727257485:1'),
(502, 300, '_edit_last', '1'),
(504, 301, '_edit_lock', '1727258264:1'),
(505, 301, '_edit_last', '1'),
(507, 299, 'acf', 'a:12:{s:6:"parent";s:10:"collection";s:7:"_parent";s:19:"field_66ab79834b31b";s:20:"parent_collection_id";i:284;s:21:"_parent_collection_id";s:19:"field_66ab7b843a8f4";s:12:"intro_img_id";i:8;s:13:"_intro_img_id";s:19:"field_66ab78fc4b318";s:10:"intro_desc";s:0:"";s:11:"_intro_desc";s:19:"field_66ab79264b319";s:15:"publication_ids";s:0:"";s:16:"_publication_ids";s:19:"field_66ab7aa343c01";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(508, 300, 'acf', 'a:12:{s:6:"parent";s:10:"collection";s:7:"_parent";s:19:"field_66ab79834b31b";s:20:"parent_collection_id";i:284;s:21:"_parent_collection_id";s:19:"field_66ab7b843a8f4";s:12:"intro_img_id";i:8;s:13:"_intro_img_id";s:19:"field_66ab78fc4b318";s:10:"intro_desc";s:0:"";s:11:"_intro_desc";s:19:"field_66ab79264b319";s:15:"publication_ids";s:0:"";s:16:"_publication_ids";s:19:"field_66ab7aa343c01";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(509, 301, 'acf', 'a:12:{s:6:"parent";s:10:"collection";s:7:"_parent";s:19:"field_66ab79834b31b";s:20:"parent_collection_id";i:284;s:21:"_parent_collection_id";s:19:"field_66ab7b843a8f4";s:12:"intro_img_id";i:8;s:13:"_intro_img_id";s:19:"field_66ab78fc4b318";s:10:"intro_desc";s:0:"";s:11:"_intro_desc";s:19:"field_66ab79264b319";s:15:"publication_ids";s:0:"";s:16:"_publication_ids";s:19:"field_66ab7aa343c01";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(534, 382, '_edit_lock', '1725541013:1'),
(535, 382, '_edit_last', '1'),
(536, 382, 'acf', 'a:6:{s:14:"intro_subtitle";s:24:"Danseuse et chorégraphe";s:15:"_intro_subtitle";s:19:"field_66ab9c90a215d";s:12:"intro_img_id";i:8;s:13:"_intro_img_id";s:19:"field_66ab9c5ba215c";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(538, 392, '_edit_lock', '1727945744:1'),
(539, 392, '_edit_last', '1'),
(543, 395, '_edit_lock', '1727873165:1'),
(544, 395, '_edit_last', '1'),
(545, 396, '_edit_lock', '1727097334:1'),
(546, 397, '_edit_lock', '1722953995:1'),
(548, 396, '_edit_last', '1'),
(549, 396, 'acf', 'a:38:{s:8:"category";s:1:"7";s:9:"_category";s:19:"field_66a8f6cbc64fa";s:6:"format";s:2:"15";s:7:"_format";s:19:"field_66aa544be2232";s:10:"chronology";s:2:"20";s:11:"_chronology";s:19:"field_66aa5536cd104";s:10:"dance_type";s:2:"27";s:11:"_dance_type";s:19:"field_66aa5595cd105";s:5:"piece";s:0:"";s:6:"_piece";s:19:"field_66aa55b1cd106";s:3:"tag";s:0:"";s:4:"_tag";s:19:"field_66aa55d5cd107";s:15:"push_to_novelty";s:1:"1";s:16:"_push_to_novelty";s:19:"field_66a9000a300c6";s:11:"member_only";s:1:"1";s:12:"_member_only";s:19:"field_66a8ffe7300c5";s:15:"has_temporality";s:1:"0";s:16:"_has_temporality";s:19:"field_66a9e844ee3cc";s:10:"media_type";s:5:"video";s:11:"_media_type";s:19:"field_66aa523327c03";s:14:"media_video_id";s:9:"242893250";s:15:"_media_video_id";s:19:"field_66aa52e5a3b99";s:8:"subtitle";s:0:"";s:9:"_subtitle";s:19:"field_66a8f9ae4b6ed";s:3:"txt";s:336:"Eiusmod aliqua mollit non aute cupidatat labore incididunt ad eu in fugiat. Sunt nostrud dolor enim nulla. Laboris quis ullamco Lorem aliquip nostrud est aute nisi tempor. Cupidatat eu nostrud labore aliquip dolor occaecat excepteur anim occaecat nulla exercitation sunt duis. Aliquip commodo id exercitation aliquip ex esse officia et.";s:4:"_txt";s:19:"field_66a8fefadbb6a";s:9:"main_data";s:0:"";s:10:"_main_data";s:19:"field_66a8f9c84b6ee";s:7:"credits";s:0:"";s:8:"_credits";s:19:"field_66a8fe3919979";s:20:"push_publication_ids";s:0:"";s:21:"_push_publication_ids";s:19:"field_66ab4a146d939";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";s:13:"push_to_honor";s:1:"0";s:14:"_push_to_honor";s:19:"field_66b49a8116fae";s:13:"media_credits";s:0:"";s:14:"_media_credits";s:19:"field_66d6e096b567e";}'),
(550, 397, '_edit_last', '1'),
(551, 397, 'acf', 'a:34:{s:8:"category";s:2:"11";s:9:"_category";s:19:"field_66a8f6cbc64fa";s:6:"format";s:2:"15";s:7:"_format";s:19:"field_66aa544be2232";s:10:"chronology";s:2:"24";s:11:"_chronology";s:19:"field_66aa5536cd104";s:10:"dance_type";s:2:"30";s:11:"_dance_type";s:19:"field_66aa5595cd105";s:5:"piece";s:2:"59";s:6:"_piece";s:19:"field_66aa55b1cd106";s:3:"tag";s:0:"";s:4:"_tag";s:19:"field_66aa55d5cd107";s:15:"push_to_novelty";s:1:"1";s:16:"_push_to_novelty";s:19:"field_66a9000a300c6";s:11:"member_only";s:1:"0";s:12:"_member_only";s:19:"field_66a8ffe7300c5";s:15:"has_temporality";s:1:"0";s:16:"_has_temporality";s:19:"field_66a9e844ee3cc";s:10:"media_type";s:5:"video";s:11:"_media_type";s:19:"field_66aa523327c03";s:14:"media_video_id";s:9:"242893302";s:15:"_media_video_id";s:19:"field_66aa52e5a3b99";s:8:"subtitle";s:0:"";s:9:"_subtitle";s:19:"field_66a8f9ae4b6ed";s:3:"txt";s:0:"";s:4:"_txt";s:19:"field_66a8fefadbb6a";s:9:"main_data";s:0:"";s:10:"_main_data";s:19:"field_66a8f9c84b6ee";s:7:"credits";s:0:"";s:8:"_credits";s:19:"field_66a8fe3919979";s:20:"push_publication_ids";s:0:"";s:21:"_push_publication_ids";s:19:"field_66ab4a146d939";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(557, 392, 'push_to_novelty', '1'),
(558, 392, '_push_to_novelty', 'field_66a9000a300c6'),
(560, 224, 'push_to_novelty', '0'),
(561, 224, '_push_to_novelty', 'field_66a9000a300c6'),
(562, 395, 'push_to_novelty', '0'),
(563, 395, '_push_to_novelty', 'field_66a9000a300c6'),
(564, 396, 'push_to_novelty', '1'),
(565, 396, '_push_to_novelty', 'field_66a9000a300c6'),
(566, 397, 'push_to_novelty', '1'),
(567, 397, '_push_to_novelty', 'field_66a9000a300c6'),
(568, 404, '_edit_lock', '1723112531:1'),
(569, 404, '_edit_last', '1'),
(570, 404, 'push_to_novelty', '1'),
(571, 404, '_push_to_novelty', 'field_66a9000a300c6'),
(572, 404, 'acf', 'a:34:{s:8:"category";s:1:"8";s:9:"_category";s:19:"field_66a8f6cbc64fa";s:6:"format";s:2:"14";s:7:"_format";s:19:"field_66aa544be2232";s:10:"chronology";s:2:"18";s:11:"_chronology";s:19:"field_66aa5536cd104";s:10:"dance_type";s:2:"26";s:11:"_dance_type";s:19:"field_66aa5595cd105";s:5:"piece";s:0:"";s:6:"_piece";s:19:"field_66aa55b1cd106";s:3:"tag";s:0:"";s:4:"_tag";s:19:"field_66aa55d5cd107";s:15:"push_to_novelty";s:1:"1";s:16:"_push_to_novelty";s:19:"field_66a9000a300c6";s:11:"member_only";s:1:"0";s:12:"_member_only";s:19:"field_66a8ffe7300c5";s:15:"has_temporality";s:1:"0";s:16:"_has_temporality";s:19:"field_66a9e844ee3cc";s:10:"media_type";s:5:"video";s:11:"_media_type";s:19:"field_66aa523327c03";s:14:"media_video_id";s:4:"1312";s:15:"_media_video_id";s:19:"field_66aa52e5a3b99";s:8:"subtitle";s:0:"";s:9:"_subtitle";s:19:"field_66a8f9ae4b6ed";s:3:"txt";s:0:"";s:4:"_txt";s:19:"field_66a8fefadbb6a";s:9:"main_data";s:0:"";s:10:"_main_data";s:19:"field_66a8f9c84b6ee";s:7:"credits";s:0:"";s:8:"_credits";s:19:"field_66a8fe3919979";s:20:"push_publication_ids";s:0:"";s:21:"_push_publication_ids";s:19:"field_66ab4a146d939";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(573, 405, '_edit_lock', '1727183377:1'),
(574, 405, '_edit_last', '1'),
(578, 406, '_edit_lock', '1727253800:1'),
(579, 406, '_edit_last', '1'),
(580, 406, 'push_to_novelty', '0'),
(581, 406, '_push_to_novelty', 'field_66a9000a300c6'),
(582, 406, 'acf', 'a:70:{s:8:"category";s:2:"11";s:9:"_category";s:19:"field_66a8f6cbc64fa";s:6:"format";s:2:"15";s:7:"_format";s:19:"field_66aa544be2232";s:10:"chronology";s:2:"24";s:11:"_chronology";s:19:"field_66aa5536cd104";s:10:"dance_type";s:2:"30";s:11:"_dance_type";s:19:"field_66aa5595cd105";s:5:"piece";s:0:"";s:6:"_piece";s:19:"field_66aa55b1cd106";s:3:"tag";a:1:{i:0;s:2:"53";}s:4:"_tag";s:19:"field_66aa55d5cd107";s:15:"push_to_novelty";s:1:"0";s:16:"_push_to_novelty";s:19:"field_66a9000a300c6";s:11:"member_only";s:1:"0";s:12:"_member_only";s:19:"field_66a8ffe7300c5";s:15:"has_temporality";s:1:"0";s:16:"_has_temporality";s:19:"field_66a9e844ee3cc";s:10:"media_type";s:3:"img";s:11:"_media_type";s:19:"field_66aa523327c03";s:14:"media_video_id";s:4:"1312";s:15:"_media_video_id";s:19:"field_66aa52e5a3b99";s:8:"subtitle";s:0:"";s:9:"_subtitle";s:19:"field_66a8f9ae4b6ed";s:3:"txt";s:893:"Proident incididunt exercitation fugiat voluptate eiusmod velit do aliqua do aliquip amet incididunt ad. Anim ullamco ullamco voluptate nulla aute eiusmod. Culpa ipsum mollit consectetur esse laboris esse ullamco est dolore eiusmod occaecat. Nostrud sunt ea ad culpa commodo fugiat id.\r\n\r\nEa est aliquip exercitation enim id labore exercitation culpa non fugiat sint irure laborum. Nulla est ut ipsum anim exercitation. Labore proident quis reprehenderit mollit minim ullamco sint fugiat voluptate proident ullamco Lorem proident reprehenderit. Velit mollit laboris veniam aliquip ad qui sit cillum cupidatat quis reprehenderit quis officia Lorem. Nisi deserunt reprehenderit occaecat veniam do labore anim nostrud reprehenderit laboris. Exercitation nostrud est fugiat consequat quis veniam laborum sint anim anim nulla dolore dolore. Non eiusmod eiusmod eu. Officia minim eu sint irure elit.";s:4:"_txt";s:19:"field_66a8fefadbb6a";s:9:"main_data";i:3;s:10:"_main_data";s:19:"field_66a8f9c84b6ee";s:7:"credits";i:3;s:8:"_credits";s:19:"field_66a8fe3919979";s:20:"push_publication_ids";s:0:"";s:21:"_push_publication_ids";s:19:"field_66ab4a146d939";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";s:13:"push_to_honor";s:1:"1";s:14:"_push_to_honor";s:19:"field_66b49a8116fae";s:12:"media_img_id";i:446;s:13:"_media_img_id";s:19:"field_66aa52a8a3b98";s:13:"media_credits";s:0:"";s:14:"_media_credits";s:19:"field_66d6e096b567e";s:17:"main_data_0_label";s:2:"43";s:18:"_main_data_0_label";s:19:"field_66a8f9ec4b6ef";s:15:"main_data_0_txt";s:27:"<a href="#">Numeridanse</a>";s:16:"_main_data_0_txt";s:19:"field_66a8fa234b6f0";s:17:"main_data_1_label";s:2:"42";s:18:"_main_data_1_label";s:19:"field_66a8f9ec4b6ef";s:15:"main_data_1_txt";s:42:"<a href="#">Anne Teresa De Keersmaeker</a>";s:16:"_main_data_1_txt";s:19:"field_66a8fa234b6f0";s:17:"main_data_2_label";s:2:"61";s:18:"_main_data_2_label";s:19:"field_66a8f9ec4b6ef";s:15:"main_data_2_txt";s:20:"<a href="#">TNTV</a>";s:16:"_main_data_2_txt";s:19:"field_66a8fa234b6f0";s:15:"credits_0_label";s:2:"43";s:16:"_credits_0_label";s:19:"field_66a8fe3a1997c";s:13:"credits_0_txt";s:27:"<a href="#">Numeridanse</a>";s:14:"_credits_0_txt";s:19:"field_66a8fe3a1997d";s:17:"credits_0_is_push";s:1:"1";s:18:"_credits_0_is_push";s:19:"field_66a8fe521997e";s:15:"credits_1_label";s:2:"42";s:16:"_credits_1_label";s:19:"field_66a8fe3a1997c";s:13:"credits_1_txt";s:42:"<a href="#">Anne Teresa De Keersmaeker</a>";s:14:"_credits_1_txt";s:19:"field_66a8fe3a1997d";s:17:"credits_1_is_push";s:1:"1";s:18:"_credits_1_is_push";s:19:"field_66a8fe521997e";s:15:"credits_2_label";s:2:"61";s:16:"_credits_2_label";s:19:"field_66a8fe3a1997c";s:13:"credits_2_txt";s:4:"TNTV";s:14:"_credits_2_txt";s:19:"field_66a8fe3a1997d";s:17:"credits_2_is_push";s:1:"1";s:18:"_credits_2_is_push";s:19:"field_66a8fe521997e";}'),
(583, 407, '_edit_lock', '1723127665:1'),
(584, 407, '_edit_last', '1'),
(585, 407, 'push_to_novelty', '1'),
(586, 407, '_push_to_novelty', 'field_66a9000a300c6'),
(587, 407, 'acf', 'a:36:{s:8:"category";s:2:"10";s:9:"_category";s:19:"field_66a8f6cbc64fa";s:6:"format";s:2:"15";s:7:"_format";s:19:"field_66aa544be2232";s:10:"chronology";s:2:"23";s:11:"_chronology";s:19:"field_66aa5536cd104";s:10:"dance_type";s:2:"27";s:11:"_dance_type";s:19:"field_66aa5595cd105";s:5:"piece";s:0:"";s:6:"_piece";s:19:"field_66aa55b1cd106";s:3:"tag";s:0:"";s:4:"_tag";s:19:"field_66aa55d5cd107";s:15:"push_to_novelty";s:1:"1";s:16:"_push_to_novelty";s:19:"field_66a9000a300c6";s:11:"member_only";s:1:"0";s:12:"_member_only";s:19:"field_66a8ffe7300c5";s:15:"has_temporality";s:1:"0";s:16:"_has_temporality";s:19:"field_66a9e844ee3cc";s:10:"media_type";s:5:"video";s:11:"_media_type";s:19:"field_66aa523327c03";s:14:"media_video_id";s:4:"1312";s:15:"_media_video_id";s:19:"field_66aa52e5a3b99";s:8:"subtitle";s:0:"";s:9:"_subtitle";s:19:"field_66a8f9ae4b6ed";s:3:"txt";s:0:"";s:4:"_txt";s:19:"field_66a8fefadbb6a";s:9:"main_data";s:0:"";s:10:"_main_data";s:19:"field_66a8f9c84b6ee";s:7:"credits";s:0:"";s:8:"_credits";s:19:"field_66a8fe3919979";s:20:"push_publication_ids";s:0:"";s:21:"_push_publication_ids";s:19:"field_66ab4a146d939";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";s:13:"push_to_honor";s:1:"0";s:14:"_push_to_honor";s:19:"field_66b49a8116fae";}'),
(588, 408, '_edit_lock', '1722954508:1'),
(589, 408, '_edit_last', '1'),
(590, 408, 'push_to_novelty', '0'),
(591, 408, '_push_to_novelty', 'field_66a9000a300c6'),
(592, 408, 'acf', 'a:34:{s:8:"category";s:2:"10";s:9:"_category";s:19:"field_66a8f6cbc64fa";s:6:"format";s:2:"14";s:7:"_format";s:19:"field_66aa544be2232";s:10:"chronology";s:2:"24";s:11:"_chronology";s:19:"field_66aa5536cd104";s:10:"dance_type";s:2:"28";s:11:"_dance_type";s:19:"field_66aa5595cd105";s:5:"piece";s:2:"59";s:6:"_piece";s:19:"field_66aa55b1cd106";s:3:"tag";a:2:{i:0;s:2:"53";i:1;s:2:"55";}s:4:"_tag";s:19:"field_66aa55d5cd107";s:15:"push_to_novelty";s:1:"0";s:16:"_push_to_novelty";s:19:"field_66a9000a300c6";s:11:"member_only";s:1:"1";s:12:"_member_only";s:19:"field_66a8ffe7300c5";s:15:"has_temporality";s:1:"0";s:16:"_has_temporality";s:19:"field_66a9e844ee3cc";s:10:"media_type";s:5:"video";s:11:"_media_type";s:19:"field_66aa523327c03";s:14:"media_video_id";s:4:"1312";s:15:"_media_video_id";s:19:"field_66aa52e5a3b99";s:8:"subtitle";s:0:"";s:9:"_subtitle";s:19:"field_66a8f9ae4b6ed";s:3:"txt";s:0:"";s:4:"_txt";s:19:"field_66a8fefadbb6a";s:9:"main_data";s:0:"";s:10:"_main_data";s:19:"field_66a8f9c84b6ee";s:7:"credits";s:0:"";s:8:"_credits";s:19:"field_66a8fe3919979";s:20:"push_publication_ids";s:0:"";s:21:"_push_publication_ids";s:19:"field_66ab4a146d939";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(593, 409, '_edit_last', '1'),
(594, 409, 'push_to_novelty', '1'),
(595, 409, '_push_to_novelty', 'field_66a9000a300c6'),
(596, 409, 'acf', 'a:36:{s:8:"category";s:1:"9";s:9:"_category";s:19:"field_66a8f6cbc64fa";s:6:"format";s:2:"14";s:7:"_format";s:19:"field_66aa544be2232";s:10:"chronology";s:2:"18";s:11:"_chronology";s:19:"field_66aa5536cd104";s:10:"dance_type";s:2:"27";s:11:"_dance_type";s:19:"field_66aa5595cd105";s:5:"piece";s:0:"";s:6:"_piece";s:19:"field_66aa55b1cd106";s:3:"tag";a:1:{i:0;s:2:"54";}s:4:"_tag";s:19:"field_66aa55d5cd107";s:15:"push_to_novelty";s:1:"1";s:16:"_push_to_novelty";s:19:"field_66a9000a300c6";s:11:"member_only";s:1:"0";s:12:"_member_only";s:19:"field_66a8ffe7300c5";s:15:"has_temporality";s:1:"1";s:16:"_has_temporality";s:19:"field_66a9e844ee3cc";s:10:"media_type";s:5:"video";s:11:"_media_type";s:19:"field_66aa523327c03";s:14:"media_video_id";s:4:"1312";s:15:"_media_video_id";s:19:"field_66aa52e5a3b99";s:8:"subtitle";s:0:"";s:9:"_subtitle";s:19:"field_66a8f9ae4b6ed";s:3:"txt";s:0:"";s:4:"_txt";s:19:"field_66a8fefadbb6a";s:9:"main_data";s:0:"";s:10:"_main_data";s:19:"field_66a8f9c84b6ee";s:7:"credits";s:0:"";s:8:"_credits";s:19:"field_66a8fe3919979";s:20:"push_publication_ids";s:0:"";s:21:"_push_publication_ids";s:19:"field_66ab4a146d939";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";s:12:"display_date";s:8:"20241213";s:13:"_display_date";s:19:"field_66a9e87cee3cd";}'),
(597, 409, '_edit_lock', '1727879608:1'),
(598, 410, '_edit_lock', '1723019341:1'),
(599, 410, '_edit_last', '1'),
(600, 410, 'push_to_novelty', '1'),
(601, 410, '_push_to_novelty', 'field_66a9000a300c6'),
(602, 410, 'acf', 'a:34:{s:8:"category";s:1:"8";s:9:"_category";s:19:"field_66a8f6cbc64fa";s:6:"format";s:2:"14";s:7:"_format";s:19:"field_66aa544be2232";s:10:"chronology";s:2:"21";s:11:"_chronology";s:19:"field_66aa5536cd104";s:10:"dance_type";s:2:"27";s:11:"_dance_type";s:19:"field_66aa5595cd105";s:5:"piece";s:0:"";s:6:"_piece";s:19:"field_66aa55b1cd106";s:3:"tag";s:0:"";s:4:"_tag";s:19:"field_66aa55d5cd107";s:15:"push_to_novelty";s:1:"1";s:16:"_push_to_novelty";s:19:"field_66a9000a300c6";s:11:"member_only";s:1:"1";s:12:"_member_only";s:19:"field_66a8ffe7300c5";s:15:"has_temporality";s:1:"0";s:16:"_has_temporality";s:19:"field_66a9e844ee3cc";s:10:"media_type";s:5:"video";s:11:"_media_type";s:19:"field_66aa523327c03";s:14:"media_video_id";s:4:"1312";s:15:"_media_video_id";s:19:"field_66aa52e5a3b99";s:8:"subtitle";s:0:"";s:9:"_subtitle";s:19:"field_66a8f9ae4b6ed";s:3:"txt";s:0:"";s:4:"_txt";s:19:"field_66a8fefadbb6a";s:9:"main_data";s:0:"";s:10:"_main_data";s:19:"field_66a8f9c84b6ee";s:7:"credits";s:0:"";s:8:"_credits";s:19:"field_66a8fe3919979";s:20:"push_publication_ids";s:0:"";s:21:"_push_publication_ids";s:19:"field_66ab4a146d939";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(603, 411, '_edit_lock', '1727272735:1') ;
INSERT INTO `nd_postmeta` ( `meta_id`, `post_id`, `meta_key`, `meta_value`) VALUES
(604, 411, '_edit_last', '1'),
(605, 411, 'push_to_novelty', '0'),
(606, 411, '_push_to_novelty', 'field_66a9000a300c6'),
(607, 411, 'acf', 'a:36:{s:8:"category";s:1:"7";s:9:"_category";s:19:"field_66a8f6cbc64fa";s:6:"format";s:2:"15";s:7:"_format";s:19:"field_66aa544be2232";s:10:"chronology";s:2:"20";s:11:"_chronology";s:19:"field_66aa5536cd104";s:10:"dance_type";s:2:"29";s:11:"_dance_type";s:19:"field_66aa5595cd105";s:5:"piece";s:2:"60";s:6:"_piece";s:19:"field_66aa55b1cd106";s:3:"tag";a:1:{i:0;s:2:"57";}s:4:"_tag";s:19:"field_66aa55d5cd107";s:15:"push_to_novelty";s:1:"0";s:16:"_push_to_novelty";s:19:"field_66a9000a300c6";s:11:"member_only";s:1:"1";s:12:"_member_only";s:19:"field_66a8ffe7300c5";s:15:"has_temporality";s:1:"0";s:16:"_has_temporality";s:19:"field_66a9e844ee3cc";s:10:"media_type";s:5:"video";s:11:"_media_type";s:19:"field_66aa523327c03";s:14:"media_video_id";s:4:"1312";s:15:"_media_video_id";s:19:"field_66aa52e5a3b99";s:8:"subtitle";s:0:"";s:9:"_subtitle";s:19:"field_66a8f9ae4b6ed";s:3:"txt";s:0:"";s:4:"_txt";s:19:"field_66a8fefadbb6a";s:9:"main_data";s:0:"";s:10:"_main_data";s:19:"field_66a8f9c84b6ee";s:7:"credits";s:0:"";s:8:"_credits";s:19:"field_66a8fe3919979";s:20:"push_publication_ids";s:0:"";s:21:"_push_publication_ids";s:19:"field_66ab4a146d939";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";s:13:"push_to_honor";s:1:"1";s:14:"_push_to_honor";s:19:"field_66b49a8116fae";}'),
(612, 411, 'push_to_honor', '1'),
(613, 411, '_push_to_honor', 'field_66b49a8116fae'),
(614, 406, 'push_to_honor', '1'),
(615, 406, '_push_to_honor', 'field_66b49a8116fae'),
(618, 224, 'push_to_honor', '1'),
(619, 224, '_push_to_honor', 'field_66b49a8116fae'),
(624, 407, 'push_to_honor', '0'),
(625, 407, '_push_to_honor', 'field_66b49a8116fae'),
(645, 446, '_wp_attached_file', '2024/07/img_1-temp.jpg'),
(646, 446, '_wp_attachment_metadata', 'a:6:{s:5:"width";i:1952;s:6:"height";i:1008;s:4:"file";s:22:"2024/07/img_1-temp.jpg";s:8:"filesize";i:1460875;s:5:"sizes";a:1:{s:9:"thumbnail";a:5:{s:4:"file";s:22:"img_1-temp-256x256.jpg";s:5:"width";i:256;s:6:"height";i:256;s:9:"mime-type";s:10:"image/jpeg";s:8:"filesize";i:28267;}}s:10:"image_meta";a:12:{s:8:"aperture";s:1:"0";s:6:"credit";s:0:"";s:6:"camera";s:0:"";s:7:"caption";s:0:"";s:17:"created_timestamp";s:1:"0";s:9:"copyright";s:0:"";s:12:"focal_length";s:1:"0";s:3:"iso";s:1:"0";s:13:"shutter_speed";s:1:"0";s:5:"title";s:0:"";s:11:"orientation";s:1:"0";s:8:"keywords";a:0:{}}}'),
(651, 456, '_wp_attached_file', '2024/07/Empty-file-temp.pdf'),
(652, 456, '_wp_attachment_metadata', 'a:2:{s:5:"sizes";a:2:{s:4:"full";a:5:{s:4:"file";s:23:"Empty-file-temp-pdf.jpg";s:5:"width";i:1058;s:6:"height";i:1497;s:9:"mime-type";s:10:"image/jpeg";s:8:"filesize";i:10469;}s:9:"thumbnail";a:5:{s:4:"file";s:31:"Empty-file-temp-pdf-181x256.jpg";s:5:"width";i:181;s:6:"height";i:256;s:9:"mime-type";s:10:"image/jpeg";s:8:"filesize";i:3043;}}s:8:"filesize";i:7151;}'),
(653, 457, '_wp_attached_file', '2024/07/Empty-file-2-temp.pdf'),
(654, 457, '_wp_attachment_metadata', 'a:2:{s:5:"sizes";a:2:{s:4:"full";a:5:{s:4:"file";s:25:"Empty-file-2-temp-pdf.jpg";s:5:"width";i:1058;s:6:"height";i:1497;s:9:"mime-type";s:10:"image/jpeg";s:8:"filesize";i:11691;}s:9:"thumbnail";a:5:{s:4:"file";s:33:"Empty-file-2-temp-pdf-181x256.jpg";s:5:"width";i:181;s:6:"height";i:256;s:9:"mime-type";s:10:"image/jpeg";s:8:"filesize";i:3810;}}s:8:"filesize";i:39234;}'),
(656, 224, 'acf', 'a:218:{s:8:"category";s:1:"7";s:9:"_category";s:19:"field_66a8f6cbc64fa";s:6:"format";s:2:"15";s:7:"_format";s:19:"field_66aa544be2232";s:10:"chronology";s:2:"22";s:11:"_chronology";s:19:"field_66aa5536cd104";s:10:"dance_type";s:2:"28";s:11:"_dance_type";s:19:"field_66aa5595cd105";s:5:"piece";s:0:"";s:6:"_piece";s:19:"field_66aa55b1cd106";s:3:"tag";a:3:{i:0;s:2:"54";i:1;s:2:"55";i:2;s:2:"56";}s:4:"_tag";s:19:"field_66aa55d5cd107";s:15:"push_to_novelty";s:1:"0";s:16:"_push_to_novelty";s:19:"field_66a9000a300c6";s:13:"push_to_honor";s:1:"1";s:14:"_push_to_honor";s:19:"field_66b49a8116fae";s:11:"member_only";s:1:"0";s:12:"_member_only";s:19:"field_66a8ffe7300c5";s:15:"has_temporality";s:1:"0";s:16:"_has_temporality";s:19:"field_66a9e844ee3cc";s:10:"media_type";s:5:"video";s:11:"_media_type";s:19:"field_66aa523327c03";s:14:"media_video_id";s:9:"244009930";s:15:"_media_video_id";s:19:"field_66aa52e5a3b99";s:8:"subtitle";s:35:"Exemple d’un sous-titre optionnel";s:9:"_subtitle";s:19:"field_66a8f9ae4b6ed";s:3:"txt";s:587:"Tumuls est une procession infinie de treize corps chantant et dansant au sein d’une seule et même pratique, un seul et même geste. Fruit d’une collaboration entre le chorégraphe François Chaignaud et Geoffroy Jourdain, directeur des Cris de Paris, ce projet porte le rêve d’éprouver conjointement ces deux arts, de penser ensemble danse et musique. Les tumulus étaient autrefois des tombes surmontées d’une colline. Sépultures d’un genre particulier, ces «  œuvres » contiennent autant le corps des défunts que la vie qui leur succède et pousse au-dessus d’eux.";s:4:"_txt";s:19:"field_66a8fefadbb6a";s:17:"main_data_0_label";s:2:"41";s:18:"_main_data_0_label";s:19:"field_66a8f9ec4b6ef";s:15:"main_data_0_txt";s:84:"[lesanimals_rich_link type="collection" collection-id="573" post-name="Numeridanse"]";s:16:"_main_data_0_txt";s:39:"field_66a8fa234b6f0_field_66faa1ff17a7d";s:17:"main_data_1_label";s:2:"40";s:18:"_main_data_1_label";s:19:"field_66a8f9ec4b6ef";s:15:"main_data_1_txt";s:84:"[lesanimals_rich_link type="collection" collection-id="573" post-name="Numeridanse"]";s:16:"_main_data_1_txt";s:39:"field_66a8fa234b6f0_field_66faa1ff17a7d";s:17:"main_data_2_label";s:2:"38";s:18:"_main_data_2_label";s:19:"field_66a8f9ec4b6ef";s:15:"main_data_2_txt";s:4:"2024";s:16:"_main_data_2_txt";s:39:"field_66a8fa234b6f0_field_66faa1ff17a7d";s:17:"main_data_3_label";s:2:"39";s:18:"_main_data_3_label";s:19:"field_66a8f9ec4b6ef";s:15:"main_data_3_txt";s:4:"2019";s:16:"_main_data_3_txt";s:39:"field_66a8fa234b6f0_field_66faa1ff17a7d";s:9:"main_data";i:4;s:10:"_main_data";s:19:"field_66a8f9c84b6ee";s:15:"credits_0_label";s:2:"37";s:16:"_credits_0_label";s:19:"field_66a8fe3a1997c";s:13:"credits_0_txt";s:86:"[lesanimals_rich_link type="profile" profile-id="579" post-name="Geoffroy%20Jourdain"]";s:14:"_credits_0_txt";s:39:"field_66a8fe3a1997d_field_66faa1ff17a7d";s:17:"credits_0_is_push";s:1:"1";s:18:"_credits_0_is_push";s:19:"field_66a8fe521997e";s:15:"credits_1_label";s:2:"43";s:16:"_credits_1_label";s:19:"field_66a8fe3a1997c";s:13:"credits_1_txt";s:18:"Maison de la danse";s:14:"_credits_1_txt";s:39:"field_66a8fe3a1997d_field_66faa1ff17a7d";s:17:"credits_1_is_push";s:1:"0";s:18:"_credits_1_is_push";s:19:"field_66a8fe521997e";s:15:"credits_2_label";s:2:"41";s:16:"_credits_2_label";s:19:"field_66a8fe3a1997c";s:13:"credits_2_txt";s:42:"Maison de la danse, Saisons 2020 &gt; 2024";s:14:"_credits_2_txt";s:39:"field_66a8fe3a1997d_field_66faa1ff17a7d";s:17:"credits_2_is_push";s:1:"0";s:18:"_credits_2_is_push";s:19:"field_66a8fe521997e";s:15:"credits_3_label";s:2:"42";s:16:"_credits_3_label";s:19:"field_66a8fe3a1997c";s:13:"credits_3_txt";s:92:"[lesanimals_rich_link type="profile" profile-id="580" post-name="Fran%C3%A7ois%20Chaignaud"]";s:14:"_credits_3_txt";s:39:"field_66a8fe3a1997d_field_66faa1ff17a7d";s:17:"credits_3_is_push";s:1:"1";s:18:"_credits_3_is_push";s:19:"field_66a8fe521997e";s:15:"credits_4_label";s:2:"36";s:16:"_credits_4_label";s:19:"field_66a8fe3a1997c";s:13:"credits_4_txt";s:83:"[lesanimals_rich_link type="profile" profile-id="581" post-name="Fabien%20Plasson"]";s:14:"_credits_4_txt";s:39:"field_66a8fe3a1997d_field_66faa1ff17a7d";s:17:"credits_4_is_push";s:1:"1";s:18:"_credits_4_is_push";s:19:"field_66a8fe521997e";s:15:"credits_5_label";s:2:"45";s:16:"_credits_5_label";s:19:"field_66a8fe3a1997c";s:13:"credits_5_txt";s:84:"[lesanimals_rich_link type="profile" profile-id="586" post-name="Marinette%20Buchy"]";s:14:"_credits_5_txt";s:39:"field_66a8fe3a1997d_field_66faa1ff17a7d";s:17:"credits_5_is_push";s:1:"1";s:18:"_credits_5_is_push";s:19:"field_66a8fe521997e";s:15:"credits_6_label";s:2:"46";s:16:"_credits_6_label";s:19:"field_66a8fe3a1997c";s:13:"credits_6_txt";s:67:"Romain Brau ; Régie costume : Alejandra Garcia ou Cara Ben Assayag";s:14:"_credits_6_txt";s:39:"field_66a8fe3a1997d_field_66faa1ff17a7d";s:17:"credits_6_is_push";s:1:"0";s:18:"_credits_6_is_push";s:19:"field_66a8fe521997e";s:15:"credits_7_label";s:2:"61";s:16:"_credits_7_label";s:19:"field_66a8fe3a1997c";s:13:"credits_7_txt";s:140:"Mandorle productions, en association avec Les Cris de Paris : Garance Roggero, Jeanne Lefèvre, Emma Forster, Antoine Boucon, Diane Geoffroy";s:14:"_credits_7_txt";s:39:"field_66a8fe3a1997d_field_66faa1ff17a7d";s:17:"credits_7_is_push";s:1:"0";s:18:"_credits_7_is_push";s:19:"field_66a8fe521997e";s:15:"credits_8_label";s:2:"40";s:16:"_credits_8_label";s:19:"field_66a8fe3a1997c";s:13:"credits_8_txt";s:18:"Maison de la danse";s:14:"_credits_8_txt";s:39:"field_66a8fe3a1997d_field_66faa1ff17a7d";s:17:"credits_8_is_push";s:1:"0";s:18:"_credits_8_is_push";s:19:"field_66a8fe521997e";s:15:"credits_9_label";s:2:"44";s:16:"_credits_9_label";s:19:"field_66a8fe3a1997c";s:13:"credits_9_txt";s:216:"Simon Bailly, Mario Barrantes-Espinoza, Evann Loget-Raymond, Florence Gengoul, Myriam Jarmache, Marie Picaut, Alan Picol, Antoine Roux-Briffaud, Vivien Simon, Maryfé Singy, Ryan Veillet, Aure Wachter, Daniel Wendler";s:14:"_credits_9_txt";s:39:"field_66a8fe3a1997d_field_66faa1ff17a7d";s:17:"credits_9_is_push";s:1:"0";s:18:"_credits_9_is_push";s:19:"field_66a8fe521997e";s:16:"credits_10_label";s:2:"48";s:17:"_credits_10_label";s:19:"field_66a8fe3a1997c";s:14:"credits_10_txt";s:10:"70 minutes";s:15:"_credits_10_txt";s:39:"field_66a8fe3a1997d_field_66faa1ff17a7d";s:18:"credits_10_is_push";s:1:"0";s:19:"_credits_10_is_push";s:19:"field_66a8fe521997e";s:16:"credits_11_label";s:2:"50";s:17:"_credits_11_label";s:19:"field_66a8fe3a1997c";s:14:"credits_11_txt";s:14:"Baudouin Woehl";s:15:"_credits_11_txt";s:39:"field_66a8fe3a1997d_field_66faa1ff17a7d";s:18:"credits_11_is_push";s:1:"0";s:19:"_credits_11_is_push";s:19:"field_66a8fe521997e";s:16:"credits_12_label";s:2:"39";s:17:"_credits_12_label";s:19:"field_66a8fe3a1997c";s:14:"credits_12_txt";s:4:"2024";s:15:"_credits_12_txt";s:39:"field_66a8fe3a1997d_field_66faa1ff17a7d";s:18:"credits_12_is_push";s:1:"0";s:19:"_credits_12_is_push";s:19:"field_66a8fe521997e";s:7:"credits";i:13;s:8:"_credits";s:19:"field_66a8fe3919979";s:20:"push_publication_ids";a:5:{i:0;s:3:"396";i:1;s:3:"409";i:2;s:3:"397";i:3;s:3:"407";i:4;s:3:"392";}s:21:"_push_publication_ids";s:19:"field_66ab4a146d939";s:13:"flexible_main";a:9:{i:0;s:13:"text-see-more";i:1;s:13:"text-see-more";i:2;s:13:"text-see-more";i:3;s:4:"text";i:4;s:5:"image";i:5;s:4:"text";i:6;s:12:"push-content";i:7;s:12:"push-content";i:8;s:13:"resource-list";}s:14:"_flexible_main";s:19:"field_flexible_main";s:13:"media_credits";s:63:"Crédits photo de couverture - Maguy Marin - Félix Ledru 2018.";s:14:"_media_credits";s:19:"field_66d6e096b567e";s:12:"presentation";s:1891:"Tumuls est une procession infinie de treize corps chantant et dansant au sein d’une seule et même pratique, un seul et même geste. Fruit d’une collaboration entre le chorégraphe François Chaignaud et Geoffroy Jourdain, directeur des Cris de Paris, ce projet porte le rêve d’éprouver conjointement ces deux arts, de penser ensemble danse et musique. Les tumulus étaient autrefois des tombes surmontées d’une colline. Sépultures d’un genre particulier, ces «  œuvres » contiennent autant le corps des défunts que la vie qui leur succède et pousse au-dessus d’eux.\r\n\r\nCillum aute aliquip in aute eiusmod proident consectetur culpa sit reprehenderit et aliqua qui. Ad pariatur est nulla occaecat sint laboris aute proident eiusmod eu culpa dolore occaecat. Sunt cillum irure sit commodo aliquip eiusmod. Exercitation proident occaecat ad velit ullamco do ullamco labore.\r\nNon cillum occaecat non excepteur sit. Fugiat esse aliquip amet ipsum aute. Do cupidatat enim ipsum pariatur nisi eu cillum aliquip reprehenderit ea culpa enim incididunt sit. In velit aliquip sit quis officia quis qui.\r\n\r\nElit consequat culpa nulla esse eu in sint magna. Sint aliquip proident fugiat tempor. Mollit officia ex enim aute et magna adipisicing reprehenderit laboris enim ullamco. Elit irure id sit nostrud quis nisi cillum magna nulla veniam fugiat nulla. Reprehenderit consectetur aute mollit aliqua veniam tempor. Proident nulla duis nisi irure nostrud adipisicing enim cupidatat nisi magna in consequat.\r\n\r\nSit mollit commodo irure laborum cillum ad sit. Mollit fugiat elit non Lorem eiusmod. Veniam id excepteur ullamco. In aliquip ullamco dolore quis nulla ut mollit aliqua reprehenderit incididunt cillum irure incididunt. Deserunt ad commodo ex consectetur nostrud proident laboris culpa. Qui duis ullamco fugiat cillum mollit laborum quis pariatur eu aliquip nisi excepteur.";s:13:"_presentation";s:19:"field_66a8fefadbb6a";s:36:"flexible_main_0_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_0_acfe_flexible_toggle";s:33:"field_layout_text-see-more_toggle";s:21:"flexible_main_0_title";s:5:"Krump";s:22:"_flexible_main_0_title";s:45:"field_clone_text-see-more_field_66fd5c2af2930";s:21:"flexible_main_0_infos";s:71:"Clément Cogitore, Les Indes Galantes\r\nNach, Nulle part est un endroit";s:22:"_flexible_main_0_infos";s:45:"field_clone_text-see-more_field_66fd5c3cf2931";s:19:"flexible_main_0_txt";s:2381:"En 2017, nombreux sont ceux qui découvrent le Krump par le biais du court métrage Les Indes Galantes. Le réalisateur Clément Cogitore et trois chorégraphes – Bintou Dembelé, Grichka Caruge et Brahim Rachiki – signent un film percutant mettant en scène, sur le plateau de l’Opéra de Bastille en un plan séquence, une vingtaine de danseurs issus de familles du krump français appelées « fam » et 40 figurants. Un battle d’une puissance irrésistible sur la musique de Jean-Philippe Rameau retravaillée avec des basses percussives, renforcées par les frappements de pieds des danseurs. Ce mouvement, le stomp, fait partie des pas de base. On reconnaı̂t aussi le chest pop (la poitrine se soulève vers le haut), le arm swing (les bras moulinent et miment le jet d’un projectile) et le jab (jet et arrêt du mouvement). Les visages accompagnent le sens de chaque mouvement : front plissé, langue tirée, bouche ouverte…chaque danseur développe un personnage, sorte d’avatar, à l’énergie et au style propres. On entend les cris qui encouragent l’autre à se surpasser : les danseurs « hypent » leurs acolytes au cœur du cercle.\r\n\r\nDans le premier volet de sa conférence dansée Nulle part est un endroit Nach raconte, en mouvements, sa rencontre fondatrice avec cette danse. Le Krump trouve ses racines dans les ghettos de Los Angeles dans les années 90. Dans un contexte de violences policières, le danseur Tommy le Clown anime les goûters d’anniversaire des enfants des quartiers défavorisés en se grimant et imaginant une danse hyper expressive pour les divertir et leur transmettre des valeurs positives. Dans la rue les jeunes s’approprient ce moyen d’expression et subliment leur rage en dansant au travers des battles. Parmi eux Tight Eyez et Big Mijo, figures majeures du développement du mouvement à travers le monde, apparaissent dans Rize de David Lachapelle. Le film documentaire provoque une déflagration chez les futurs Krumpers. Nach, comme Grichka un des pionniers du mouvement en France, le voit 5 fois et part se former auprès de ces danseurs à Los Angeles au début des années 2000. Le Krump lui permet de connecter avec d’autres cultures à travers le monde et ses créations sont aujourd’hui traversées par la danse contemporaine, le flamenco ou encore le bûto.";s:20:"_flexible_main_0_txt";s:45:"field_clone_text-see-more_field_66fd5bf677ffa";s:36:"flexible_main_1_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_1_acfe_flexible_toggle";s:33:"field_layout_text-see-more_toggle";s:21:"flexible_main_1_title";s:7:"Electro";s:22:"_flexible_main_1_title";s:45:"field_clone_text-see-more_field_66fd5c2af2930";s:21:"flexible_main_1_infos";s:40:"Le Fréquency\r\nMazel Freten, Rave Lucid";s:22:"_flexible_main_1_infos";s:45:"field_clone_text-see-more_field_66fd5c3cf2931";s:19:"flexible_main_1_txt";s:2091:"La danse electro c’est la première danse urbaine française. Elle naı̂t en 2006 dans les clubs parisiens et fait partie de ces danses indissociables du style musical sur laquelle elle se danse : l’électro house et la techno. On la reconnaı̂t à ses mouvements de bras lancés de façon ultra musicale. Les danseurs cherchent, avec leurs bras, leurs mains, à imiter les formes que peuvent prendre les « glow stick », ces bâtons qu’on craque pour faire de la lumière fluorescente.\r\n\r\nInspirée de mouvements du voguing, du locking, de la house et du popping elle se mêle de différentes techniques : le tetris, le flexing ou le bone breaking. Le mouvement se propage grâce à Youtube et à la technoparade de 2007. Youval, SteadyGun et Hagson font sortir la danse des clubs en créant le Battle Vertifight en 2007. A l’époque on compte 10 000 danseurs en France. En 2020 le danseur chorégraphe, DJ et activiste Achraf Bouchefour double champion du monde de danse électro lance les évènements Frequency dont le battle éponyme à la Gaı̂té Lyrique. Il devient un des rendez-vous incontournables d’une communauté mondiale et s’invite également à La Villette et plus récemment au Théâtre national de Chaillot.\r\n\r\nAchraf fait partie de Alliance Crew avec Brandon Masele dit Miel, autre ambassadeur de la danse électro française. Avec Laura Nala ils ont créé la compagnie Mazel Freten pour porter leurs projets de rencontre entre les cultures hip-hop et électro. En 2002 ils produisent la première création de danse electro pour 10 danseurs et danseuses : Rave Lucid. Au programme musique (très) rapide, dark techno et mouvements frénétiques.\r\n\r\nL’écriture de groupe est originale et d’une extrême précision : les grands ensembles sont hypnotiques et rappellent les ballets de danses classiques les plus rigoureux, où les fouettés seraient remplacés par les mouvements de bras. La musique originale de NikiT amplifie les battements de cœurs à l’unisson et les corps font caisse de résonance.";s:20:"_flexible_main_1_txt";s:45:"field_clone_text-see-more_field_66fd5bf677ffa";s:36:"flexible_main_6_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_6_acfe_flexible_toggle";s:32:"field_layout_push-content_toggle";s:23:"flexible_main_6_display";s:20:"devenez-contributeur";s:24:"_flexible_main_6_display";s:44:"field_clone_push-content_field_66d6eca69ad4b";s:21:"flexible_main_6_title";s:25:"Customus esse amet veniam";s:22:"_flexible_main_6_title";s:44:"field_clone_push-content_field_66d71f7e998c0";s:19:"flexible_main_6_txt";s:117:"Tempor labore pariatur labore pariatur enim amet. Laborum nostrud veniam reprehenderit fugiat tempor cupidatat id in.";s:20:"_flexible_main_6_txt";s:44:"field_clone_push-content_field_66d720142ab15";s:20:"flexible_main_6_link";a:4:{s:4:"type";s:4:"post";s:5:"value";s:3:"177";s:5:"title";s:17:"Customus buttonus";s:6:"target";b:0;}s:21:"_flexible_main_6_link";s:44:"field_clone_push-content_field_66d720352ab16";s:22:"flexible_main_6_img_id";i:486;s:23:"_flexible_main_6_img_id";s:44:"field_clone_push-content_field_66d7204f2ab17";s:36:"flexible_main_2_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_2_acfe_flexible_toggle";s:33:"field_layout_text-see-more_toggle";s:21:"flexible_main_2_title";s:0:"";s:22:"_flexible_main_2_title";s:45:"field_clone_text-see-more_field_66fd5c2af2930";s:21:"flexible_main_2_infos";s:0:"";s:22:"_flexible_main_2_infos";s:45:"field_clone_text-see-more_field_66fd5c3cf2931";s:19:"flexible_main_2_txt";s:937:"Nostrud laboris labore sint in veniam pariatur enim magna et sint incididunt dolore officia minim in. Lorem amet ex officia dolore non in quis adipisicing tempor do minim reprehenderit proident magna non. Veniam qui laborum excepteur. Quis laboris sit irure in.\r\n\r\nSint eu tempor voluptate labore adipisicing aliquip sint consectetur proident ipsum dolor ex. Cupidatat anim commodo occaecat fugiat ad veniam tempor veniam in velit. Ex est id voluptate nostrud irure aliquip. Quis excepteur ex officia officia labore excepteur consectetur est qui anim ut consectetur ut.\r\n\r\nVoluptate exercitation esse sunt. Aute nostrud proident est sit nisi mollit ad. Sint in ex incididunt consectetur. Nisi elit nisi reprehenderit. Ipsum esse veniam anim. Id ex quis ut et mollit ad laborum exercitation ipsum aliquip amet consequat occaecat esse ut. Consectetur Lorem eu est amet consequat exercitation occaecat velit ea do laborum culpa dolor nulla.";s:20:"_flexible_main_2_txt";s:45:"field_clone_text-see-more_field_66fd5bf677ffa";s:36:"flexible_main_3_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_3_acfe_flexible_toggle";s:24:"field_layout_text_toggle";s:19:"flexible_main_3_txt";s:6949:"<p class="chapo">Officia ipsum ipsum qui. Cupidatat adipisicing labore adipisicing pariatur pariatur fugiat aliquip et. Sit dolore sit ea nostrud consequat. Non magna non officia nostrud aliqua quis consectetur commodo voluptate cupidatat commodo officia cupidatat.</p>\r\n\r\n<h2>La course de la vie</h2>\r\nIl y a un lieu de naissance, autre qu’une ville. Toulouse. <a href="#">Un emplacement atteint</a> suite à une série de déplacements provoqués par des mouvements politiques en Espagne. Ainsi, grandir par là, en France, au tout début des années 50. Puis il y a un désir de danser qui se confirme par un enchaînement d’études - de Toulouse, à Strasbourg puis à Mudra (Bruxelles) Maurice Béjart, Alfons Goris et Fernand Schirren ... dans lequel se manifestent déjà des rencontres : les étudiants acteurs du <strong>Théâtre National de Strasbourg</strong>. Une volonté qui s’affirme avec le groupe Chandra puis au Ballet du XXème siècle. Le travail de création s’amorce aux côtés de Daniel Ambash, et les concours de Nyon et de Bagnolet (1978) viennent appuyer cet élan.\r\n<h2>Faire à plusieurs</h2>\r\nDe 1980 à 1990, portée par la confiance de l’équipe de la Maison des arts de Créteil, la recherche se poursuit avec Christiane Glik, Luna Bloomfield, Mychel Lecoq et la complicité de Montserrat Casanova. Une troupe se constitue renforcée par Cathy Polo, Françoise Leick, Ulises Alvarez, Teresa Cunha, et bien d’autres encore. Chercher toujours, avec une composante, une compagnie qui deviendra en 1985 le <em>Centre chorégraphique national de Créteil et du Val-de-Marne</em>. Une tentative de travailler à plusieurs et pouvoir en vivre, soutenue par une intense diffusion de par le monde. En 1987, la rencontre avec Denis Mariotte amorce une longue collaboration qui ouvre le champ des expériences par un questionnement mutuel hors des cadres d’un champ artistique spécifique.\r\n\r\n<a href="#" class="btn">Lien bouton</a>\r\n<h3>Reprehenderit adipisicing ut cillum anim</h3>\r\nTempor et laboris nostrud nostrud aliquip eiusmod ad exercitation qui. Pariatur esse minim excepteur amet pariatur ex fugiat laboris non adipisicing commodo occaecat. Velit commodo proident fugiat proident sunt ullamco officia laborum tempor sint incididunt. Laborum aliqua irure adipisicing veniam enim deserunt ullamco consequat dolor nisi. Consequat nisi mollit esse sunt proident aute minim officia officia dolore cillum aliqua. Aliquip nostrud reprehenderit tempor ipsum labore cupidatat veniam aliquip. Exercitation laboris adipisicing sit. Et et nostrud quis tempor consequat ipsum esse pariatur aute duis sit.\r\n<ul>\r\n 	<li>Non labore consectetur minim ex sit non quis eu quis.</li>\r\n 	<li>Dolore ad anim dolore ullamco fugiat id.</li>\r\n 	<li>Ullamco elit ad dolore irure aliquip irure Lorem.</li>\r\n 	<li>Enim sit velit est excepteur proident minim enim in ea ipsum aliquip qui.</li>\r\n 	<li>Dolore qui ex ex nulla culpa culpa enim consectetur proident.</li>\r\n</ul>\r\nMinim Lorem laborum id eiusmod quis magna esse irure magna culpa incididunt sit do sit est. Elit est quis sunt. Quis id adipisicing excepteur ad magna sint fugiat nostrud culpa. Amet pariatur enim elit ullamco esse magna magna nostrud esse. Labore nisi ea laborum eu voluptate ullamco minim ullamco occaecat. Mollit eu quis dolor exercitation commodo culpa excepteur in adipisicing magna aliquip nostrud pariatur voluptate. Consectetur pariatur et ut cillum tempor do ut qui duis irure. Et aute consectetur commodo aliquip minim mollit anim ad consequat incididunt cupidatat anim.\r\n<h3>Veniam aliqua proident qui officia sint deserunt</h3>\r\nDolore laborum non consectetur magna elit. Magna officia labore dolor ea ad elit minim aute nulla veniam aliquip labore qui non.\r\n<ul class="list_check">\r\n 	<li>Non labore consectetur minim ex sit non quis eu quis.</li>\r\n 	<li>Dolore ad anim dolore ullamco fugiat id.</li>\r\n 	<li>Ullamco elit ad dolore irure aliquip irure Lorem.</li>\r\n 	<li>Enim sit velit est excepteur proident minim enim in ea ipsum aliquip qui.</li>\r\n 	<li>Dolore qui ex ex nulla culpa culpa enim consectetur proident.</li>\r\n</ul>\r\nNostrud exercitation esse mollit veniam occaecat sit veniam nulla eiusmod esse sint nostrud ad cillum. Culpa nisi velit in pariatur proident amet velit amet est mollit consectetur. Incididunt laborum duis irure ea magna officia sint. Mollit ea voluptate est eiusmod mollit fugiat ex quis dolor in officia ex. Labore laboris ad in ad voluptate laboris sunt aute cillum qui labore non tempor reprehenderit ex. Adipisicing exercitation aliqua minim adipisicing culpa. Veniam sunt aliqua fugiat labore pariatur officia ex officia.\r\n<ol>\r\n 	<li>Sit do aliquip et. Ullamco id fugiat nulla fugiat eu magna sit do consectetur in tempor culpa.</li>\r\n 	<li>Est dolore cupidatat in commodo est ea anim reprehenderit proident ad proident aliqua voluptate consectetur nulla. In in sunt magna.</li>\r\n 	<li>Do et anim commodo fugiat labore quis sit consectetur.</li>\r\n 	<li>Deserunt elit elit elit Lorem ullamco pariatur est excepteur velit.</li>\r\n 	<li>Et in sunt aliqua veniam consectetur tempor anim duis aute veniam mollit sunt dolore aliqua labore.</li>\r\n</ol>\r\n<h2>Faire - Défaire - Refaire</h2>\r\n<strong>1998, une nouvelle implantation.</strong>\r\nUn nouveau territoire pour un nouveau Centre chorégraphique national à Rillieux-la-Pape, dans le quartier de la Velette. Avec la nécessité de reprendre place dans l’espace public. Un croisement de présences qui agit dans un espace commun : Un « nous, en temps et lieu ». Ainsi chercher en ce lieu la distance nécessaire pour renforcer notre capacité à faire surgir « ces forces diagonales résistantes à l’oubli » (H. Arendt).\r\n\r\nLe travail se poursuit dans une pluralité de territoires - du Studio, au quartier de la Velette, aux villes partenaires, jusqu’aux villes d’autres pays. Un travail où s’entremêlent des créations, des interventions multiples où l’exigence artistique ouvre des pistes qui dépassent le désir convivial immédiat d’un être ensemble.\r\n<p class="quote">Parfois, la danse est un défi, un combat contre la douleur et la fatigue. Mais lorsque la dernière note s\'éteint et que les applaudissements résonnent, tout est oublié. Il n\'y a plus que la joie d\'avoir partagé mon art et l\'immense satisfaction d\'avoir donné le meilleur de moi-même.</p>\r\nIn elit labore sint aliquip ea. Dolore magna et et nostrud mollit occaecat officia cillum irure non proident laborum. Consectetur fugiat ex anim duis consequat voluptate laboris proident nostrud ea incididunt nisi ipsum do. Eiusmod aute occaecat sunt elit id Lorem eu consectetur. Voluptate et anim deserunt eu voluptate non occaecat dolore aliquip ullamco. Veniam do laboris ad voluptate eiusmod nulla. Laboris ex cupidatat aliqua occaecat duis adipisicing cupidatat voluptate dolor magna mollit nulla.";s:20:"_flexible_main_3_txt";s:36:"field_clone_text_field_63791defce143";s:36:"flexible_main_4_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_4_acfe_flexible_toggle";s:25:"field_layout_image_toggle";s:22:"flexible_main_4_img_id";i:446;s:23:"_flexible_main_4_img_id";s:37:"field_clone_image_field_637920176370f";s:23:"flexible_main_4_caption";s:65:"© Représentation de May B à la Maison de la Danse en 2018.";s:24:"_flexible_main_4_caption";s:37:"field_clone_image_field_66d5cca991a07";s:36:"flexible_main_5_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_5_acfe_flexible_toggle";s:24:"field_layout_text_toggle";s:19:"flexible_main_5_txt";s:914:"<h2>Faire - Défaire - Refaire</h2>\r\n<p class="chapo">1998, une nouvelle implantation.</p>\r\nUn nouveau territoire pour un nouveau Centre chorégraphique national à Rillieux-la-Pape, dans le quartier de la Velette. Avec la nécessité de reprendre place dans l’espace public. Un croisement de présences qui agit dans un espace commun : Un « nous, en temps et lieu ». Ainsi chercher en ce lieu la distance nécessaire pour renforcer notre capacité à faire surgir <em>« ces forces diagonales résistantes à l’oubli »</em> (H. Arendt).\r\n\r\nLe travail se poursuit dans une pluralité de territoires - du Studio, au quartier de la Velette, aux villes partenaires, jusqu’aux villes d’autres pays. Un travail où s’entremêlent des créations, des interventions multiples où l’exigence artistique ouvre des pistes qui dépassent le désir convivial immédiat d’un être ensemble.";s:20:"_flexible_main_5_txt";s:36:"field_clone_text_field_63791defce143";s:36:"flexible_main_7_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_7_acfe_flexible_toggle";s:32:"field_layout_push-content_toggle";s:23:"flexible_main_7_display";s:6:"custom";s:24:"_flexible_main_7_display";s:44:"field_clone_push-content_field_66d6eca69ad4b";s:21:"flexible_main_7_title";s:25:"Customus esse amet veniam";s:22:"_flexible_main_7_title";s:44:"field_clone_push-content_field_66d71f7e998c0";s:19:"flexible_main_7_txt";s:117:"Tempor labore pariatur labore pariatur enim amet. Laborum nostrud veniam reprehenderit fugiat tempor cupidatat id in.";s:20:"_flexible_main_7_txt";s:44:"field_clone_push-content_field_66d720142ab15";s:20:"flexible_main_7_link";a:4:{s:4:"type";s:4:"post";s:5:"value";s:3:"177";s:5:"title";s:17:"Customus buttonus";s:6:"target";b:0;}s:21:"_flexible_main_7_link";s:44:"field_clone_push-content_field_66d720352ab16";s:22:"flexible_main_7_img_id";i:486;s:23:"_flexible_main_7_img_id";s:44:"field_clone_push-content_field_66d7204f2ab17";s:36:"flexible_main_8_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_8_acfe_flexible_toggle";s:33:"field_layout_resource-list_toggle";s:28:"flexible_main_8_list_0_title";s:29:"Ouvrir la fiche présentation";s:29:"_flexible_main_8_list_0_title";s:19:"field_66d5d9b6e6cc9";s:27:"flexible_main_8_list_0_type";s:4:"file";s:28:"_flexible_main_8_list_0_type";s:19:"field_66d5d78163bf7";s:30:"flexible_main_8_list_0_file_id";i:456;s:31:"_flexible_main_8_list_0_file_id";s:19:"field_66d6c062641e1";s:28:"flexible_main_8_list_1_title";s:36:"Télécharger la fiche présentation";s:29:"_flexible_main_8_list_1_title";s:19:"field_66d5d9b6e6cc9";s:27:"flexible_main_8_list_1_type";s:7:"dl_file";s:28:"_flexible_main_8_list_1_type";s:19:"field_66d5d78163bf7";s:33:"flexible_main_8_list_1_dl_file_id";i:457;s:34:"_flexible_main_8_list_1_dl_file_id";s:19:"field_66d5d88950061";s:28:"flexible_main_8_list_2_title";s:30:"En savoir plus sur Maguy Marin";s:29:"_flexible_main_8_list_2_title";s:19:"field_66d5d9b6e6cc9";s:27:"flexible_main_8_list_2_type";s:13:"external_link";s:28:"_flexible_main_8_list_2_type";s:19:"field_66d5d78163bf7";s:36:"flexible_main_8_list_2_external_link";s:32:"https://compagnie-maguy-marin.fr";s:37:"_flexible_main_8_list_2_external_link";s:19:"field_66d5d86250060";s:20:"flexible_main_8_list";i:3;s:21:"_flexible_main_8_list";s:45:"field_clone_resource-list_field_66d5d6bff7753";}'),
(661, 470, '_wp_attached_file', '2024/09/img_2-temp.jpg'),
(662, 470, '_wp_attachment_metadata', 'a:6:{s:5:"width";i:2624;s:6:"height";i:768;s:4:"file";s:22:"2024/09/img_2-temp.jpg";s:8:"filesize";i:713375;s:5:"sizes";a:1:{s:9:"thumbnail";a:5:{s:4:"file";s:22:"img_2-temp-256x256.jpg";s:5:"width";i:256;s:6:"height";i:256;s:9:"mime-type";s:10:"image/jpeg";s:8:"filesize";i:5109;}}s:10:"image_meta";a:12:{s:8:"aperture";s:1:"0";s:6:"credit";s:0:"";s:6:"camera";s:0:"";s:7:"caption";s:0:"";s:17:"created_timestamp";s:1:"0";s:9:"copyright";s:0:"";s:12:"focal_length";s:1:"0";s:3:"iso";s:1:"0";s:13:"shutter_speed";s:1:"0";s:5:"title";s:0:"";s:11:"orientation";s:1:"0";s:8:"keywords";a:0:{}}}'),
(668, 486, '_wp_attached_file', '2024/07/img_3-temp.jpg'),
(669, 486, '_wp_attachment_metadata', 'a:6:{s:5:"width";i:2880;s:6:"height";i:1296;s:4:"file";s:22:"2024/07/img_3-temp.jpg";s:8:"filesize";i:861977;s:5:"sizes";a:1:{s:9:"thumbnail";a:5:{s:4:"file";s:22:"img_3-temp-256x256.jpg";s:5:"width";i:256;s:6:"height";i:256;s:9:"mime-type";s:10:"image/jpeg";s:8:"filesize";i:15112;}}s:10:"image_meta";a:12:{s:8:"aperture";s:1:"0";s:6:"credit";s:0:"";s:6:"camera";s:0:"";s:7:"caption";s:0:"";s:17:"created_timestamp";s:1:"0";s:9:"copyright";s:0:"";s:12:"focal_length";s:1:"0";s:3:"iso";s:1:"0";s:13:"shutter_speed";s:1:"0";s:5:"title";s:0:"";s:11:"orientation";s:1:"0";s:8:"keywords";a:0:{}}}'),
(674, 492, '_wp_attached_file', '2024/09/img_4-temp.jpg'),
(675, 492, '_wp_attachment_metadata', 'a:6:{s:5:"width";i:2880;s:6:"height";i:1296;s:4:"file";s:22:"2024/09/img_4-temp.jpg";s:8:"filesize";i:1386071;s:5:"sizes";a:1:{s:9:"thumbnail";a:5:{s:4:"file";s:22:"img_4-temp-256x256.jpg";s:5:"width";i:256;s:6:"height";i:256;s:9:"mime-type";s:10:"image/jpeg";s:8:"filesize";i:18144;}}s:10:"image_meta";a:12:{s:8:"aperture";s:1:"0";s:6:"credit";s:0:"";s:6:"camera";s:0:"";s:7:"caption";s:0:"";s:17:"created_timestamp";s:1:"0";s:9:"copyright";s:0:"";s:12:"focal_length";s:1:"0";s:3:"iso";s:1:"0";s:13:"shutter_speed";s:1:"0";s:5:"title";s:0:"";s:11:"orientation";s:1:"0";s:8:"keywords";a:0:{}}}'),
(677, 396, 'push_to_honor', '0'),
(678, 396, '_push_to_honor', 'field_66b49a8116fae'),
(682, 395, 'push_to_honor', '0'),
(683, 395, '_push_to_honor', 'field_66b49a8116fae'),
(698, 405, 'push_to_novelty', '1'),
(699, 405, '_push_to_novelty', 'field_66a9000a300c6'),
(700, 405, 'push_to_honor', '1'),
(701, 405, '_push_to_honor', 'field_66b49a8116fae'),
(702, 405, 'acf', 'a:42:{s:8:"category";s:2:"13";s:9:"_category";s:19:"field_66a8f6cbc64fa";s:6:"format";s:2:"15";s:7:"_format";s:19:"field_66aa544be2232";s:10:"chronology";s:2:"20";s:11:"_chronology";s:19:"field_66aa5536cd104";s:10:"dance_type";s:2:"29";s:11:"_dance_type";s:19:"field_66aa5595cd105";s:5:"piece";s:2:"60";s:6:"_piece";s:19:"field_66aa55b1cd106";s:3:"tag";a:2:{i:0;s:2:"54";i:1;s:2:"55";}s:4:"_tag";s:19:"field_66aa55d5cd107";s:15:"push_to_novelty";s:1:"1";s:16:"_push_to_novelty";s:19:"field_66a9000a300c6";s:13:"push_to_honor";s:1:"1";s:14:"_push_to_honor";s:19:"field_66b49a8116fae";s:11:"member_only";s:1:"1";s:12:"_member_only";s:19:"field_66a8ffe7300c5";s:10:"date_start";s:8:"20241209";s:11:"_date_start";s:19:"field_66a9e87cee3cd";s:8:"date_end";s:8:"20241215";s:9:"_date_end";s:19:"field_66a9e87cee3cd";s:4:"date";b:0;s:5:"_date";s:19:"field_66a9e87cee3cd";s:10:"media_type";s:5:"video";s:11:"_media_type";s:19:"field_66aa523327c03";s:14:"media_video_id";s:4:"1312";s:15:"_media_video_id";s:19:"field_66aa52e5a3b99";s:13:"media_credits";s:0:"";s:14:"_media_credits";s:19:"field_66d6e096b567e";s:8:"subtitle";s:0:"";s:9:"_subtitle";s:19:"field_66a8f9ae4b6ed";s:3:"txt";s:0:"";s:4:"_txt";s:19:"field_66a8fefadbb6a";s:9:"main_data";s:0:"";s:10:"_main_data";s:19:"field_66a8f9c84b6ee";s:7:"credits";s:0:"";s:8:"_credits";s:19:"field_66a8fe3919979";s:20:"push_publication_ids";s:0:"";s:21:"_push_publication_ids";s:19:"field_66ab4a146d939";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(703, 395, 'acf', 'a:44:{s:8:"category";s:2:"13";s:9:"_category";s:19:"field_66a8f6cbc64fa";s:6:"format";s:2:"15";s:7:"_format";s:19:"field_66aa544be2232";s:10:"chronology";s:2:"18";s:11:"_chronology";s:19:"field_66aa5536cd104";s:10:"dance_type";s:2:"28";s:11:"_dance_type";s:19:"field_66aa5595cd105";s:5:"piece";s:0:"";s:6:"_piece";s:19:"field_66aa55b1cd106";s:3:"tag";s:0:"";s:4:"_tag";s:19:"field_66aa55d5cd107";s:15:"push_to_novelty";s:1:"0";s:16:"_push_to_novelty";s:19:"field_66a9000a300c6";s:13:"push_to_honor";s:1:"0";s:14:"_push_to_honor";s:19:"field_66b49a8116fae";s:11:"member_only";s:1:"0";s:12:"_member_only";s:19:"field_66a8ffe7300c5";s:10:"date_start";s:8:"20240916";s:11:"_date_start";s:19:"field_66a9e87cee3cd";s:8:"date_end";s:8:"20241011";s:9:"_date_end";s:19:"field_66a9e87cee3cd";s:4:"date";b:0;s:5:"_date";s:19:"field_66a9e87cee3cd";s:10:"media_type";s:5:"video";s:11:"_media_type";s:19:"field_66aa523327c03";s:14:"media_video_id";s:9:"242893245";s:15:"_media_video_id";s:19:"field_66aa52e5a3b99";s:13:"media_credits";s:0:"";s:14:"_media_credits";s:19:"field_66d6e096b567e";s:8:"subtitle";s:105:"Sint do nulla aliqua aute velit nulla reprehenderit sit qui amet dolor aliquip incididunt culpa excepteur";s:9:"_subtitle";s:19:"field_66a8f9ae4b6ed";s:3:"txt";s:522:"Quis reprehenderit laborum ullamco elit elit non do et deserunt ut consequat est culpa. Deserunt aute nulla in culpa esse exercitation nostrud ut. Excepteur reprehenderit nostrud Lorem duis incididunt minim amet. Commodo consequat Lorem id est esse irure commodo pariatur Lorem reprehenderit do exercitation nisi ex est. Nostrud deserunt qui laborum id ex ipsum proident. Exercitation nisi commodo duis velit quis incididunt. Duis cupidatat deserunt voluptate incididunt Lorem Lorem est exercitation sunt nisi nulla nulla.";s:4:"_txt";s:19:"field_66a8fefadbb6a";s:9:"main_data";s:0:"";s:10:"_main_data";s:19:"field_66a8f9c84b6ee";s:7:"credits";s:0:"";s:8:"_credits";s:19:"field_66a8fe3919979";s:20:"push_publication_ids";s:0:"";s:21:"_push_publication_ids";s:19:"field_66ab4a146d939";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";s:12:"presentation";s:449:"Elit ullamco ipsum laborum non fugiat nisi cupidatat tempor cillum laboris eu do enim voluptate. Eu enim duis deserunt. Lorem laboris Lorem id incididunt velit dolor tempor commodo aliqua aliquip. Labore est proident commodo qui tempor aute fugiat esse.\r\n\r\nLabore officia excepteur dolore cillum voluptate fugiat culpa nostrud proident id esse cillum. Nulla tempor deserunt labore velit laboris nostrud duis laborum labore cillum enim voluptate est.";s:13:"_presentation";s:19:"field_66a8fefadbb6a";}'),
(713, 392, 'push_to_honor', '0'),
(714, 392, '_push_to_honor', 'field_66b49a8116fae'),
(727, 540, '_wp_attached_file', '2024/08/logo_1-temp.jpg'),
(728, 540, '_wp_attachment_metadata', 'a:6:{s:5:"width";i:678;s:6:"height";i:480;s:4:"file";s:23:"2024/08/logo_1-temp.jpg";s:8:"filesize";i:60404;s:5:"sizes";a:1:{s:9:"thumbnail";a:5:{s:4:"file";s:23:"logo_1-temp-256x256.jpg";s:5:"width";i:256;s:6:"height";i:256;s:9:"mime-type";s:10:"image/jpeg";s:8:"filesize";i:9531;}}s:10:"image_meta";a:12:{s:8:"aperture";s:1:"0";s:6:"credit";s:0:"";s:6:"camera";s:0:"";s:7:"caption";s:0:"";s:17:"created_timestamp";s:1:"0";s:9:"copyright";s:0:"";s:12:"focal_length";s:1:"0";s:3:"iso";s:1:"0";s:13:"shutter_speed";s:1:"0";s:5:"title";s:0:"";s:11:"orientation";s:1:"0";s:8:"keywords";a:0:{}}}'),
(729, 541, '_wp_attached_file', '2024/08/logo_2-temp.jpg'),
(730, 541, '_wp_attachment_metadata', 'a:6:{s:5:"width";i:678;s:6:"height";i:480;s:4:"file";s:23:"2024/08/logo_2-temp.jpg";s:8:"filesize";i:36563;s:5:"sizes";a:1:{s:9:"thumbnail";a:5:{s:4:"file";s:23:"logo_2-temp-256x256.jpg";s:5:"width";i:256;s:6:"height";i:256;s:9:"mime-type";s:10:"image/jpeg";s:8:"filesize";i:6531;}}s:10:"image_meta";a:12:{s:8:"aperture";s:1:"0";s:6:"credit";s:0:"";s:6:"camera";s:0:"";s:7:"caption";s:0:"";s:17:"created_timestamp";s:1:"0";s:9:"copyright";s:0:"";s:12:"focal_length";s:1:"0";s:3:"iso";s:1:"0";s:13:"shutter_speed";s:1:"0";s:5:"title";s:0:"";s:11:"orientation";s:1:"0";s:8:"keywords";a:0:{}}}'),
(731, 542, '_wp_attached_file', '2024/08/logo_3-temp.jpg'),
(732, 542, '_wp_attachment_metadata', 'a:6:{s:5:"width";i:678;s:6:"height";i:480;s:4:"file";s:23:"2024/08/logo_3-temp.jpg";s:8:"filesize";i:54492;s:5:"sizes";a:1:{s:9:"thumbnail";a:5:{s:4:"file";s:23:"logo_3-temp-256x256.jpg";s:5:"width";i:256;s:6:"height";i:256;s:9:"mime-type";s:10:"image/jpeg";s:8:"filesize";i:8930;}}s:10:"image_meta";a:12:{s:8:"aperture";s:1:"0";s:6:"credit";s:0:"";s:6:"camera";s:0:"";s:7:"caption";s:0:"";s:17:"created_timestamp";s:1:"0";s:9:"copyright";s:0:"";s:12:"focal_length";s:1:"0";s:3:"iso";s:1:"0";s:13:"shutter_speed";s:1:"0";s:5:"title";s:0:"";s:11:"orientation";s:1:"0";s:8:"keywords";a:0:{}}}'),
(733, 543, '_wp_attached_file', '2024/08/logo_4-temp.jpg'),
(734, 543, '_wp_attachment_metadata', 'a:6:{s:5:"width";i:678;s:6:"height";i:480;s:4:"file";s:23:"2024/08/logo_4-temp.jpg";s:8:"filesize";i:57410;s:5:"sizes";a:1:{s:9:"thumbnail";a:5:{s:4:"file";s:23:"logo_4-temp-256x256.jpg";s:5:"width";i:256;s:6:"height";i:256;s:9:"mime-type";s:10:"image/jpeg";s:8:"filesize";i:9068;}}s:10:"image_meta";a:12:{s:8:"aperture";s:1:"0";s:6:"credit";s:0:"";s:6:"camera";s:0:"";s:7:"caption";s:0:"";s:17:"created_timestamp";s:1:"0";s:9:"copyright";s:0:"";s:12:"focal_length";s:1:"0";s:3:"iso";s:1:"0";s:13:"shutter_speed";s:1:"0";s:5:"title";s:0:"";s:11:"orientation";s:1:"0";s:8:"keywords";a:0:{}}}'),
(735, 544, '_wp_attached_file', '2024/08/logo_5-temp.jpg'),
(736, 544, '_wp_attachment_metadata', 'a:6:{s:5:"width";i:678;s:6:"height";i:480;s:4:"file";s:23:"2024/08/logo_5-temp.jpg";s:8:"filesize";i:58454;s:5:"sizes";a:1:{s:9:"thumbnail";a:5:{s:4:"file";s:23:"logo_5-temp-256x256.jpg";s:5:"width";i:256;s:6:"height";i:256;s:9:"mime-type";s:10:"image/jpeg";s:8:"filesize";i:7641;}}s:10:"image_meta";a:12:{s:8:"aperture";s:1:"0";s:6:"credit";s:0:"";s:6:"camera";s:0:"";s:7:"caption";s:0:"";s:17:"created_timestamp";s:1:"0";s:9:"copyright";s:0:"";s:12:"focal_length";s:1:"0";s:3:"iso";s:1:"0";s:13:"shutter_speed";s:1:"0";s:5:"title";s:0:"";s:11:"orientation";s:1:"0";s:8:"keywords";a:0:{}}}'),
(741, 549, '_edit_lock', '1727259784:1'),
(742, 549, '_edit_last', '1'),
(743, 549, 'acf', 'a:8:{s:6:"parent";s:4:"user";s:7:"_parent";s:19:"field_66ab79834b31b";s:14:"parent_user_id";i:3;s:15:"_parent_user_id";s:19:"field_66ab7c882004e";s:10:"intro_desc";s:501:"Minim ullamco aliquip excepteur et magna sint est do. Non est mollit duis qui et reprehenderit proident quis esse mollit incididunt culpa minim. Veniam sint Lorem qui deserunt eiusmod elit pariatur quis veniam excepteur esse et exercitation enim adipisicing. Ut eiusmod ut sunt elit reprehenderit et mollit amet dolore id do duis enim sit. Dolor laboris aute aliqua ut non cillum. Esse consequat consectetur sint sunt nisi. Occaecat sit qui est exercitation excepteur officia enim. Ea excepteur in do.";s:11:"_intro_desc";s:19:"field_66ab79264b319";s:15:"publication_ids";a:4:{i:0;s:3:"404";i:1;s:3:"411";i:2;s:3:"224";i:3;s:3:"407";}s:16:"_publication_ids";s:19:"field_66ab7aa343c01";}'),
(765, 573, '_edit_lock', '1727685780:1'),
(766, 573, '_edit_last', '1'),
(767, 573, 'acf', 'a:6:{s:12:"intro_img_id";i:492;s:13:"_intro_img_id";s:19:"field_66ab762b3bc67";s:12:"playlist_ids";s:0:"";s:13:"_playlist_ids";s:19:"field_66ab76713bc69";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(771, 579, '_edit_lock', '1727690478:1'),
(772, 579, '_edit_last', '1'),
(773, 579, 'acf', 'a:6:{s:14:"intro_subtitle";s:0:"";s:15:"_intro_subtitle";s:19:"field_66ab9c90a215d";s:12:"intro_img_id";i:446;s:13:"_intro_img_id";s:19:"field_66ab9c5ba215c";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(775, 580, '_edit_lock', '1727690477:1'),
(776, 580, '_edit_last', '1'),
(777, 581, '_edit_lock', '1727690475:1'),
(778, 580, 'acf', 'a:6:{s:14:"intro_subtitle";s:0:"";s:15:"_intro_subtitle";s:19:"field_66ab9c90a215d";s:12:"intro_img_id";i:486;s:13:"_intro_img_id";s:19:"field_66ab9c5ba215c";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(780, 581, '_edit_last', '1'),
(781, 581, 'acf', 'a:6:{s:14:"intro_subtitle";s:0:"";s:15:"_intro_subtitle";s:19:"field_66ab9c90a215d";s:12:"intro_img_id";i:470;s:13:"_intro_img_id";s:19:"field_66ab9c5ba215c";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(783, 585, '_edit_lock', '1727690531:1'),
(784, 585, '_edit_last', '1'),
(785, 585, 'acf', 'a:6:{s:14:"intro_subtitle";s:0:"";s:15:"_intro_subtitle";s:19:"field_66ab9c90a215d";s:12:"intro_img_id";i:492;s:13:"_intro_img_id";s:19:"field_66ab9c5ba215c";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(787, 586, '_edit_lock', '1727690599:1'),
(788, 586, '_edit_last', '1'),
(789, 587, '_edit_lock', '1727690593:1'),
(790, 586, 'acf', 'a:6:{s:14:"intro_subtitle";s:0:"";s:15:"_intro_subtitle";s:19:"field_66ab9c90a215d";s:12:"intro_img_id";i:446;s:13:"_intro_img_id";s:19:"field_66ab9c5ba215c";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}'),
(792, 587, '_edit_last', '1'),
(793, 587, 'acf', 'a:6:{s:14:"intro_subtitle";s:0:"";s:15:"_intro_subtitle";s:19:"field_66ab9c90a215d";s:12:"intro_img_id";i:446;s:13:"_intro_img_id";s:19:"field_66ab9c5ba215c";s:13:"flexible_main";s:0:"";s:14:"_flexible_main";s:19:"field_flexible_main";}') ;
INSERT INTO `nd_postmeta` ( `meta_id`, `post_id`, `meta_key`, `meta_value`) VALUES
(809, 392, 'acf', 'a:76:{s:8:"category";s:2:"11";s:9:"_category";s:19:"field_66a8f6cbc64fa";s:6:"format";s:2:"14";s:7:"_format";s:19:"field_66aa544be2232";s:10:"chronology";s:2:"23";s:11:"_chronology";s:19:"field_66aa5536cd104";s:10:"dance_type";s:2:"29";s:11:"_dance_type";s:19:"field_66aa5595cd105";s:5:"piece";s:2:"60";s:6:"_piece";s:19:"field_66aa55b1cd106";s:3:"tag";a:1:{i:0;s:2:"58";}s:4:"_tag";s:19:"field_66aa55d5cd107";s:15:"push_to_novelty";s:1:"1";s:16:"_push_to_novelty";s:19:"field_66a9000a300c6";s:13:"push_to_honor";s:1:"0";s:14:"_push_to_honor";s:19:"field_66b49a8116fae";s:11:"member_only";s:1:"0";s:12:"_member_only";s:19:"field_66a8ffe7300c5";s:10:"media_type";s:5:"video";s:11:"_media_type";s:19:"field_66aa523327c03";s:14:"media_video_id";s:9:"232791634";s:15:"_media_video_id";s:19:"field_66aa52e5a3b99";s:13:"media_credits";s:0:"";s:14:"_media_credits";s:19:"field_66d6e096b567e";s:8:"subtitle";s:35:"Exemple d’un sous-titre optionnel";s:9:"_subtitle";s:19:"field_66a8f9ae4b6ed";s:12:"presentation";s:1200:"Commodo ad ut ea laboris incididunt non do dolor do. Ullamco fugiat esse voluptate. Non eu cupidatat consequat labore consequat ex. Anim ut et proident officia aliqua enim excepteur. Ex magna irure reprehenderit dolor et et voluptate in et sit voluptate velit culpa ea. Commodo ad sint sint anim ex eu velit reprehenderit ullamco consequat deserunt dolore irure ut.\r\n\r\nOfficia consequat ipsum eiusmod nostrud tempor laborum magna Lorem nulla sit sit Lorem aute cupidatat. Et consectetur veniam cupidatat culpa laborum voluptate aute sint esse adipisicing labore occaecat. Ut reprehenderit consectetur amet proident ex irure et sunt veniam sunt qui nisi anim sint. Dolore commodo laborum incididunt ea nostrud. Nostrud fugiat eiusmod non sint eu nisi ullamco. Reprehenderit aliquip sunt fugiat aliquip exercitation non quis nostrud aliquip.\r\n\r\nEx dolore esse dolor labore laboris nostrud velit aliqua adipisicing aliqua duis. Mollit aliqua aliquip anim Lorem quis aliqua fugiat est. Laboris dolor aliquip Lorem. Proident do cillum irure esse incididunt ullamco ut ex duis ut ex ea dolor laboris. Adipisicing dolore eiusmod ex. Reprehenderit laborum mollit minim est enim ullamco excepteur magna nulla.";s:13:"_presentation";s:19:"field_66a8fefadbb6a";s:17:"main_data_0_label";s:2:"41";s:18:"_main_data_0_label";s:19:"field_66a8f9ec4b6ef";s:15:"main_data_0_txt";s:84:"[lesanimals_rich_link type="collection" collection-id="573" post-name="Numeridanse"]";s:16:"_main_data_0_txt";s:39:"field_66a8fa234b6f0_field_66faa1ff17a7d";s:17:"main_data_1_label";s:2:"40";s:18:"_main_data_1_label";s:19:"field_66a8f9ec4b6ef";s:15:"main_data_1_txt";s:84:"[lesanimals_rich_link type="collection" collection-id="573" post-name="Numeridanse"]";s:16:"_main_data_1_txt";s:39:"field_66a8fa234b6f0_field_66faa1ff17a7d";s:17:"main_data_2_label";s:2:"38";s:18:"_main_data_2_label";s:19:"field_66a8f9ec4b6ef";s:15:"main_data_2_txt";s:4:"2024";s:16:"_main_data_2_txt";s:39:"field_66a8fa234b6f0_field_66faa1ff17a7d";s:17:"main_data_3_label";s:2:"39";s:18:"_main_data_3_label";s:19:"field_66a8f9ec4b6ef";s:15:"main_data_3_txt";s:4:"2019";s:16:"_main_data_3_txt";s:39:"field_66a8fa234b6f0_field_66faa1ff17a7d";s:9:"main_data";i:4;s:10:"_main_data";s:19:"field_66a8f9c84b6ee";s:15:"credits_0_label";s:2:"39";s:16:"_credits_0_label";s:19:"field_66a8fe3a1997c";s:13:"credits_0_txt";s:4:"2008";s:14:"_credits_0_txt";s:39:"field_66a8fe3a1997d_field_66faa1ff17a7d";s:17:"credits_0_is_push";s:1:"0";s:18:"_credits_0_is_push";s:19:"field_66a8fe521997e";s:7:"credits";i:1;s:8:"_credits";s:19:"field_66a8fe3919979";s:20:"push_publication_ids";s:0:"";s:21:"_push_publication_ids";s:19:"field_66ab4a146d939";s:36:"flexible_main_0_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_0_acfe_flexible_toggle";s:40:"field_layout_embedded-publication_toggle";s:22:"flexible_main_0_pub_id";i:224;s:23:"_flexible_main_0_pub_id";s:52:"field_clone_embedded-publication_field_66f2ba2cafa95";s:36:"flexible_main_1_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_1_acfe_flexible_toggle";s:40:"field_layout_embedded-publication_toggle";s:22:"flexible_main_1_pub_id";i:406;s:23:"_flexible_main_1_pub_id";s:52:"field_clone_embedded-publication_field_66f2ba2cafa95";s:36:"flexible_main_2_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_2_acfe_flexible_toggle";s:29:"field_layout_logo-list_toggle";s:21:"flexible_main_2_title";s:34:"Autres partenaires institutionnels";s:22:"_flexible_main_2_title";s:41:"field_clone_logo-list_field_66f3cd8e8afe1";s:24:"flexible_main_2_logo_ids";a:5:{i:0;s:3:"540";i:1;s:3:"541";i:2;s:3:"542";i:3;s:3:"543";i:4;s:3:"544";}s:25:"_flexible_main_2_logo_ids";s:41:"field_clone_logo-list_field_66f3cd51c7a03";s:36:"flexible_main_3_acfe_flexible_toggle";s:0:"";s:37:"_flexible_main_3_acfe_flexible_toggle";s:24:"field_layout_text_toggle";s:19:"flexible_main_3_txt";s:731:"<h3>Ea aute elit ea ipsum</h3>\r\nDo esse ea dolore qui ad elit proident sint. Aliquip sunt non consectetur qui et ea excepteur minim enim fugiat est esse reprehenderit eiusmod. Sunt enim adipisicing ea non laborum sunt anim deserunt esse esse nulla aliquip nostrud enim. Amet eiusmod occaecat non ut laborum Lorem commodo veniam elit deserunt non proident officia. Anim in esse pariatur in voluptate voluptate amet et ex nulla.\r\n\r\nReprehenderit amet nostrud minim sint irure voluptate consectetur cillum quis elit officia. Minim deserunt quis irure excepteur eu amet esse sint ea. Nostrud mollit consectetur mollit sit est est dolor duis labore deserunt deserunt duis. Et pariatur est dolore. Aute incididunt esse eu ut sint mollit.";s:20:"_flexible_main_3_txt";s:36:"field_clone_text_field_63791defce143";s:13:"flexible_main";a:4:{i:0;s:20:"embedded-publication";i:1;s:20:"embedded-publication";i:2;s:9:"logo-list";i:3;s:4:"text";}s:14:"_flexible_main";s:19:"field_flexible_main";}') ;

#
# End of data contents of table `nd_postmeta`
# --------------------------------------------------------



#
# Delete any existing table `nd_posts`
#

DROP TABLE IF EXISTS `nd_posts`;


#
# Table structure of table `nd_posts`
#

CREATE TABLE `nd_posts` (
  `ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `post_author` bigint(20) unsigned NOT NULL DEFAULT '0',
  `post_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `post_date_gmt` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `post_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_excerpt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'publish',
  `comment_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'open',
  `ping_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'open',
  `post_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `post_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `to_ping` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `pinged` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_modified` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `post_modified_gmt` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `post_content_filtered` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_parent` bigint(20) unsigned NOT NULL DEFAULT '0',
  `guid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `menu_order` int(11) NOT NULL DEFAULT '0',
  `post_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'post',
  `post_mime_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_count` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  KEY `post_name` (`post_name`(191)),
  KEY `type_status_date` (`post_type`,`post_status`,`post_date`,`ID`),
  KEY `post_parent` (`post_parent`),
  KEY `post_author` (`post_author`)
) ENGINE=InnoDB AUTO_INCREMENT=621 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_posts`
#
INSERT INTO `nd_posts` ( `ID`, `post_author`, `post_date`, `post_date_gmt`, `post_content`, `post_title`, `post_excerpt`, `post_status`, `comment_status`, `ping_status`, `post_password`, `post_name`, `to_ping`, `pinged`, `post_modified`, `post_modified_gmt`, `post_content_filtered`, `post_parent`, `guid`, `menu_order`, `post_type`, `post_mime_type`, `comment_count`) VALUES
(2, 1, '2024-07-15 15:08:04', '2024-07-15 15:08:04', '<!-- wp:paragraph -->\n<p>This is an example page. It\'s different from a blog post because it will stay in one place and will show up in your site navigation (in most themes). Most people start with an About page that introduces them to potential site visitors. It might say something like this:</p>\n<!-- /wp:paragraph -->\n\n<!-- wp:quote -->\n<blockquote class="wp-block-quote"><p>Hi there! I\'m a bike messenger by day, aspiring actor by night, and this is my website. I live in Los Angeles, have a great dog named Jack, and I like pi&#241;a coladas. (And gettin\' caught in the rain.)</p></blockquote>\n<!-- /wp:quote -->\n\n<!-- wp:paragraph -->\n<p>...or something like this:</p>\n<!-- /wp:paragraph -->\n\n<!-- wp:quote -->\n<blockquote class="wp-block-quote"><p>The XYZ Doohickey Company was founded in 1971, and has been providing quality doohickeys to the public ever since. Located in Gotham City, XYZ employs over 2,000 people and does all kinds of awesome things for the Gotham community.</p></blockquote>\n<!-- /wp:quote -->\n\n<!-- wp:paragraph -->\n<p>As a new WordPress user, you should go to <a href="https://numeridanse.local/wp/wp-admin/">your dashboard</a> to delete this page and create new pages for your content. Have fun!</p>\n<!-- /wp:paragraph -->', 'Accueil', '', 'publish', 'closed', 'closed', '', 'accueil', '', '', '2024-09-24 10:58:58', '2024-09-24 08:58:58', '', 0, 'https://numeridanse.local/?page_id=2', 0, 'page', '', 0),
(3, 1, '2024-07-15 15:08:04', '2024-07-15 15:08:04', '<!-- wp:heading -->\n<h2 class="wp-block-heading">Who we are</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class="privacy-policy-tutorial">Suggested text: </strong>Our website address is: https://numeridanse.local.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class="wp-block-heading">Comments</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class="privacy-policy-tutorial">Suggested text: </strong>When visitors leave comments on the site we collect the data shown in the comments form, and also the visitor&#8217;s IP address and browser user agent string to help spam detection.</p>\n<!-- /wp:paragraph -->\n<!-- wp:paragraph -->\n<p>An anonymized string created from your email address (also called a hash) may be provided to the Gravatar service to see if you are using it. The Gravatar service privacy policy is available here: https://automattic.com/privacy/. After approval of your comment, your profile picture is visible to the public in the context of your comment.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class="wp-block-heading">Media</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class="privacy-policy-tutorial">Suggested text: </strong>If you upload images to the website, you should avoid uploading images with embedded location data (EXIF GPS) included. Visitors to the website can download and extract any location data from images on the website.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class="wp-block-heading">Cookies</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class="privacy-policy-tutorial">Suggested text: </strong>If you leave a comment on our site you may opt-in to saving your name, email address and website in cookies. These are for your convenience so that you do not have to fill in your details again when you leave another comment. These cookies will last for one year.</p>\n<!-- /wp:paragraph -->\n<!-- wp:paragraph -->\n<p>If you visit our login page, we will set a temporary cookie to determine if your browser accepts cookies. This cookie contains no personal data and is discarded when you close your browser.</p>\n<!-- /wp:paragraph -->\n<!-- wp:paragraph -->\n<p>When you log in, we will also set up several cookies to save your login information and your screen display choices. Login cookies last for two days, and screen options cookies last for a year. If you select &quot;Remember Me&quot;, your login will persist for two weeks. If you log out of your account, the login cookies will be removed.</p>\n<!-- /wp:paragraph -->\n<!-- wp:paragraph -->\n<p>If you edit or publish an article, an additional cookie will be saved in your browser. This cookie includes no personal data and simply indicates the post ID of the article you just edited. It expires after 1 day.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class="wp-block-heading">Embedded content from other websites</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class="privacy-policy-tutorial">Suggested text: </strong>Articles on this site may include embedded content (e.g. videos, images, articles, etc.). Embedded content from other websites behaves in the exact same way as if the visitor has visited the other website.</p>\n<!-- /wp:paragraph -->\n<!-- wp:paragraph -->\n<p>These websites may collect data about you, use cookies, embed additional third-party tracking, and monitor your interaction with that embedded content, including tracking your interaction with the embedded content if you have an account and are logged in to that website.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class="wp-block-heading">Who we share your data with</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class="privacy-policy-tutorial">Suggested text: </strong>If you request a password reset, your IP address will be included in the reset email.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class="wp-block-heading">How long we retain your data</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class="privacy-policy-tutorial">Suggested text: </strong>If you leave a comment, the comment and its metadata are retained indefinitely. This is so we can recognize and approve any follow-up comments automatically instead of holding them in a moderation queue.</p>\n<!-- /wp:paragraph -->\n<!-- wp:paragraph -->\n<p>For users that register on our website (if any), we also store the personal information they provide in their user profile. All users can see, edit, or delete their personal information at any time (except they cannot change their username). Website administrators can also see and edit that information.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class="wp-block-heading">What rights you have over your data</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class="privacy-policy-tutorial">Suggested text: </strong>If you have an account on this site, or have left comments, you can request to receive an exported file of the personal data we hold about you, including any data you have provided to us. You can also request that we erase any personal data we hold about you. This does not include any data we are obliged to keep for administrative, legal, or security purposes.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class="wp-block-heading">Where your data is sent</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class="privacy-policy-tutorial">Suggested text: </strong>Visitor comments may be checked through an automated spam detection service.</p>\n<!-- /wp:paragraph -->\n', 'Mentions légales', '', 'publish', 'closed', 'closed', '', 'mentions-legales', '', '', '2024-07-18 13:56:01', '2024-07-18 13:56:01', '', 0, 'https://numeridanse.local/?page_id=3', 7, 'page', '', 0),
(7, 1, '2024-07-16 14:34:36', '2024-07-16 14:34:36', '', 'Favicon', '', 'inherit', 'closed', 'closed', '', 'favicon', '', '', '2024-07-16 14:34:36', '2024-07-16 14:34:36', '', 0, 'https://numeridanse.local/app/uploads/2024/07/Favicon.png', 0, 'attachment', 'image/png', 0),
(8, 1, '2024-07-16 14:34:39', '2024-07-16 14:34:39', 'https://numeridanse.local/app/uploads/2024/07/cropped-Favicon.png', 'cropped-Favicon.png', '', 'inherit', 'closed', 'closed', '', 'cropped-favicon-png', '', '', '2024-07-16 14:34:39', '2024-07-16 14:34:39', '', 7, 'https://numeridanse.local/app/uploads/2024/07/cropped-Favicon.png', 0, 'attachment', 'image/png', 0),
(15, 1, '2024-07-18 10:37:16', '0000-00-00 00:00:00', '', 'Accueil', '', 'draft', 'closed', 'closed', '', '', '', '', '2024-07-18 10:37:16', '0000-00-00 00:00:00', '', 0, 'https://numeridanse.local/?p=15', 1, 'nav_menu_item', '', 0),
(16, 1, '2024-07-18 10:37:16', '0000-00-00 00:00:00', ' ', '', '', 'draft', 'closed', 'closed', '', '', '', '', '2024-07-18 10:37:16', '0000-00-00 00:00:00', '', 0, 'https://numeridanse.local/?p=16', 1, 'nav_menu_item', '', 0),
(33, 1, '2024-07-30 09:45:23', '2024-07-22 08:51:48', '', 'Mon compte', '', 'publish', 'closed', 'closed', '', 'mon-compte', '', '', '2024-07-30 09:45:24', '2024-07-30 09:45:24', '', 0, 'https://numeridanse.local/?p=33', 3, 'nav_menu_item', '', 0),
(36, 1, '2024-07-30 09:45:56', '2024-07-22 08:54:27', '', 'Data-danse', '', 'publish', 'closed', 'closed', '', 'data-danse', '', '', '2024-07-30 09:45:56', '2024-07-30 09:45:56', '', 0, 'https://numeridanse.local/?p=36', 3, 'nav_menu_item', '', 0),
(37, 1, '2024-07-30 09:36:24', '2024-07-22 09:19:03', ' ', '', '', 'publish', 'closed', 'closed', '', '37', '', '', '2024-07-30 09:36:24', '2024-07-30 09:36:24', '', 0, 'https://numeridanse.local/?p=37', 1, 'nav_menu_item', '', 0),
(141, 1, '2024-07-30 09:23:22', '2024-07-30 09:23:22', '', 'Recherche', '', 'publish', 'closed', 'closed', '', 'recherche', '', '', '2024-07-30 09:23:22', '2024-07-30 09:23:22', '', 0, 'https://numeridanse.local/?page_id=141', 2, 'page', '', 0),
(145, 1, '2024-07-30 09:24:48', '2024-07-30 09:24:48', '', 'Devenir contributeur', '', 'publish', 'closed', 'closed', '', 'devenir-contributeur', '', '', '2024-07-30 09:25:34', '2024-07-30 09:25:34', '', 0, 'https://numeridanse.local/?page_id=145', 6, 'page', '', 0),
(149, 1, '2024-07-30 09:25:09', '2024-07-30 09:25:09', '', 'Les avantages', '', 'publish', 'closed', 'closed', '', 'les-avantages', '', '', '2024-07-30 09:25:09', '2024-07-30 09:25:09', '', 145, 'https://numeridanse.local/?page_id=149', 0, 'page', '', 0),
(150, 1, '2024-07-30 09:25:17', '2024-07-30 09:25:17', '', 'Les abonnements', '', 'publish', 'closed', 'closed', '', 'les-abonnements', '', '', '2024-07-30 09:25:17', '2024-07-30 09:25:17', '', 145, 'https://numeridanse.local/?page_id=150', 1, 'page', '', 0),
(157, 1, '2024-07-30 09:26:44', '2024-07-30 09:26:44', '', 'À propos', '', 'publish', 'closed', 'closed', '', 'a-propos', '', '', '2024-07-30 09:26:44', '2024-07-30 09:26:44', '', 0, 'https://numeridanse.local/?page_id=157', 3, 'page', '', 0),
(161, 1, '2024-07-30 09:27:00', '2024-07-30 09:27:00', '', 'Qui sommes-nous ?', '', 'publish', 'closed', 'closed', '', 'qui-sommes-nous', '', '', '2024-07-30 09:27:07', '2024-07-30 09:27:07', '', 157, 'https://numeridanse.local/?page_id=161', 0, 'page', '', 0),
(165, 1, '2024-07-30 09:27:24', '2024-07-30 09:27:24', '', 'Fonctionnement', '', 'publish', 'closed', 'closed', '', 'fonctionnement', '', '', '2024-07-30 09:27:24', '2024-07-30 09:27:24', '', 157, 'https://numeridanse.local/?page_id=165', 1, 'page', '', 0),
(169, 1, '2024-07-30 09:27:36', '2024-07-30 09:27:36', '', 'Partenaires', '', 'publish', 'closed', 'closed', '', 'partenaires', '', '', '2024-07-30 09:27:36', '2024-07-30 09:27:36', '', 157, 'https://numeridanse.local/?page_id=169', 2, 'page', '', 0),
(173, 1, '2024-07-30 09:28:21', '2024-07-30 09:28:21', '', 'Questions fréquentes', '', 'publish', 'closed', 'closed', '', 'questions-frequentes', '', '', '2024-07-30 09:28:21', '2024-07-30 09:28:21', '', 0, 'https://numeridanse.local/?page_id=173', 4, 'page', '', 0),
(177, 1, '2024-07-30 09:32:03', '2024-07-30 09:32:03', '', 'Index', '', 'publish', 'closed', 'closed', '', 'index', '', '', '2024-07-30 09:32:03', '2024-07-30 09:32:03', '', 0, 'https://numeridanse.local/?page_id=177', 1, 'page', '', 0),
(181, 1, '2024-07-30 09:32:17', '2024-07-30 09:32:17', '', 'Titres', '', 'publish', 'closed', 'closed', '', 'titres', '', '', '2024-07-30 09:32:17', '2024-07-30 09:32:17', '', 177, 'https://numeridanse.local/?page_id=181', 0, 'page', '', 0),
(185, 1, '2024-07-30 09:33:42', '2024-07-30 09:33:42', '', 'Auteurs', '', 'publish', 'closed', 'closed', '', 'auteurs', '', '', '2024-07-30 09:33:42', '2024-07-30 09:33:42', '', 177, 'https://numeridanse.local/?page_id=185', 1, 'page', '', 0),
(189, 1, '2024-07-30 09:34:14', '2024-07-30 09:34:14', '', 'Compagnies', '', 'publish', 'closed', 'closed', '', 'compagnies', '', '', '2024-07-30 09:34:14', '2024-07-30 09:34:14', '', 177, 'https://numeridanse.local/?page_id=189', 2, 'page', '', 0),
(193, 1, '2024-07-30 09:34:18', '2024-07-30 09:34:18', '', 'Collections', '', 'publish', 'closed', 'closed', '', 'collections', '', '', '2024-07-30 09:34:18', '2024-07-30 09:34:18', '', 177, 'https://numeridanse.local/?page_id=193', 3, 'page', '', 0),
(197, 1, '2024-07-30 09:35:31', '2024-07-30 09:35:31', '', 'Politique de confidentialité', '', 'publish', 'closed', 'closed', '', 'politique-de-confidentialite', '', '', '2024-07-30 09:35:31', '2024-07-30 09:35:31', '', 0, 'https://numeridanse.local/?page_id=197', 8, 'page', '', 0),
(198, 1, '2024-07-30 09:35:38', '2024-07-30 09:35:38', '', 'Conditions générales', '', 'publish', 'closed', 'closed', '', 'conditions-generales', '', '', '2024-07-30 09:35:38', '2024-07-30 09:35:38', '', 0, 'https://numeridanse.local/?page_id=198', 9, 'page', '', 0),
(205, 1, '2024-07-30 09:36:24', '2024-07-30 09:36:24', ' ', '', '', 'publish', 'closed', 'closed', '', '205', '', '', '2024-07-30 09:36:24', '2024-07-30 09:36:24', '', 0, 'https://numeridanse.local/?p=205', 3, 'nav_menu_item', '', 0),
(206, 1, '2024-07-30 09:36:24', '2024-07-30 09:36:24', ' ', '', '', 'publish', 'closed', 'closed', '', '206', '', '', '2024-07-30 09:36:24', '2024-07-30 09:36:24', '', 0, 'https://numeridanse.local/?p=206', 2, 'nav_menu_item', '', 0),
(207, 1, '2024-07-30 09:44:17', '2024-07-30 09:44:17', '', 'Contactez-nous', '', 'publish', 'closed', 'closed', '', 'contactez-nous', '', '', '2024-07-30 09:44:17', '2024-07-30 09:44:17', '', 0, 'https://numeridanse.local/?page_id=207', 5, 'page', '', 0),
(211, 1, '2024-07-30 09:45:23', '2024-07-30 09:45:23', ' ', '', '', 'publish', 'closed', 'closed', '', '211', '', '', '2024-07-30 09:45:23', '2024-07-30 09:45:23', '', 0, 'https://numeridanse.local/?p=211', 1, 'nav_menu_item', '', 0),
(212, 1, '2024-07-30 09:45:23', '2024-07-30 09:45:23', ' ', '', '', 'publish', 'closed', 'closed', '', '212', '', '', '2024-07-30 09:45:23', '2024-07-30 09:45:23', '', 0, 'https://numeridanse.local/?p=212', 2, 'nav_menu_item', '', 0),
(213, 1, '2024-07-30 09:45:56', '2024-07-30 09:45:56', ' ', '', '', 'publish', 'closed', 'closed', '', '213', '', '', '2024-07-30 09:45:56', '2024-07-30 09:45:56', '', 0, 'https://numeridanse.local/?p=213', 1, 'nav_menu_item', '', 0),
(214, 1, '2024-07-30 09:45:56', '2024-07-30 09:45:56', ' ', '', '', 'publish', 'closed', 'closed', '', '214', '', '', '2024-07-30 09:45:56', '2024-07-30 09:45:56', '', 0, 'https://numeridanse.local/?p=214', 2, 'nav_menu_item', '', 0),
(224, 1, '2024-07-30 14:28:23', '2024-07-30 14:28:23', '', 'Tumulus', '', 'publish', 'closed', 'closed', '', 'tumulus', '', '', '2024-10-02 17:34:35', '2024-10-02 15:34:35', '', 0, 'https://numeridanse.local/?post_type=publication&#038;p=224', 0, 'publication', '', 0),
(284, 1, '2024-08-01 11:46:53', '2024-08-01 11:46:53', '', 'Maison de la Danse', '', 'publish', 'closed', 'closed', '', 'maison-de-la-danse', '', '', '2024-08-01 13:35:15', '2024-08-01 13:35:15', '', 0, 'https://numeridanse.local/?post_type=collection&#038;p=284', 0, 'collection', '', 0),
(299, 1, '2024-08-01 12:05:25', '2024-08-01 12:05:25', '', 'Saisons 1980 > 1989', '', 'publish', 'closed', 'closed', '', 'saisons-1980-1989', '', '', '2024-08-01 12:22:27', '2024-08-01 12:22:27', '', 0, 'https://numeridanse.local/?post_type=playlist&#038;p=299', 0, 'playlist', '', 0),
(300, 1, '2024-08-01 12:06:01', '2024-08-01 12:06:01', '', 'Saisons 1990 > 1999', '', 'publish', 'closed', 'closed', '', 'saisons-1990-1999', '', '', '2024-08-01 12:22:33', '2024-08-01 12:22:33', '', 0, 'https://numeridanse.local/?post_type=playlist&#038;p=300', 0, 'playlist', '', 0),
(301, 1, '2024-08-01 12:07:01', '2024-08-01 12:07:01', '', 'Saisons 2000 > 2009', '', 'publish', 'closed', 'closed', '', 'saisons-2000-2009', '', '', '2024-08-01 12:22:39', '2024-08-01 12:22:39', '', 0, 'https://numeridanse.local/?post_type=playlist&#038;p=301', 0, 'playlist', '', 0),
(382, 1, '2024-08-01 14:56:22', '2024-08-01 14:56:22', '', 'Maguy Marin', '', 'publish', 'closed', 'closed', '', 'maguy-marin', '', '', '2024-08-01 14:56:22', '2024-08-01 14:56:22', '', 0, 'https://numeridanse.local/?post_type=profile&#038;p=382', 0, 'profile', '', 0),
(392, 1, '2024-08-06 10:38:50', '2024-08-06 08:38:50', '', 'La Ciudad de los Otros', '', 'publish', 'closed', 'closed', '', 'la-ciudad-de-los-otros', '', '', '2024-10-02 12:54:45', '2024-10-02 10:54:45', '', 0, 'https://numeridanse.local/?post_type=publication&#038;p=392', 0, 'publication', '', 0),
(395, 1, '2024-08-06 12:10:41', '2024-08-06 10:10:41', '', 'The empty boat', '', 'publish', 'closed', 'closed', '', 'the-empty-boat', '', '', '2024-10-01 16:04:16', '2024-10-01 14:04:16', '', 0, 'https://numeridanse.local/?post_type=publication&#038;p=395', 0, 'publication', '', 0),
(396, 1, '2024-08-06 12:11:18', '2024-08-06 10:11:18', '', 'Danseuse, pas pute !', '', 'publish', 'closed', 'closed', '', 'danseuse-pas-pute', '', '', '2024-09-04 16:57:18', '2024-09-04 14:57:18', '', 0, 'https://numeridanse.local/?post_type=publication&#038;p=396', 0, 'publication', '', 0),
(397, 1, '2024-08-06 12:11:59', '2024-08-06 10:11:59', '', 'The future of statues', '', 'publish', 'closed', 'closed', '', 'the-future-of-statues', '', '', '2024-08-06 16:19:28', '2024-08-06 14:19:28', '', 0, 'https://numeridanse.local/?post_type=publication&#038;p=397', 0, 'publication', '', 0),
(404, 1, '2024-08-06 16:21:59', '2024-08-06 14:21:59', '', 'Ghosts', '', 'publish', 'closed', 'closed', '', 'ghosts', '', '', '2024-08-06 16:23:41', '2024-08-06 14:23:41', '', 0, 'https://numeridanse.local/?post_type=publication&#038;p=404', 0, 'publication', '', 0),
(405, 1, '2024-08-06 16:22:55', '2024-08-06 14:22:55', '', 'Lazarus', '', 'publish', 'closed', 'closed', '', 'lazarus', '', '', '2024-09-24 12:11:58', '2024-09-24 10:11:58', '', 0, 'https://numeridanse.local/?post_type=publication&#038;p=405', 0, 'publication', '', 0),
(406, 1, '2024-08-06 16:23:24', '2024-08-06 14:23:24', '', 'Un voyage d\'hiver', '', 'publish', 'closed', 'closed', '', 'un-voyage-dhiver', '', '', '2024-09-25 10:25:34', '2024-09-25 08:25:34', '', 0, 'https://numeridanse.local/?post_type=publication&#038;p=406', 0, 'publication', '', 0),
(407, 1, '2024-08-06 16:24:17', '2024-08-06 14:24:17', '', 'Tusitala', '', 'publish', 'closed', 'closed', '', 'tusitala', '', '', '2024-08-08 16:34:25', '2024-08-08 14:34:25', '', 0, 'https://numeridanse.local/?post_type=publication&#038;p=407', 0, 'publication', '', 0),
(408, 1, '2024-08-06 16:24:51', '2024-08-06 14:24:51', '', 'Balles perdues', '', 'publish', 'closed', 'closed', '', 'balles-perdues', '', '', '2024-08-06 16:24:51', '2024-08-06 14:24:51', '', 0, 'https://numeridanse.local/?post_type=publication&#038;p=408', 0, 'publication', '', 0),
(409, 1, '2024-08-06 16:25:49', '2024-08-06 14:25:49', '', 'NYST', '', 'publish', 'closed', 'closed', '', 'nyst', '', '', '2024-08-07 10:30:56', '2024-08-07 08:30:56', '', 0, 'https://numeridanse.local/?post_type=publication&#038;p=409', 0, 'publication', '', 0),
(410, 1, '2024-08-06 16:26:18', '2024-08-06 14:26:18', '', 'Vanguardia jonda', '', 'publish', 'closed', 'closed', '', 'vanguardia-jonda', '', '', '2024-08-06 16:26:18', '2024-08-06 14:26:18', '', 0, 'https://numeridanse.local/?post_type=publication&#038;p=410', 0, 'publication', '', 0),
(411, 1, '2024-08-06 16:27:37', '2024-08-06 14:27:37', '', 'Rapides', '', 'publish', 'closed', 'closed', '', 'rapides', '', '', '2024-08-08 12:21:42', '2024-08-08 10:21:42', '', 0, 'https://numeridanse.local/?post_type=publication&#038;p=411', 0, 'publication', '', 0),
(446, 1, '2024-09-02 16:35:09', '2024-09-02 14:35:09', '', 'img_1-temp', '', 'inherit', 'closed', 'closed', '', 'img_1-temp', '', '', '2024-09-02 16:35:09', '2024-09-02 14:35:09', '', 224, 'https://numeridanse.local/app/uploads/2024/07/img_1-temp.jpg', 0, 'attachment', 'image/jpeg', 0),
(456, 1, '2024-09-03 09:59:32', '2024-09-03 07:59:32', '', 'Empty file-temp', '', 'inherit', 'closed', 'closed', '', 'empty-file-temp', '', '', '2024-09-03 09:59:32', '2024-09-03 07:59:32', '', 224, 'https://numeridanse.local/app/uploads/2024/07/Empty-file-temp.pdf', 0, 'attachment', 'application/pdf', 0),
(457, 1, '2024-09-03 10:00:06', '2024-09-03 08:00:06', '', 'Empty file 2-temp', '', 'inherit', 'closed', 'closed', '', 'empty-file-2-temp', '', '', '2024-09-03 10:00:06', '2024-09-03 08:00:06', '', 224, 'https://numeridanse.local/app/uploads/2024/07/Empty-file-2-temp.pdf', 0, 'attachment', 'application/pdf', 0),
(470, 1, '2024-09-03 13:01:11', '2024-09-03 11:01:11', '', 'img_2-temp', '', 'inherit', 'closed', 'closed', '', 'img_2-temp', '', '', '2024-09-30 12:01:14', '2024-09-30 10:01:14', '', 581, 'https://numeridanse.local/app/uploads/2024/09/img_2-temp.jpg', 0, 'attachment', 'image/jpeg', 0),
(473, 1, '2024-09-03 13:01:58', '2024-09-03 11:01:58', 'a:13:{s:10:"aria-label";s:0:"";s:4:"type";s:4:"text";s:12:"instructions";s:0:"";s:8:"required";i:1;s:17:"conditional_logic";i:0;s:7:"wrapper";a:3:{s:5:"width";s:2:"40";s:5:"class";s:0:"";s:2:"id";s:0:"";}s:13:"default_value";s:0:"";s:16:"required_message";s:0:"";s:9:"maxlength";s:0:"";s:17:"allow_in_bindings";i:0;s:11:"placeholder";s:0:"";s:7:"prepend";s:0:"";s:6:"append";s:0:"";}', 'Titre', 'title', 'publish', 'closed', 'closed', '', 'field_66d6eca69cc51', '', '', '2024-09-03 13:01:58', '2024-09-03 11:01:58', '', 471, 'https://numeridanse.local/?post_type=acf-field&p=473', 0, 'acf-field', '', 0),
(474, 1, '2024-09-03 13:01:58', '2024-09-03 11:01:58', 'a:15:{s:10:"aria-label";s:0:"";s:4:"type";s:5:"radio";s:12:"instructions";s:0:"";s:8:"required";i:1;s:17:"conditional_logic";i:0;s:7:"wrapper";a:3:{s:5:"width";s:2:"30";s:5:"class";s:0:"";s:2:"id";s:0:"";}s:7:"choices";a:3:{s:4:"file";s:7:"Fichier";s:7:"dl_file";s:24:"Fichier à télécharger";s:13:"external_link";s:12:"Lien externe";}s:13:"default_value";s:4:"file";s:13:"return_format";s:5:"array";s:16:"required_message";s:0:"";s:10:"allow_null";i:0;s:12:"other_choice";i:0;s:17:"allow_in_bindings";i:0;s:6:"layout";s:8:"vertical";s:17:"save_other_choice";i:0;}', 'Type', 'type', 'publish', 'closed', 'closed', '', 'field_66d6eca69cc5a', '', '', '2024-09-03 13:01:58', '2024-09-03 11:01:58', '', 471, 'https://numeridanse.local/?post_type=acf-field&p=474', 1, 'acf-field', '', 0),
(475, 1, '2024-09-03 13:01:58', '2024-09-03 11:01:58', 'a:23:{s:10:"aria-label";s:0:"";s:4:"type";s:4:"file";s:12:"instructions";s:0:"";s:8:"required";i:1;s:17:"conditional_logic";a:1:{i:0;a:1:{i:0;a:3:{s:5:"field";s:19:"field_66d6eca69cc5a";s:8:"operator";s:2:"==";s:5:"value";s:4:"file";}}}s:7:"wrapper";a:3:{s:5:"width";s:2:"30";s:5:"class";s:0:"";s:2:"id";s:0:"";}s:8:"uploader";s:0:"";s:13:"return_format";s:2:"id";s:13:"preview_style";s:7:"default";s:7:"library";s:3:"all";s:13:"upload_folder";s:0:"";s:12:"button_label";s:18:"Ajouter un fichier";s:10:"file_count";i:0;s:8:"multiple";i:0;s:3:"max";s:0:"";s:16:"required_message";s:0:"";s:8:"min_size";s:0:"";s:8:"max_size";s:0:"";s:10:"mime_types";s:0:"";s:17:"allow_in_bindings";i:0;s:11:"placeholder";s:6:"Select";s:15:"stylised_button";i:0;s:3:"min";s:0:"";}', 'Fichier', 'file_id', 'publish', 'closed', 'closed', '', 'field_66d6eca69cc62', '', '', '2024-09-03 13:01:58', '2024-09-03 11:01:58', '', 471, 'https://numeridanse.local/?post_type=acf-field&p=475', 2, 'acf-field', '', 0),
(476, 1, '2024-09-03 13:01:58', '2024-09-03 11:01:58', 'a:23:{s:10:"aria-label";s:0:"";s:4:"type";s:4:"file";s:12:"instructions";s:0:"";s:8:"required";i:1;s:17:"conditional_logic";a:1:{i:0;a:1:{i:0;a:3:{s:5:"field";s:19:"field_66d6eca69cc5a";s:8:"operator";s:2:"==";s:5:"value";s:7:"dl_file";}}}s:7:"wrapper";a:3:{s:5:"width";s:2:"30";s:5:"class";s:0:"";s:2:"id";s:0:"";}s:8:"uploader";s:0:"";s:13:"return_format";s:2:"id";s:13:"preview_style";s:7:"default";s:7:"library";s:3:"all";s:13:"upload_folder";s:0:"";s:12:"button_label";s:18:"Ajouter un fichier";s:10:"file_count";i:0;s:8:"multiple";i:0;s:3:"max";s:0:"";s:16:"required_message";s:0:"";s:8:"min_size";s:0:"";s:8:"max_size";s:0:"";s:10:"mime_types";s:0:"";s:17:"allow_in_bindings";i:0;s:11:"placeholder";s:6:"Select";s:15:"stylised_button";i:0;s:3:"min";s:0:"";}', 'Fichier à télécharger', 'dl_file_id', 'publish', 'closed', 'closed', '', 'field_66d6eca69cc69', '', '', '2024-09-03 13:01:58', '2024-09-03 11:01:58', '', 471, 'https://numeridanse.local/?post_type=acf-field&p=476', 3, 'acf-field', '', 0),
(477, 1, '2024-09-03 13:01:58', '2024-09-03 11:01:58', 'a:10:{s:10:"aria-label";s:0:"";s:4:"type";s:3:"url";s:12:"instructions";s:0:"";s:8:"required";i:1;s:17:"conditional_logic";a:1:{i:0;a:1:{i:0;a:3:{s:5:"field";s:19:"field_66d6eca69cc5a";s:8:"operator";s:2:"==";s:5:"value";s:13:"external_link";}}}s:7:"wrapper";a:3:{s:5:"width";s:2:"30";s:5:"class";s:0:"";s:2:"id";s:0:"";}s:13:"default_value";s:0:"";s:16:"required_message";s:0:"";s:17:"allow_in_bindings";i:1;s:11:"placeholder";s:0:"";}', 'Lien externe', 'external_link', 'publish', 'closed', 'closed', '', 'field_66d6eca69cc71', '', '', '2024-09-03 13:01:58', '2024-09-03 11:01:58', '', 471, 'https://numeridanse.local/?post_type=acf-field&p=477', 4, 'acf-field', '', 0),
(486, 1, '2024-09-04 16:13:58', '2024-09-04 14:13:58', '', 'img_3-temp', '', 'inherit', 'closed', 'closed', '', 'img_3-temp', '', '', '2024-09-04 16:13:58', '2024-09-04 14:13:58', '', 224, 'https://numeridanse.local/app/uploads/2024/07/img_3-temp.jpg', 0, 'attachment', 'image/jpeg', 0),
(492, 1, '2024-09-04 16:48:52', '2024-09-04 14:48:52', '', 'img_4-temp', '', 'inherit', 'closed', 'closed', '', 'img_4-temp', '', '', '2024-09-04 16:48:52', '2024-09-04 14:48:52', '', 2, 'https://numeridanse.local/app/uploads/2024/09/img_4-temp.jpg', 0, 'attachment', 'image/jpeg', 0),
(534, 1, '2024-09-25 10:44:01', '2024-09-25 08:44:01', 'a:13:{s:10:"aria-label";s:0:"";s:4:"type";s:4:"text";s:12:"instructions";s:0:"";s:8:"required";i:1;s:17:"conditional_logic";i:0;s:7:"wrapper";a:3:{s:5:"width";s:2:"40";s:5:"class";s:0:"";s:2:"id";s:0:"";}s:13:"default_value";s:0:"";s:16:"required_message";s:0:"";s:9:"maxlength";s:0:"";s:17:"allow_in_bindings";i:0;s:11:"placeholder";s:0:"";s:7:"prepend";s:0:"";s:6:"append";s:0:"";}', 'Titre', 'title', 'publish', 'closed', 'closed', '', 'field_66f3cd51ca0ce', '', '', '2024-09-25 10:44:01', '2024-09-25 08:44:01', '', 532, 'https://numeridanse.local/?post_type=acf-field&p=534', 0, 'acf-field', '', 0),
(535, 1, '2024-09-25 10:44:01', '2024-09-25 08:44:01', 'a:15:{s:10:"aria-label";s:0:"";s:4:"type";s:5:"radio";s:12:"instructions";s:0:"";s:8:"required";i:1;s:17:"conditional_logic";i:0;s:7:"wrapper";a:3:{s:5:"width";s:2:"30";s:5:"class";s:0:"";s:2:"id";s:0:"";}s:7:"choices";a:3:{s:4:"file";s:7:"Fichier";s:7:"dl_file";s:24:"Fichier à télécharger";s:13:"external_link";s:12:"Lien externe";}s:13:"default_value";s:4:"file";s:13:"return_format";s:5:"array";s:16:"required_message";s:0:"";s:10:"allow_null";i:0;s:12:"other_choice";i:0;s:17:"allow_in_bindings";i:0;s:6:"layout";s:8:"vertical";s:17:"save_other_choice";i:0;}', 'Type', 'type', 'publish', 'closed', 'closed', '', 'field_66f3cd51ca0f1', '', '', '2024-09-25 10:44:01', '2024-09-25 08:44:01', '', 532, 'https://numeridanse.local/?post_type=acf-field&p=535', 1, 'acf-field', '', 0),
(536, 1, '2024-09-25 10:44:01', '2024-09-25 08:44:01', 'a:23:{s:10:"aria-label";s:0:"";s:4:"type";s:4:"file";s:12:"instructions";s:0:"";s:8:"required";i:1;s:17:"conditional_logic";a:1:{i:0;a:1:{i:0;a:3:{s:5:"field";s:19:"field_66f3cd51ca0f1";s:8:"operator";s:2:"==";s:5:"value";s:4:"file";}}}s:7:"wrapper";a:3:{s:5:"width";s:2:"30";s:5:"class";s:0:"";s:2:"id";s:0:"";}s:8:"uploader";s:0:"";s:13:"return_format";s:2:"id";s:13:"preview_style";s:7:"default";s:7:"library";s:3:"all";s:13:"upload_folder";s:0:"";s:12:"button_label";s:18:"Ajouter un fichier";s:10:"file_count";i:0;s:8:"multiple";i:0;s:3:"max";s:0:"";s:16:"required_message";s:0:"";s:8:"min_size";s:0:"";s:8:"max_size";s:0:"";s:10:"mime_types";s:0:"";s:17:"allow_in_bindings";i:0;s:11:"placeholder";s:6:"Select";s:15:"stylised_button";i:0;s:3:"min";s:0:"";}', 'Fichier', 'file_id', 'publish', 'closed', 'closed', '', 'field_66f3cd51ca0f8', '', '', '2024-09-25 10:44:01', '2024-09-25 08:44:01', '', 532, 'https://numeridanse.local/?post_type=acf-field&p=536', 2, 'acf-field', '', 0),
(537, 1, '2024-09-25 10:44:01', '2024-09-25 08:44:01', 'a:23:{s:10:"aria-label";s:0:"";s:4:"type";s:4:"file";s:12:"instructions";s:0:"";s:8:"required";i:1;s:17:"conditional_logic";a:1:{i:0;a:1:{i:0;a:3:{s:5:"field";s:19:"field_66f3cd51ca0f1";s:8:"operator";s:2:"==";s:5:"value";s:7:"dl_file";}}}s:7:"wrapper";a:3:{s:5:"width";s:2:"30";s:5:"class";s:0:"";s:2:"id";s:0:"";}s:8:"uploader";s:0:"";s:13:"return_format";s:2:"id";s:13:"preview_style";s:7:"default";s:7:"library";s:3:"all";s:13:"upload_folder";s:0:"";s:12:"button_label";s:18:"Ajouter un fichier";s:10:"file_count";i:0;s:8:"multiple";i:0;s:3:"max";s:0:"";s:16:"required_message";s:0:"";s:8:"min_size";s:0:"";s:8:"max_size";s:0:"";s:10:"mime_types";s:0:"";s:17:"allow_in_bindings";i:0;s:11:"placeholder";s:6:"Select";s:15:"stylised_button";i:0;s:3:"min";s:0:"";}', 'Fichier à télécharger', 'dl_file_id', 'publish', 'closed', 'closed', '', 'field_66f3cd51ca0ff', '', '', '2024-09-25 10:44:01', '2024-09-25 08:44:01', '', 532, 'https://numeridanse.local/?post_type=acf-field&p=537', 3, 'acf-field', '', 0),
(538, 1, '2024-09-25 10:44:01', '2024-09-25 08:44:01', 'a:10:{s:10:"aria-label";s:0:"";s:4:"type";s:3:"url";s:12:"instructions";s:0:"";s:8:"required";i:1;s:17:"conditional_logic";a:1:{i:0;a:1:{i:0;a:3:{s:5:"field";s:19:"field_66f3cd51ca0f1";s:8:"operator";s:2:"==";s:5:"value";s:13:"external_link";}}}s:7:"wrapper";a:3:{s:5:"width";s:2:"30";s:5:"class";s:0:"";s:2:"id";s:0:"";}s:13:"default_value";s:0:"";s:16:"required_message";s:0:"";s:17:"allow_in_bindings";i:1;s:11:"placeholder";s:0:"";}', 'Lien externe', 'external_link', 'publish', 'closed', 'closed', '', 'field_66f3cd51ca106', '', '', '2024-09-25 10:44:01', '2024-09-25 08:44:01', '', 532, 'https://numeridanse.local/?post_type=acf-field&p=538', 4, 'acf-field', '', 0),
(540, 1, '2024-09-25 10:56:17', '2024-09-25 08:56:17', '', 'logo_1-temp', '', 'inherit', 'closed', 'closed', '', 'logo_1-temp', '', '', '2024-09-25 10:56:20', '2024-09-25 08:56:20', '', 392, 'https://numeridanse.local/app/uploads/2024/08/logo_1-temp.jpg', 0, 'attachment', 'image/jpeg', 0),
(541, 1, '2024-09-25 10:56:18', '2024-09-25 08:56:18', '', 'logo_2-temp', '', 'inherit', 'closed', 'closed', '', 'logo_2-temp', '', '', '2024-09-25 10:56:18', '2024-09-25 08:56:18', '', 392, 'https://numeridanse.local/app/uploads/2024/08/logo_2-temp.jpg', 0, 'attachment', 'image/jpeg', 0),
(542, 1, '2024-09-25 10:56:18', '2024-09-25 08:56:18', '', 'logo_3-temp', '', 'inherit', 'closed', 'closed', '', 'logo_3-temp', '', '', '2024-09-25 10:56:18', '2024-09-25 08:56:18', '', 392, 'https://numeridanse.local/app/uploads/2024/08/logo_3-temp.jpg', 0, 'attachment', 'image/jpeg', 0),
(543, 1, '2024-09-25 10:56:19', '2024-09-25 08:56:19', '', 'logo_4-temp', '', 'inherit', 'closed', 'closed', '', 'logo_4-temp', '', '', '2024-09-25 10:56:19', '2024-09-25 08:56:19', '', 392, 'https://numeridanse.local/app/uploads/2024/08/logo_4-temp.jpg', 0, 'attachment', 'image/jpeg', 0),
(544, 1, '2024-09-25 10:56:19', '2024-09-25 08:56:19', '', 'logo_5-temp', '', 'inherit', 'closed', 'closed', '', 'logo_5-temp', '', '', '2024-09-25 11:03:56', '2024-09-25 09:03:56', '', 392, 'https://numeridanse.local/app/uploads/2024/08/logo_5-temp.jpg', 0, 'attachment', 'image/jpeg', 0),
(549, 1, '2024-09-25 11:49:54', '2024-09-25 09:49:54', '', 'Danses disruptives', '', 'publish', 'closed', 'closed', '', 'danses-disruptives', '', '', '2024-09-25 11:49:54', '2024-09-25 09:49:54', '', 0, 'https://numeridanse.local/?post_type=playlist&#038;p=549', 0, 'playlist', '', 0),
(573, 1, '2024-09-30 10:42:43', '2024-09-30 08:42:43', '', 'Numeridanse', '', 'publish', 'closed', 'closed', '', 'numeridanse', '', '', '2024-09-30 10:42:43', '2024-09-30 08:42:43', '', 0, 'https://numeridanse.local/?post_type=collection&#038;p=573', 0, 'collection', '', 0),
(579, 1, '2024-09-30 12:00:44', '2024-09-30 10:00:44', '', 'Geoffroy Jourdain', '', 'publish', 'closed', 'closed', '', 'geoffroy-jourdain', '', '', '2024-09-30 12:00:44', '2024-09-30 10:00:44', '', 0, 'https://numeridanse.local/?post_type=profile&#038;p=579', 0, 'profile', '', 0),
(580, 1, '2024-09-30 12:00:59', '2024-09-30 10:00:59', '', 'François Chaignaud', '', 'publish', 'closed', 'closed', '', 'francois-chaignaud', '', '', '2024-09-30 12:00:59', '2024-09-30 10:00:59', '', 0, 'https://numeridanse.local/?post_type=profile&#038;p=580', 0, 'profile', '', 0),
(581, 1, '2024-09-30 12:01:14', '2024-09-30 10:01:14', '', 'Fabien Plasson', '', 'publish', 'closed', 'closed', '', 'fabien-plasson', '', '', '2024-09-30 12:01:14', '2024-09-30 10:01:14', '', 0, 'https://numeridanse.local/?post_type=profile&#038;p=581', 0, 'profile', '', 0),
(585, 1, '2024-09-30 12:02:11', '2024-09-30 10:02:11', '', 'Anna Chirescu', '', 'publish', 'closed', 'closed', '', 'anna-chirescu', '', '', '2024-09-30 12:02:11', '2024-09-30 10:02:11', '', 0, 'https://numeridanse.local/?post_type=profile&#038;p=585', 0, 'profile', '', 0),
(586, 1, '2024-09-30 12:02:31', '2024-09-30 10:02:31', '', 'Marinette Buchy', '', 'publish', 'closed', 'closed', '', 'marinette-buchy', '', '', '2024-09-30 12:02:31', '2024-09-30 10:02:31', '', 0, 'https://numeridanse.local/?post_type=profile&#038;p=586', 0, 'profile', '', 0),
(587, 1, '2024-09-30 12:03:13', '2024-09-30 10:03:13', '', 'Aude Besnard', '', 'publish', 'closed', 'closed', '', 'aude-besnard', '', '', '2024-09-30 12:03:13', '2024-09-30 10:03:13', '', 0, 'https://numeridanse.local/?post_type=profile&#038;p=587', 0, 'profile', '', 0) ;

#
# End of data contents of table `nd_posts`
# --------------------------------------------------------



#
# Delete any existing table `nd_pp_activity_logs`
#

DROP TABLE IF EXISTS `nd_pp_activity_logs`;


#
# Table structure of table `nd_pp_activity_logs`
#

CREATE TABLE `nd_pp_activity_logs` (
  `id` mediumint(9) NOT NULL AUTO_INCREMENT,
  `ip` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `browser` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `status` tinytext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `created_at` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_pp_activity_logs`
#

#
# End of data contents of table `nd_pp_activity_logs`
# --------------------------------------------------------



#
# Delete any existing table `nd_seopress_content_analysis`
#

DROP TABLE IF EXISTS `nd_seopress_content_analysis`;


#
# Table structure of table `nd_seopress_content_analysis`
#

CREATE TABLE `nd_seopress_content_analysis` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `post_id` bigint(20) DEFAULT NULL,
  `title` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `og_title` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `og_description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `og_image` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `og_url` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `og_site_name` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `twitter_title` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `twitter_description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `twitter_image` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `twitter_image_src` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `canonical` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `h1` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `h2` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `h3` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `images` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `meta_robots` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `meta_google` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `links_no_follow` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `outbound_links` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `internal_links` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `json_schemas` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `permalink` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `score` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `analysis_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_post_id` (`post_id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_seopress_content_analysis`
#
INSERT INTO `nd_seopress_content_analysis` ( `id`, `post_id`, `title`, `description`, `og_title`, `og_description`, `og_image`, `og_url`, `og_site_name`, `twitter_title`, `twitter_description`, `twitter_image`, `twitter_image_src`, `canonical`, `h1`, `h2`, `h3`, `images`, `meta_robots`, `meta_google`, `links_no_follow`, `outbound_links`, `internal_links`, `json_schemas`, `keywords`, `permalink`, `score`, `analysis_date`) VALUES
(1, 2, 'numeridanse.tv', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'a:1:{i:0;s:14:"numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:1:{i:0;a:2:{s:5:"value";s:11:"Les Animals";s:3:"url";s:26:"https://lesanimals.digital";}}', 'a:1:{i:0;a:4:{s:2:"id";i:2;s:14:"edit_post_link";s:65:"https://numeridanse.local/wp/wp-admin/post.php?post=2&action=edit";s:3:"url";s:26:"https://numeridanse.local/";s:5:"value";s:7:"Accueil";}}', NULL, NULL, 'a:1:{s:5:"value";s:26:"https://numeridanse.local/";}', 'a:3:{i:0;s:4:"high";i:1;s:6:"medium";i:2;s:4:"good";}', '2024-10-03 14:24:59'),
(2, 3, 'Mentions légales - numeridanse.tv', 'Who we are Suggested text: Our website address is: https://numeridanse.local. Comments Suggested text: When visitors leave comments on the site we collect the data shown in the comments form, and also the visitor’s IP address and browser user agent string to help spam detection. An anonymized string created from your…', 'a:1:{i:0;s:34:"Mentions légales - numeridanse.tv";}', 'a:1:{i:0;s:323:"Who we are Suggested text: Our website address is: https://numeridanse.local. Comments Suggested text: When visitors leave comments on the site we collect the data shown in the comments form, and also the visitor’s IP address and browser user agent string to help spam detection. An anonymized string created from your…";}', NULL, 'a:1:{i:0;s:43:"https://numeridanse.local/mentions-legales/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:34:"Mentions légales - numeridanse.tv";}', 'a:1:{i:0;s:323:"Who we are Suggested text: Our website address is: https://numeridanse.local. Comments Suggested text: When visitors leave comments on the site we collect the data shown in the comments form, and also the visitor’s IP address and browser user agent string to help spam detection. An anonymized string created from your…";}', NULL, NULL, 'a:1:{i:0;s:43:"https://numeridanse.local/mentions-legales/";}', NULL, NULL, NULL, NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, NULL, NULL, NULL, NULL, 'a:1:{s:5:"value";s:43:"https://numeridanse.local/mentions-legales/";}', 'a:3:{i:0;s:4:"good";i:1;s:6:"medium";i:4;s:4:"high";}', '2024-07-18 13:56:04'),
(3, 125, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'a:1:{s:5:"value";s:43:"https://numeridanse.local/publication/test/";}', 'a:3:{i:0;s:4:"high";i:1;s:6:"medium";i:2;s:4:"good";}', '2024-07-29 13:13:14'),
(4, 141, 'Recherche - numeridanse.tv', '', 'a:1:{i:0;s:26:"Recherche - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:36:"https://numeridanse.local/recherche/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:26:"Recherche - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:36:"https://numeridanse.local/recherche/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:36:"https://numeridanse.local/recherche/";}', 'a:3:{i:0;s:4:"high";i:1;s:6:"medium";i:2;s:4:"good";}', '2024-07-30 09:23:24'),
(5, 145, 'Les avantages - numeridanse.tv', '', 'a:1:{i:0;s:30:"Les avantages - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:61:"https://numeridanse.local/devenir-contributeur/les-avantages/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:30:"Les avantages - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:61:"https://numeridanse.local/devenir-contributeur/les-avantages/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:61:"https://numeridanse.local/devenir-contributeur/les-avantages/";}', 'a:3:{i:0;s:4:"good";i:1;s:6:"medium";i:4;s:4:"high";}', '2024-07-30 09:25:36'),
(6, 149, 'Les avantages - numeridanse.tv', '', 'a:1:{i:0;s:30:"Les avantages - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:61:"https://numeridanse.local/devenir-contributeur/les-avantages/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:30:"Les avantages - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:61:"https://numeridanse.local/devenir-contributeur/les-avantages/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:61:"https://numeridanse.local/devenir-contributeur/les-avantages/";}', 'a:3:{i:0;s:4:"good";i:1;s:6:"medium";i:4;s:4:"high";}', '2024-07-30 09:25:44'),
(7, 150, 'Les abonnements - numeridanse.tv', '', 'a:1:{i:0;s:32:"Les abonnements - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:63:"https://numeridanse.local/devenir-contributeur/les-abonnements/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:32:"Les abonnements - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:63:"https://numeridanse.local/devenir-contributeur/les-abonnements/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:63:"https://numeridanse.local/devenir-contributeur/les-abonnements/";}', 'a:3:{i:0;s:4:"good";i:1;s:6:"medium";i:4;s:4:"high";}', '2024-07-30 09:25:46'),
(8, 157, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'a:1:{s:5:"value";s:35:"https://numeridanse.local/a-propos/";}', 'a:3:{i:0;s:4:"high";i:1;s:6:"medium";i:2;s:4:"good";}', '2024-07-30 09:26:46'),
(9, 161, 'Qui sommes-nous ? - numeridanse.tv', '', 'a:1:{i:0;s:34:"Qui sommes-nous ? - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:51:"https://numeridanse.local/a-propos/qui-sommes-nous/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:34:"Qui sommes-nous ? - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:51:"https://numeridanse.local/a-propos/qui-sommes-nous/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:51:"https://numeridanse.local/a-propos/qui-sommes-nous/";}', 'a:3:{i:0;s:4:"good";i:1;s:6:"medium";i:4;s:4:"high";}', '2024-07-30 09:27:13'),
(10, 165, 'Fonctionnement - numeridanse.tv', '', 'a:1:{i:0;s:31:"Fonctionnement - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:50:"https://numeridanse.local/a-propos/fonctionnement/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:31:"Fonctionnement - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:50:"https://numeridanse.local/a-propos/fonctionnement/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:50:"https://numeridanse.local/a-propos/fonctionnement/";}', 'a:3:{i:0;s:4:"high";i:1;s:6:"medium";i:2;s:4:"good";}', '2024-07-30 09:27:26'),
(11, 169, 'Partenaires - numeridanse.tv', '', 'a:1:{i:0;s:28:"Partenaires - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:47:"https://numeridanse.local/a-propos/partenaires/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:28:"Partenaires - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:47:"https://numeridanse.local/a-propos/partenaires/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:47:"https://numeridanse.local/a-propos/partenaires/";}', 'a:3:{i:0;s:4:"high";i:1;s:6:"medium";i:2;s:4:"good";}', '2024-07-30 09:27:38'),
(12, 173, 'Questions fréquentes - numeridanse.tv', '', 'a:1:{i:0;s:38:"Questions fréquentes - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:47:"https://numeridanse.local/questions-frequentes/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:38:"Questions fréquentes - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:47:"https://numeridanse.local/questions-frequentes/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:47:"https://numeridanse.local/questions-frequentes/";}', 'a:3:{i:0;s:4:"high";i:1;s:6:"medium";i:2;s:4:"good";}', '2024-07-30 09:28:23'),
(13, 177, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'a:1:{s:5:"value";s:32:"https://numeridanse.local/index/";}', 'a:3:{i:0;s:4:"high";i:1;s:6:"medium";i:2;s:4:"good";}', '2024-07-30 09:32:05'),
(14, 181, 'Titres - numeridanse.tv', '', 'a:1:{i:0;s:23:"Titres - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:39:"https://numeridanse.local/index/titres/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:23:"Titres - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:39:"https://numeridanse.local/index/titres/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:39:"https://numeridanse.local/index/titres/";}', 'a:3:{i:0;s:4:"high";i:1;s:6:"medium";i:2;s:4:"good";}', '2024-07-30 09:32:19'),
(15, 185, 'Auteurs - numeridanse.tv', '', 'a:1:{i:0;s:24:"Auteurs - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:40:"https://numeridanse.local/index/auteurs/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:24:"Auteurs - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:40:"https://numeridanse.local/index/auteurs/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:40:"https://numeridanse.local/index/auteurs/";}', 'a:3:{i:0;s:4:"good";i:1;s:6:"medium";i:4;s:4:"high";}', '2024-07-30 09:34:21'),
(16, 189, 'Compagnies - numeridanse.tv', '', 'a:1:{i:0;s:27:"Compagnies - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:43:"https://numeridanse.local/index/compagnies/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:27:"Compagnies - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:43:"https://numeridanse.local/index/compagnies/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:43:"https://numeridanse.local/index/compagnies/";}', 'a:3:{i:0;s:4:"good";i:1;s:6:"medium";i:4;s:4:"high";}', '2024-07-30 09:34:23'),
(17, 193, 'Collections - numeridanse.tv', '', 'a:1:{i:0;s:28:"Collections - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:44:"https://numeridanse.local/index/collections/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:28:"Collections - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:44:"https://numeridanse.local/index/collections/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:44:"https://numeridanse.local/index/collections/";}', 'a:3:{i:0;s:4:"good";i:1;s:6:"medium";i:4;s:4:"high";}', '2024-09-04 10:58:54'),
(18, 197, 'Politique de confidentialité - numeridanse.tv', '', 'a:1:{i:0;s:46:"Politique de confidentialité - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:55:"https://numeridanse.local/politique-de-confidentialite/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:46:"Politique de confidentialité - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:55:"https://numeridanse.local/politique-de-confidentialite/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:55:"https://numeridanse.local/politique-de-confidentialite/";}', 'a:3:{i:0;s:4:"high";i:1;s:6:"medium";i:2;s:4:"good";}', '2024-07-30 09:35:34'),
(19, 198, 'Conditions générales - numeridanse.tv', '', 'a:1:{i:0;s:39:"Conditions générales - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:47:"https://numeridanse.local/conditions-generales/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:39:"Conditions générales - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:47:"https://numeridanse.local/conditions-generales/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:47:"https://numeridanse.local/conditions-generales/";}', 'a:3:{i:0;s:4:"high";i:1;s:6:"medium";i:2;s:4:"good";}', '2024-07-30 09:35:40'),
(20, 207, 'Contactez-nous - numeridanse.tv', '', 'a:1:{i:0;s:31:"Contactez-nous - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:41:"https://numeridanse.local/contactez-nous/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:31:"Contactez-nous - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:41:"https://numeridanse.local/contactez-nous/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:41:"https://numeridanse.local/contactez-nous/";}', 'a:3:{i:0;s:4:"high";i:1;s:6:"medium";i:2;s:4:"good";}', '2024-07-30 09:44:19'),
(21, 32, 'testyolo – numeridanse.tv', '', 'a:1:{i:0;s:25:"testyolo - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:48:"https://numeridanse.local/pub/tax-yolo/testyolo/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:25:"testyolo - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:48:"https://numeridanse.local/pub/tax-yolo/testyolo/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', 'a:10:{i:0;a:4:{s:2:"id";i:207;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=207&action=edit";s:3:"url";s:41:"https://numeridanse.local/contactez-nous/";s:5:"value";s:14:"Contactez-nous";}i:1;a:4:{s:2:"id";i:198;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=198&action=edit";s:3:"url";s:47:"https://numeridanse.local/conditions-generales/";s:5:"value";s:22:"Conditions générales";}i:2;a:4:{s:2:"id";i:197;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=197&action=edit";s:3:"url";s:55:"https://numeridanse.local/politique-de-confidentialite/";s:5:"value";s:29:"Politique de confidentialité";}i:3;a:4:{s:2:"id";i:193;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=193&action=edit";s:3:"url";s:44:"https://numeridanse.local/index/collections/";s:5:"value";s:11:"Collections";}i:4;a:4:{s:2:"id";i:189;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=189&action=edit";s:3:"url";s:43:"https://numeridanse.local/index/compagnies/";s:5:"value";s:10:"Compagnies";}i:5;a:4:{s:2:"id";i:185;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=185&action=edit";s:3:"url";s:40:"https://numeridanse.local/index/auteurs/";s:5:"value";s:7:"Auteurs";}i:6;a:4:{s:2:"id";i:181;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=181&action=edit";s:3:"url";s:39:"https://numeridanse.local/index/titres/";s:5:"value";s:6:"Titres";}i:7;a:4:{s:2:"id";i:177;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=177&action=edit";s:3:"url";s:39:"https://numeridanse.local/index/titres/";s:5:"value";s:5:"Index";}i:8;a:4:{s:2:"id";i:173;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=173&action=edit";s:3:"url";s:47:"https://numeridanse.local/questions-frequentes/";s:5:"value";s:21:"Questions fréquentes";}i:9;a:4:{s:2:"id";i:169;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=169&action=edit";s:3:"url";s:47:"https://numeridanse.local/a-propos/partenaires/";s:5:"value";s:11:"Partenaires";}}', NULL, NULL, 'a:1:{s:5:"value";b:0;}', 'a:3:{i:0;s:4:"high";i:1;s:6:"medium";i:2;s:4:"good";}', '2024-07-30 13:33:17'),
(22, 33, 'houyou – numeridanse.tv', '', 'a:1:{i:0;s:23:"houyou - numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:41:"https://numeridanse.local/you/hou/houyou/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:23:"houyou - numeridanse.tv";}', NULL, NULL, NULL, 'a:1:{i:0;s:41:"https://numeridanse.local/you/hou/houyou/";}', NULL, NULL, 'a:1:{i:0;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', NULL, NULL, NULL, 'a:1:{s:5:"value";s:37:"https://numeridanse.local/mon-compte/";}', 'a:3:{i:0;s:4:"good";i:1;s:6:"medium";i:4;s:4:"high";}', '2024-07-30 13:23:13'),
(23, 34, 'numeridanse.tv', 'numeridanse.tv', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', NULL, 'a:1:{i:0;s:26:"https://numeridanse.local/";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', 'a:1:{i:0;s:14:"numeridanse.tv";}', NULL, NULL, 'a:1:{i:0;s:26:"https://numeridanse.local/";}', 'a:1:{i:0;s:15:"Naturally close";}', 'a:1:{i:0;s:23:"Représentation filmée";}', 'a:2:{i:0;s:200:"La recherche du sens et de l’esthétique pour un style intemporel à la fois musclé, énergique et sobre, qui puise sa richesse dans les racines de la danse et dans une vision dynamique de cet art.";i:1;s:28:"Souscrire \r\nà la newsletter";}', NULL, 'a:1:{i:0;s:17:"noindex, nofollow";}', NULL, NULL, 'a:10:{i:0;a:2:{s:5:"value";s:21:"Découvrir un exemple";s:3:"url";s:43:"https://www.brevo.com/#decouvrir-un-exemple";}i:1;a:2:{s:5:"value";s:22:"\n						S’abonner\n	\n	";s:3:"url";s:32:"https://www.brevo.com/#s-abonner";}i:2;a:2:{s:5:"value";s:10:"Data-danse";s:3:"url";s:34:"https://data-danse.numeridanse.tv/";}i:3;a:2:{s:5:"value";s:8:"Facebook";s:3:"url";s:36:"https://www.facebook.com/Numeridanse";}i:4;a:2:{s:5:"value";s:9:"Instagram";s:3:"url";s:37:"https://www.instagram.com/numeridanse";}i:5;a:2:{s:5:"value";s:8:"LinkedIn";s:3:"url";s:44:"https://www.linkedin.com/company/numeridanse";}i:6;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://maisondeladanse.com";}i:7;a:2:{s:5:"value";s:0:"";s:3:"url";s:27:"https://www.culture.gouv.fr";}i:8;a:2:{s:5:"value";s:0:"";s:3:"url";s:18:"https://www.cnd.fr";}i:9;a:2:{s:5:"value";s:0:"";s:3:"url";s:28:"https://fondation.bnpparibas";}}', 'a:10:{i:0;a:4:{s:2:"id";i:207;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=207&action=edit";s:3:"url";s:41:"https://numeridanse.local/contactez-nous/";s:5:"value";s:14:"Contactez-nous";}i:1;a:4:{s:2:"id";i:198;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=198&action=edit";s:3:"url";s:47:"https://numeridanse.local/conditions-generales/";s:5:"value";s:22:"Conditions générales";}i:2;a:4:{s:2:"id";i:197;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=197&action=edit";s:3:"url";s:55:"https://numeridanse.local/politique-de-confidentialite/";s:5:"value";s:29:"Politique de confidentialité";}i:3;a:4:{s:2:"id";i:193;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=193&action=edit";s:3:"url";s:44:"https://numeridanse.local/index/collections/";s:5:"value";s:11:"Collections";}i:4;a:4:{s:2:"id";i:189;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=189&action=edit";s:3:"url";s:43:"https://numeridanse.local/index/compagnies/";s:5:"value";s:10:"Compagnies";}i:5;a:4:{s:2:"id";i:185;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=185&action=edit";s:3:"url";s:40:"https://numeridanse.local/index/auteurs/";s:5:"value";s:7:"Auteurs";}i:6;a:4:{s:2:"id";i:181;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=181&action=edit";s:3:"url";s:39:"https://numeridanse.local/index/titres/";s:5:"value";s:6:"Titres";}i:7;a:4:{s:2:"id";i:177;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=177&action=edit";s:3:"url";s:39:"https://numeridanse.local/index/titres/";s:5:"value";s:5:"Index";}i:8;a:4:{s:2:"id";i:173;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=173&action=edit";s:3:"url";s:47:"https://numeridanse.local/questions-frequentes/";s:5:"value";s:21:"Questions fréquentes";}i:9;a:4:{s:2:"id";i:169;s:14:"edit_post_link";s:67:"https://numeridanse.local/wp/wp-admin/post.php?post=169&action=edit";s:3:"url";s:47:"https://numeridanse.local/a-propos/partenaires/";s:5:"value";s:11:"Partenaires";}}', 'a:2:{i:0;s:7:"WebSite";i:1;s:12:"Organization";}', NULL, 'a:1:{s:5:"value";b:0;}', 'a:3:{i:0;s:4:"high";i:1;s:6:"medium";i:2;s:4:"good";}', '2024-07-30 13:27:19') ;

#
# End of data contents of table `nd_seopress_content_analysis`
# --------------------------------------------------------



#
# Delete any existing table `nd_seopress_seo_issues`
#

DROP TABLE IF EXISTS `nd_seopress_seo_issues`;


#
# Table structure of table `nd_seopress_seo_issues`
#

CREATE TABLE `nd_seopress_seo_issues` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `post_id` bigint(20) DEFAULT NULL,
  `issue_name` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `issue_desc` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `issue_type` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `issue_priority` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`),
  KEY `idx_post_id` (`post_id`)
) ENGINE=InnoDB AUTO_INCREMENT=157 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_seopress_seo_issues`
#
INSERT INTO `nd_seopress_seo_issues` ( `id`, `post_id`, `issue_name`, `issue_desc`, `issue_type`, `issue_priority`) VALUES
(130, 2, 'json_schemas_not_found', '', 'json_schemas', 'medium'),
(134, 2, 'headings_h1_without_target_kw', '', 'headings', 'high'),
(136, 2, 'headings_h2_without_target_kw', '', 'headings', 'high'),
(139, 2, 'headings_h3_without_target_kw', '', 'headings', 'high'),
(141, 2, 'title_without_target_kw', '', 'title', 'medium'),
(143, 2, 'description_not_custom', '', 'description', 'medium'),
(145, 2, 'og_title_missing', '', 'social', 'high'),
(146, 2, 'og_desc_missing', '', 'social', 'high'),
(148, 2, 'og_img_missing', '', 'social', 'high'),
(149, 2, 'og_url_missing', '', 'social', 'high'),
(150, 2, 'og_sitename_missing', '', 'social', 'high'),
(151, 2, 'x_title_missing', '', 'social', 'high'),
(152, 2, 'x_desc_missing', '', 'social', 'high'),
(153, 2, 'x_img_missing', '', 'social', 'high'),
(154, 2, 'meta_robots_nofollow', '', 'robots', 'high'),
(155, 2, 'img_alt_no_media', '', 'img_alt', 'medium'),
(156, 2, 'canonical_missing', '', 'all_canonical', 'high') ;

#
# End of data contents of table `nd_seopress_seo_issues`
# --------------------------------------------------------



#
# Delete any existing table `nd_seopress_significant_keywords`
#

DROP TABLE IF EXISTS `nd_seopress_significant_keywords`;


#
# Table structure of table `nd_seopress_significant_keywords`
#

CREATE TABLE `nd_seopress_significant_keywords` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `post_id` bigint(20) DEFAULT NULL,
  `word` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `count` int(11) DEFAULT NULL,
  `tf` float DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_word` (`word`)
) ENGINE=InnoDB AUTO_INCREMENT=301 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_seopress_significant_keywords`
#
INSERT INTO `nd_seopress_significant_keywords` ( `id`, `post_id`, `word`, `count`, `tf`) VALUES
(21, 3, 'data', 14, '0.0163551'),
(22, 3, 'suggested', 9, '0.010514'),
(23, 3, 'text', 9, '0.010514'),
(24, 3, 'website', 9, '0.010514'),
(25, 3, 'cookies', 9, '0.010514'),
(26, 3, 'will', 7, '0.00817757'),
(27, 3, 'comments', 6, '0.00700935'),
(28, 3, 'embedded', 6, '0.00700935'),
(29, 3, 'personal', 6, '0.00700935'),
(30, 3, 'address', 5, '0.00584112'),
(31, 3, 'login', 5, '0.00584112'),
(32, 3, 'content', 5, '0.00584112'),
(33, 3, 'leave', 4, '0.0046729'),
(34, 3, 'site', 4, '0.0046729'),
(35, 3, 'browser', 4, '0.0046729'),
(36, 3, 'comment', 4, '0.0046729'),
(37, 3, 'images', 4, '0.0046729'),
(38, 3, 'cookie', 4, '0.0046729'),
(39, 3, 'visitor', 3, '0.00350467'),
(40, 3, 'email', 3, '0.00350467'),
(281, 2, 'will', 2, '0.00961538'),
(282, 2, 'site', 2, '0.00961538'),
(283, 2, 'people', 2, '0.00961538'),
(284, 2, 'this', 2, '0.00961538'),
(285, 2, 'xyz', 2, '0.00961538'),
(286, 2, 'gotham', 2, '0.00961538'),
(287, 2, 'example', 1, '0.00480769'),
(288, 2, 'page', 1, '0.00480769'),
(289, 2, 'blog', 1, '0.00480769'),
(290, 2, 'post', 1, '0.00480769'),
(291, 2, 'stay', 1, '0.00480769'),
(292, 2, 'place', 1, '0.00480769'),
(293, 2, 'navigation', 1, '0.00480769'),
(294, 2, 'themes', 1, '0.00480769'),
(295, 2, 'start', 1, '0.00480769'),
(296, 2, 'introduces', 1, '0.00480769'),
(297, 2, 'potential', 1, '0.00480769'),
(298, 2, 'visitors', 1, '0.00480769'),
(299, 2, 'there', 1, '0.00480769'),
(300, 2, 'bike', 1, '0.00480769') ;

#
# End of data contents of table `nd_seopress_significant_keywords`
# --------------------------------------------------------



#
# Delete any existing table `nd_term_relationships`
#

DROP TABLE IF EXISTS `nd_term_relationships`;


#
# Table structure of table `nd_term_relationships`
#

CREATE TABLE `nd_term_relationships` (
  `object_id` bigint(20) unsigned NOT NULL DEFAULT '0',
  `term_taxonomy_id` bigint(20) unsigned NOT NULL DEFAULT '0',
  `term_order` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`object_id`,`term_taxonomy_id`),
  KEY `term_taxonomy_id` (`term_taxonomy_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_term_relationships`
#
INSERT INTO `nd_term_relationships` ( `object_id`, `term_taxonomy_id`, `term_order`) VALUES
(19, 1, 0),
(21, 1, 0),
(33, 4, 0),
(36, 5, 0),
(37, 6, 0),
(133, 1, 0),
(205, 6, 0),
(206, 6, 0),
(211, 4, 0),
(212, 4, 0),
(213, 5, 0),
(214, 5, 0),
(219, 1, 0),
(224, 7, 0),
(224, 15, 0),
(224, 22, 0),
(224, 28, 0),
(224, 54, 0),
(224, 55, 0),
(224, 56, 0),
(246, 1, 0),
(392, 11, 0),
(392, 14, 0),
(392, 23, 0),
(392, 29, 0),
(392, 58, 0),
(392, 60, 0),
(395, 13, 0),
(395, 15, 0),
(395, 18, 0),
(395, 28, 0),
(396, 7, 0),
(396, 15, 0),
(396, 20, 0),
(396, 27, 0),
(397, 11, 0),
(397, 15, 0),
(397, 24, 0),
(397, 30, 0),
(397, 59, 0),
(404, 8, 0),
(404, 14, 0),
(404, 18, 0),
(404, 26, 0),
(405, 13, 0),
(405, 15, 0),
(405, 20, 0),
(405, 29, 0),
(405, 54, 0),
(405, 55, 0),
(405, 60, 0),
(406, 11, 0),
(406, 15, 0),
(406, 24, 0),
(406, 30, 0),
(406, 53, 0),
(407, 10, 0),
(407, 15, 0),
(407, 23, 0),
(407, 27, 0),
(408, 10, 0),
(408, 14, 0),
(408, 24, 0),
(408, 28, 0),
(408, 53, 0),
(408, 55, 0),
(408, 59, 0),
(409, 9, 0),
(409, 14, 0),
(409, 18, 0),
(409, 27, 0),
(409, 54, 0),
(410, 8, 0),
(410, 14, 0),
(410, 21, 0),
(410, 27, 0),
(411, 7, 0),
(411, 15, 0),
(411, 20, 0),
(411, 29, 0),
(411, 57, 0),
(411, 60, 0),
(440, 1, 0),
(443, 1, 0),
(463, 1, 0),
(471, 1, 0),
(519, 1, 0),
(532, 1, 0),
(614, 1, 0) ;

#
# End of data contents of table `nd_term_relationships`
# --------------------------------------------------------



#
# Delete any existing table `nd_term_taxonomy`
#

DROP TABLE IF EXISTS `nd_term_taxonomy`;


#
# Table structure of table `nd_term_taxonomy`
#

CREATE TABLE `nd_term_taxonomy` (
  `term_taxonomy_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `term_id` bigint(20) unsigned NOT NULL DEFAULT '0',
  `taxonomy` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `parent` bigint(20) unsigned NOT NULL DEFAULT '0',
  `count` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`term_taxonomy_id`),
  UNIQUE KEY `term_id_taxonomy` (`term_id`,`taxonomy`),
  KEY `taxonomy` (`taxonomy`)
) ENGINE=InnoDB AUTO_INCREMENT=70 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_term_taxonomy`
#
INSERT INTO `nd_term_taxonomy` ( `term_taxonomy_id`, `term_id`, `taxonomy`, `description`, `parent`, `count`) VALUES
(1, 1, 'category', '', 0, 0),
(3, 3, 'acf-field-group-category', '', 0, 0),
(4, 4, 'nav_menu', '', 0, 3),
(5, 5, 'nav_menu', '', 0, 3),
(6, 6, 'nav_menu', '', 0, 3),
(7, 7, 'pub_category', '', 0, 3),
(8, 8, 'pub_category', '', 0, 2),
(9, 9, 'pub_category', '', 0, 1),
(10, 10, 'pub_category', '', 0, 2),
(11, 11, 'pub_category', '', 0, 3),
(12, 12, 'pub_category', '', 0, 0),
(13, 13, 'pub_category', '', 0, 2),
(14, 14, 'pub_format', '', 0, 5),
(15, 15, 'pub_format', '', 0, 8),
(16, 16, 'pub_chronology', '', 0, 0),
(17, 17, 'pub_chronology', '', 0, 0),
(18, 18, 'pub_chronology', '', 0, 3),
(19, 19, 'pub_chronology', '', 0, 0),
(20, 20, 'pub_chronology', '', 0, 3),
(21, 21, 'pub_chronology', '', 0, 1),
(22, 22, 'pub_chronology', '', 0, 1),
(23, 23, 'pub_chronology', '', 0, 2),
(24, 24, 'pub_chronology', '', 0, 3),
(25, 25, 'pub_dance_type', '', 0, 0),
(26, 26, 'pub_dance_type', '', 0, 1),
(27, 27, 'pub_dance_type', '', 0, 4),
(28, 28, 'pub_dance_type', '', 0, 3),
(29, 29, 'pub_dance_type', '', 0, 3),
(30, 30, 'pub_dance_type', '', 0, 2),
(35, 35, 'acf-field-group-category', '', 0, 0),
(36, 36, 'pub_credits', '', 0, 0),
(37, 37, 'pub_credits', '', 0, 0),
(38, 38, 'pub_credits', '', 0, 0),
(39, 39, 'pub_credits', '', 0, 0),
(40, 40, 'pub_credits', '', 0, 0),
(41, 41, 'pub_credits', '', 0, 0),
(42, 42, 'pub_credits', '', 0, 0),
(43, 43, 'pub_credits', '', 0, 0),
(44, 44, 'pub_credits', '', 0, 0),
(45, 45, 'pub_credits', '', 0, 0),
(46, 46, 'pub_credits', '', 0, 0),
(47, 47, 'pub_credits', '', 0, 0),
(48, 48, 'pub_credits', '', 0, 0),
(49, 49, 'pub_credits', '', 0, 0),
(50, 50, 'pub_credits', '', 0, 0),
(51, 51, 'pub_credits', '', 0, 0),
(53, 53, 'pub_tag', '', 0, 2),
(54, 54, 'pub_tag', '', 0, 3),
(55, 55, 'pub_tag', '', 0, 3),
(56, 56, 'pub_tag', '', 0, 1),
(57, 57, 'pub_tag', '', 0, 1),
(58, 58, 'pub_tag', '', 0, 1),
(59, 59, 'pub_piece', '', 0, 2),
(60, 60, 'pub_piece', '', 0, 3),
(61, 61, 'pub_credits', '', 0, 0),
(62, 62, 'acf-field-group-category', '', 0, 0),
(63, 63, 'acf-field-group-category', '', 0, 0),
(64, 64, 'acf-field-group-category', '', 0, 0),
(65, 65, 'acf-field-group-category', '', 0, 0),
(69, 69, 'pub_piece', '<p>Plus d\'informations sur Harlequin Floors : <a href="http://uk.harlequinfloors.com/en/" target="_blank" rel="noopener">http://uk.harlequinfloors.com/en/&nbsp;</a></p>\n', 0, 0) ;

#
# End of data contents of table `nd_term_taxonomy`
# --------------------------------------------------------



#
# Delete any existing table `nd_termmeta`
#

DROP TABLE IF EXISTS `nd_termmeta`;


#
# Table structure of table `nd_termmeta`
#

CREATE TABLE `nd_termmeta` (
  `meta_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `term_id` bigint(20) unsigned NOT NULL DEFAULT '0',
  `meta_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`meta_id`),
  KEY `term_id` (`term_id`),
  KEY `meta_key` (`meta_key`(191))
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_termmeta`
#
INSERT INTO `nd_termmeta` ( `meta_id`, `term_id`, `meta_key`, `meta_value`) VALUES
(24, 69, 'acf', 'a:4:{s:25:"admin_drupal_node_id_0_id";s:5:"57035";s:26:"_admin_drupal_node_id_0_id";s:19:"field_66ffe72cbb067";s:20:"admin_drupal_node_id";i:1;s:21:"_admin_drupal_node_id";s:19:"field_66ffe6f4bb066";}'),
(25, 69, 'admin_drupal_node_id_0_id', '57035'),
(26, 69, '_admin_drupal_node_id_0_id', 'field_66ffe72cbb067'),
(27, 69, 'admin_drupal_node_id', '1'),
(28, 69, '_admin_drupal_node_id', 'field_66ffe6f4bb066'),
(29, 69, 'drupal_node_id', '57035') ;

#
# End of data contents of table `nd_termmeta`
# --------------------------------------------------------



#
# Delete any existing table `nd_terms`
#

DROP TABLE IF EXISTS `nd_terms`;


#
# Table structure of table `nd_terms`
#

CREATE TABLE `nd_terms` (
  `term_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `slug` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `term_group` bigint(20) NOT NULL DEFAULT '0',
  `term_order` int(11) DEFAULT NULL,
  PRIMARY KEY (`term_id`),
  KEY `slug` (`slug`(191)),
  KEY `name` (`name`(191))
) ENGINE=InnoDB AUTO_INCREMENT=70 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_terms`
#
INSERT INTO `nd_terms` ( `term_id`, `name`, `slug`, `term_group`, `term_order`) VALUES
(1, 'Uncategorized', 'uncategorized', 0, NULL),
(3, 'Options', 'options', 0, NULL),
(4, 'Pied de page : Menu 1', 'pied-de-page-menu-1', 0, NULL),
(5, 'Pied de page : Menu 2', 'pied-de-page-menu-2', 0, NULL),
(6, 'Pied de page : Menu réglementation', 'pied-de-page-menu-reglementation', 0, NULL),
(7, 'Films de danse', 'films-de-danse', 0, 1),
(8, 'Documentaires', 'documentaires', 0, 2),
(9, 'Interviews', 'interviews', 0, 3),
(10, 'Épreuves de danse', 'epreuves-de-danse', 0, 4),
(11, 'Représentations filmées', 'representations-filmees', 0, 5),
(12, 'Thématiques', 'thematiques', 0, 6),
(13, 'Évènements', 'evenements', 0, 7),
(14, 'Œuvre intégrale', 'oeuvre-integrale', 0, 1),
(15, 'Extrait d\'œuvre', 'extrait-doeuvre', 0, 2),
(16, 'Avant 1950', 'avant-1950', 0, 1),
(17, 'Années 1950', 'annees-1950', 0, 2),
(18, 'Années 1960', 'annees-1960', 0, 3),
(19, 'Années 1970', 'annees-1970', 0, 4),
(20, 'Années 1980', 'annees-1980', 0, 5),
(21, 'Années 1990', 'annees-1990', 0, 6),
(22, 'Années 2000', 'annees-2000', 0, 7),
(23, 'Années 2010', 'annees-2010', 0, 8),
(24, 'Années 2020', 'annees-2020', 0, 9),
(25, 'Jazz', 'jazz', 0, 1),
(26, 'Contemporain', 'contemporain', 0, 2),
(27, 'Classique', 'classique', 0, 3),
(28, 'Danses urbaines', 'danses-urbaines', 0, 4),
(29, 'Danses traditionnelles', 'danses-traditionnelles', 0, 5),
(30, 'Danses de société', 'danses-de-societe', 0, 6),
(35, 'Page', 'page', 0, NULL),
(36, 'Réalisation', 'realisation', 0, NULL),
(37, 'Conception', 'conception', 0, NULL),
(38, 'Année de réalisation', 'annee-de-realisation', 0, NULL),
(39, 'Année de création', 'annee-de-creation', 0, NULL),
(40, 'Production vidéo', 'production-video', 0, NULL),
(41, 'Collection', 'collection', 0, NULL),
(42, 'Chorégraphie', 'choregraphie', 0, NULL),
(43, 'Contributeur', 'contributeur', 0, NULL),
(44, 'Interprétation', 'interpretation', 0, NULL),
(45, 'Scénographie', 'scenographie', 0, NULL),
(46, 'Costumes', 'costumes', 0, NULL),
(47, 'Musique originale', 'musique-originale', 0, NULL),
(48, 'Durée', 'duree', 0, NULL),
(49, 'Lumières', 'lumieres', 0, NULL),
(50, 'Conseil artistique / Dramaturgie', 'conseil-artistique-dramaturgie', 0, NULL),
(51, 'Direction artistique / Conception', 'direction-artistique-conception', 0, NULL),
(53, 'Hip-hop', 'hip-hop', 0, NULL),
(54, 'Jazz', 'jazz', 0, NULL),
(55, 'Street', 'street', 0, NULL),
(56, 'Breakdance', 'breakdance', 0, NULL),
(57, 'Classique', 'classique', 0, NULL),
(58, 'Opéra', 'opera', 0, NULL),
(59, 'Le lac des cygnes', 'le-lac-des-cygnes', 0, NULL),
(60, 'Casse-noisette', 'casse-noisette', 0, NULL),
(61, 'Production de l\'œuvre chorégraphique', 'production-de-loeuvre-choregraphique', 0, NULL),
(62, 'Taxonomie', 'taxonomie', 0, NULL),
(63, 'Flexible', 'flexible', 0, NULL),
(64, 'Utilisateur', 'utilisateur', 0, NULL),
(65, 'Clone', 'clone', 0, NULL),
(69, 'Opéra national de Paris : meet the stars with Harlequin', 'opera-national-de-paris-meet-the-stars-with-harlequin', 0, NULL) ;

#
# End of data contents of table `nd_terms`
# --------------------------------------------------------



#
# Delete any existing table `nd_usermeta`
#

DROP TABLE IF EXISTS `nd_usermeta`;


#
# Table structure of table `nd_usermeta`
#

CREATE TABLE `nd_usermeta` (
  `umeta_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0',
  `meta_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`umeta_id`),
  KEY `user_id` (`user_id`),
  KEY `meta_key` (`meta_key`(191))
) ENGINE=InnoDB AUTO_INCREMENT=161 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_usermeta`
#
INSERT INTO `nd_usermeta` ( `umeta_id`, `user_id`, `meta_key`, `meta_value`) VALUES
(1, 1, 'nickname', 'gaston'),
(2, 1, 'first_name', ''),
(3, 1, 'last_name', ''),
(4, 1, 'description', ''),
(5, 1, 'rich_editing', 'true'),
(6, 1, 'syntax_highlighting', 'true'),
(7, 1, 'comment_shortcuts', 'false'),
(8, 1, 'admin_color', 'fresh'),
(9, 1, 'use_ssl', '0'),
(10, 1, 'show_admin_bar_front', 'true'),
(11, 1, 'locale', ''),
(12, 1, 'nd_capabilities', 'a:1:{s:13:"administrator";b:1;}'),
(13, 1, 'nd_user_level', '10'),
(14, 1, 'dismissed_wp_pointers', ''),
(15, 1, 'show_welcome_panel', '1'),
(16, 1, 'session_tokens', 'a:17:{s:64:"7cc967f2539d1fb37e3ef0871733e6720868325b563ec46f288e9ab0dec23b63";a:4:{s:10:"expiration";i:1728477968;s:2:"ip";s:9:"127.0.0.1";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727268368;}s:64:"9b4e29ee5c4334c98cca3170688051a824a809ebeb78344bddb9ab57d6a73de6";a:4:{s:10:"expiration";i:1729064701;s:2:"ip";s:37:"2001:861:206:e5f0:341f:8641:a448:688a";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727855101;}s:64:"b1610ae1f608a7ef4f0ae922146e65e7abe37a983c853bd193aa8ec3de492bea";a:4:{s:10:"expiration";i:1729064730;s:2:"ip";s:37:"2001:861:206:e5f0:341f:8641:a448:688a";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727855130;}s:64:"49be75f96f7b131038f0f727a374b841c1f0039279e69e8a5f2b8b35f334d8cf";a:4:{s:10:"expiration";i:1729064741;s:2:"ip";s:37:"2001:861:206:e5f0:341f:8641:a448:688a";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727855141;}s:64:"0e8557fecebe7cf127c969969d38f7dae90ab671dd31b8d8fd1c93418373630a";a:4:{s:10:"expiration";i:1729066600;s:2:"ip";s:37:"2001:861:206:e5f0:341f:8641:a448:688a";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727857000;}s:64:"6a63e16f722d5eba39d6f0e6508a2656c8975c9311ebab356c41f2ca1144ca54";a:4:{s:10:"expiration";i:1729066787;s:2:"ip";s:37:"2001:861:206:e5f0:341f:8641:a448:688a";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727857187;}s:64:"e5002942fc21ae94bfffc9be11110723a5185b6fa3060aae7b8d09644b636082";a:4:{s:10:"expiration";i:1729067071;s:2:"ip";s:37:"2001:861:206:e5f0:341f:8641:a448:688a";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727857471;}s:64:"96d8de3986471ad257f10f7682425bdbfa40583f76b3710ca58a0708a5424955";a:4:{s:10:"expiration";i:1729067326;s:2:"ip";s:37:"2001:861:206:e5f0:341f:8641:a448:688a";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727857726;}s:64:"130c58d72d6d6e49f41d784f79f55f1dcf296fd2db277de02ecf6c65b4f39bc4";a:4:{s:10:"expiration";i:1729067408;s:2:"ip";s:37:"2001:861:206:e5f0:341f:8641:a448:688a";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727857808;}s:64:"487601dae3924f09ef708157ef213ebccff2766d1128880a23633692235835f3";a:4:{s:10:"expiration";i:1729067471;s:2:"ip";s:37:"2001:861:206:e5f0:341f:8641:a448:688a";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727857871;}s:64:"7d0516656c6b95b0e85484cc6132b2061281a25c6fc7361416ad9384504434bf";a:4:{s:10:"expiration";i:1729067520;s:2:"ip";s:37:"2001:861:206:e5f0:341f:8641:a448:688a";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727857920;}s:64:"0943e37917d7467197f569e08ee78c031b73711df865c28653f985a2a26366f8";a:4:{s:10:"expiration";i:1729067541;s:2:"ip";s:37:"2001:861:206:e5f0:341f:8641:a448:688a";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727857941;}s:64:"16516a4ee7af4a2ebf951294023ea2696914039306c3696526376f41400fc227";a:4:{s:10:"expiration";i:1729067663;s:2:"ip";s:37:"2001:861:206:e5f0:341f:8641:a448:688a";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727858063;}s:64:"40aa5683d84161c42967473277297380b981b84c4e52d46059a1035e539d0523";a:4:{s:10:"expiration";i:1729155044;s:2:"ip";s:37:"2001:861:206:e5f0:75f9:d9bb:cd79:385d";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727945444;}s:64:"f6c15eba5a2fe1c421420350d1d51e495d039508fb183be783fb00933e464942";a:4:{s:10:"expiration";i:1729155137;s:2:"ip";s:37:"2001:861:206:e5f0:75f9:d9bb:cd79:385d";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727945537;}s:64:"545d2f60823491ad084bacd0147b2148d99b00bf8102795103fa0a1cf796c414";a:4:{s:10:"expiration";i:1729156430;s:2:"ip";s:37:"2001:861:206:e5f0:75f9:d9bb:cd79:385d";s:2:"ua";s:84:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:128.0) Gecko/20100101 Firefox/128.0";s:5:"login";i:1727946830;}s:64:"c88212d967b7be93522712ac4cbbcaff49a48be315d62e07166c4fab61e42d5d";a:4:{s:10:"expiration";i:1729523265;s:2:"ip";s:36:"2001:861:206:e5f0:d5d1:c547:a53b:3a7";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1728313665;}}'),
(17, 1, 'nd_dashboard_quick_press_last_post_id', '4'),
(18, 1, 'nd_user-settings', 'editor=tinymce&libraryContent=browse&posts_list_mode=list'),
(19, 1, 'nd_user-settings-time', '1727706073'),
(20, 1, 'managenav-menuscolumnshidden', 'a:5:{i:0;s:11:"link-target";i:1;s:11:"css-classes";i:2;s:3:"xfn";i:3;s:11:"description";i:4;s:15:"title-attribute";}'),
(21, 1, 'metaboxhidden_nav-menus', 'a:2:{i:0;s:12:"add-post_tag";i:1;s:28:"add-acf-field-group-category";}'),
(22, 1, 'closedpostboxes_page', 'a:2:{i:0;s:12:"seopress_cpt";i:1;s:25:"seopress_content_analysis";}'),
(23, 1, 'metaboxhidden_page', 'a:3:{i:0;s:7:"slugdiv";i:1;s:12:"seopress_cpt";i:2;s:25:"seopress_content_analysis";}'),
(24, 1, 'closedpostboxes_toplevel_page_acf-options-options', 'a:0:{}'),
(25, 1, 'metaboxhidden_toplevel_page_acf-options-options', 'a:0:{}'),
(26, 1, 'nav_menu_recently_edited', '5'),
(27, 1, 'np_visible_posts', 's:250:"a:1:{s:4:"page";a:16:{i:0;s:1:"2";i:1;s:3:"177";i:2;s:3:"181";i:3;s:3:"185";i:4;s:3:"189";i:5;s:3:"193";i:6;s:3:"141";i:7;s:3:"157";i:8;s:3:"161";i:9;s:3:"165";i:10;s:3:"169";i:11;s:3:"173";i:12;s:3:"145";i:13;s:3:"149";i:14;s:3:"150";i:15;s:1:"3";}}";'),
(28, 1, 'user_order_1', '1'),
(29, 1, 'closedpostboxes_publication', 'a:0:{}'),
(30, 1, 'metaboxhidden_publication', 'a:1:{i:0;s:7:"slugdiv";}'),
(31, 2, 'nickname', 'jeanbaptiste'),
(32, 2, 'first_name', ''),
(33, 2, 'last_name', ''),
(34, 2, 'description', ''),
(35, 2, 'rich_editing', 'true'),
(36, 2, 'syntax_highlighting', 'true'),
(37, 2, 'comment_shortcuts', 'false'),
(38, 2, 'admin_color', 'fresh'),
(39, 2, 'use_ssl', '0'),
(40, 2, 'show_admin_bar_front', 'true'),
(41, 2, 'locale', ''),
(42, 2, 'nd_capabilities', 'a:1:{s:13:"administrator";b:1;}'),
(43, 2, 'nd_user_level', '10'),
(44, 2, 'dismissed_wp_pointers', ''),
(45, 1, 'closedpostboxes_playlist', 'a:0:{}'),
(46, 1, 'metaboxhidden_playlist', 'a:1:{i:0;s:7:"slugdiv";}'),
(47, 1, 'acf_user_settings', 'a:2:{s:15:"show_field_keys";s:1:"0";s:19:"post-type-first-run";b:1;}'),
(48, 1, 'manageedit-publicationcolumnshidden', 'a:1:{i:0;s:20:"taxonomy-pub_credits";}'),
(49, 1, 'edit_publication_per_page', '99'),
(50, 1, 'closedpostboxes_acf-field-group', 'a:0:{}'),
(51, 1, 'metaboxhidden_acf-field-group', 'a:1:{i:0;s:7:"slugdiv";}'),
(52, 1, 'manageedit-acf-post-typecolumnshidden', 'a:1:{i:0;s:7:"acf-key";}'),
(53, 1, 'meta-box-order_publication', 'a:5:{s:15:"acf_after_title";s:19:"acf-group_acf_merge";s:4:"side";s:9:"submitdiv";s:6:"normal";s:52:"slugdiv,acfe-wp-custom-fields,acfe-acf-custom-fields";s:8:"advanced";s:0:"";s:5:"side2";s:12:"revisionsdiv";}'),
(54, 1, 'screen_layout_publication', '2'),
(55, 3, 'nickname', 'gaston_user'),
(56, 3, 'first_name', ''),
(57, 3, 'last_name', ''),
(58, 3, 'description', ''),
(59, 3, 'rich_editing', 'true'),
(60, 3, 'syntax_highlighting', 'true'),
(61, 3, 'comment_shortcuts', 'false'),
(62, 3, 'admin_color', 'fresh'),
(63, 3, 'use_ssl', '0'),
(64, 3, 'show_admin_bar_front', 'true'),
(65, 3, 'locale', ''),
(66, 3, 'nd_capabilities', 'a:1:{s:10:"subscriber";b:1;}'),
(67, 3, 'nd_user_level', '0'),
(68, 3, 'dismissed_wp_pointers', ''),
(69, 3, 'session_tokens', 'a:1:{s:64:"e7afd4ea52a0c04da2ba045a590575dbdc9f5899887a51003addd1d6d6c92968";a:4:{s:10:"expiration";i:1728312547;s:2:"ip";s:9:"127.0.0.1";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727102947;}}'),
(70, 3, 'nd_user-settings', 'editor=tinymce'),
(71, 3, 'nd_user-settings-time', '1727102948'),
(72, 3, 'playlist_ids', 'a:1:{i:0;s:3:"549";}'),
(73, 3, '_playlist_ids', 'field_66f3d98b384a0'),
(74, 4, 'nickname', 'guillaume'),
(75, 4, 'first_name', ''),
(76, 4, 'last_name', ''),
(77, 4, 'description', ''),
(78, 4, 'rich_editing', 'true'),
(79, 4, 'syntax_highlighting', 'true'),
(80, 4, 'comment_shortcuts', 'false'),
(81, 4, 'admin_color', 'fresh'),
(82, 4, 'use_ssl', '0'),
(83, 4, 'show_admin_bar_front', 'true'),
(84, 4, 'locale', ''),
(85, 4, 'nd_capabilities', 'a:1:{s:13:"administrator";b:1;}'),
(86, 4, 'nd_user_level', '10'),
(87, 4, 'playlist_ids', ''),
(88, 4, '_playlist_ids', 'field_66f3d98b384a0'),
(89, 4, 'dismissed_wp_pointers', ''),
(90, 5, 'nickname', 'laurent'),
(91, 5, 'first_name', ''),
(92, 5, 'last_name', ''),
(93, 5, 'description', ''),
(94, 5, 'rich_editing', 'true'),
(95, 5, 'syntax_highlighting', 'true'),
(96, 5, 'comment_shortcuts', 'false'),
(97, 5, 'admin_color', 'fresh'),
(98, 5, 'use_ssl', '0'),
(99, 5, 'show_admin_bar_front', 'true'),
(100, 5, 'locale', '') ;
INSERT INTO `nd_usermeta` ( `umeta_id`, `user_id`, `meta_key`, `meta_value`) VALUES
(101, 5, 'nd_capabilities', 'a:1:{s:13:"administrator";b:1;}'),
(102, 5, 'nd_user_level', '10'),
(103, 5, 'playlist_ids', ''),
(104, 5, '_playlist_ids', 'field_66f3d98b384a0'),
(105, 5, 'dismissed_wp_pointers', ''),
(106, 6, 'nickname', 'mateo'),
(107, 6, 'first_name', ''),
(108, 6, 'last_name', ''),
(109, 6, 'description', ''),
(110, 6, 'rich_editing', 'true'),
(111, 6, 'syntax_highlighting', 'true'),
(112, 6, 'comment_shortcuts', 'false'),
(113, 6, 'admin_color', 'fresh'),
(114, 6, 'use_ssl', '0'),
(115, 6, 'show_admin_bar_front', 'true'),
(116, 6, 'locale', ''),
(117, 6, 'nd_capabilities', 'a:1:{s:13:"administrator";b:1;}'),
(118, 6, 'nd_user_level', '10'),
(119, 6, 'playlist_ids', ''),
(120, 6, '_playlist_ids', 'field_66f3d98b384a0'),
(121, 6, 'dismissed_wp_pointers', ''),
(122, 7, 'nickname', 'camille'),
(123, 7, 'first_name', ''),
(124, 7, 'last_name', ''),
(125, 7, 'description', ''),
(126, 7, 'rich_editing', 'true'),
(127, 7, 'syntax_highlighting', 'true'),
(128, 7, 'comment_shortcuts', 'false'),
(129, 7, 'admin_color', 'fresh'),
(130, 7, 'use_ssl', '0'),
(131, 7, 'show_admin_bar_front', 'true'),
(132, 7, 'locale', ''),
(133, 7, 'nd_capabilities', 'a:1:{s:13:"administrator";b:1;}'),
(134, 7, 'nd_user_level', '10'),
(135, 7, 'playlist_ids', ''),
(136, 7, '_playlist_ids', 'field_66f3d98b384a0'),
(137, 7, 'dismissed_wp_pointers', ''),
(138, 2, 'session_tokens', 'a:2:{s:64:"b64cd52c52bb9c651b923e78ab2105b682ac831f191db907d52c67724fc4a5e8";a:4:{s:10:"expiration";i:1729177976;s:2:"ip";s:37:"2001:861:206:e5f0:d94c:78ba:a40a:34f1";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1727968376;}s:64:"80497635b79dc1160dc76752ab9d2829bd24bc4bc4b38e8605d741254e12dc3e";a:4:{s:10:"expiration";i:1729538839;s:2:"ip";s:9:"127.0.0.1";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1728329239;}}'),
(139, 2, 'nd_user-settings', 'editor=tinymce'),
(140, 2, 'nd_user-settings-time', '1727968372'),
(141, 6, 'default_password_nag', ''),
(142, 6, 'session_tokens', 'a:1:{s:64:"caca10467e04b59868e9968b44651bd9eb3fb8b12759d7e6a412ccb0cb57dcb9";a:4:{s:10:"expiration";i:1729258581;s:2:"ip";s:37:"2001:861:ce0:e330:b154:9d44:5b9c:c1b8";s:2:"ua";s:117:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:5:"login";i:1728048981;}}'),
(143, 6, 'nd_user-settings', 'editor=tinymce'),
(144, 6, 'nd_user-settings-time', '1728048982'),
(145, 8, 'nickname', 'gaston_editor'),
(146, 8, 'first_name', ''),
(147, 8, 'last_name', ''),
(148, 8, 'description', ''),
(149, 8, 'rich_editing', 'true'),
(150, 8, 'syntax_highlighting', 'true'),
(151, 8, 'comment_shortcuts', 'false'),
(152, 8, 'admin_color', 'fresh'),
(153, 8, 'use_ssl', '0'),
(154, 8, 'show_admin_bar_front', 'true'),
(155, 8, 'locale', ''),
(156, 8, 'nd_capabilities', 'a:1:{s:6:"editor";b:1;}'),
(157, 8, 'nd_user_level', '7'),
(158, 8, 'playlist_ids', ''),
(159, 8, '_playlist_ids', 'field_66f3d98b384a0'),
(160, 8, 'dismissed_wp_pointers', '') ;

#
# End of data contents of table `nd_usermeta`
# --------------------------------------------------------



#
# Delete any existing table `nd_users`
#

DROP TABLE IF EXISTS `nd_users`;


#
# Table structure of table `nd_users`
#

CREATE TABLE `nd_users` (
  `ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_login` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_pass` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_nicename` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_registered` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `user_activation_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_status` int(11) NOT NULL DEFAULT '0',
  `display_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_order` int(11) DEFAULT '0',
  PRIMARY KEY (`ID`),
  KEY `user_login_key` (`user_login`),
  KEY `user_nicename` (`user_nicename`),
  KEY `user_email` (`user_email`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


#
# Data contents of table `nd_users`
#
INSERT INTO `nd_users` ( `ID`, `user_login`, `user_pass`, `user_nicename`, `user_email`, `user_url`, `user_registered`, `user_activation_key`, `user_status`, `display_name`, `user_order`) VALUES
(1, 'gaston', '$2y$10$X7c0KLFIK0nyPiEESk4cJOD1XhDL7z.A6wSOMsgL7Q2ih1UsA2jnW', 'gaston', '<EMAIL>', 'https://numeridanse.local/wp', '2024-07-15 15:08:04', '', 0, 'gaston', 0),
(2, 'jeanbaptiste', '$2y$10$upRBzp56uhuqIYqfeoEy3.f/z6xVSZteww4V85wRC1zG.8eem6/.i', 'jeanbaptiste', '<EMAIL>', '', '2024-08-01 07:37:59', '', 0, 'jeanbaptiste', 0),
(3, 'gaston_user', '$2y$10$jsq2Otzna49QJYnf2wDGZeZQPvhk4YwVEGO4bSuuoAXBI/qNqU4QK', 'gaston_user', '<EMAIL>', '', '2024-09-23 14:48:37', '', 0, 'gaston_user', 0),
(4, 'guillaume', '$2y$10$WMGwjgPdvMZgEOtA683zAOgo4QUNBqK4eUK6fPYaVLt.xSurTZWOC', 'guillaume', '<EMAIL>', '', '2024-10-02 07:47:15', '', 0, 'guillaume', 0),
(5, 'laurent', '$2y$10$QD2Y07wvGS50QhaEoz1Enud1LlYPioTorADXlcSYDd6juG8tVMrZC', 'laurent', '<EMAIL>', '', '2024-10-02 07:48:51', '', 0, 'laurent', 0),
(6, 'mateo', '$2y$10$F7CHOvr8e2d8pRIT.jUB5uc0lnL8TCXyWQrDmv17XX/a4BQp8y3Ze', 'mateo', '<EMAIL>', '', '2024-10-02 07:49:12', '', 0, 'mateo', 0),
(7, 'camille', '$2y$10$Nz8ztO.CHs7UaF/HQ.Vc/efg.wudXfPjkB3lBde3advcT56hRZ9Gy', 'camille', '<EMAIL>', '', '2024-10-02 08:36:46', '', 0, 'camille', 0),
(8, 'gaston_editor', '$2y$10$MFQICW3WggCcnxXK3rNtaOhNIt5faxW2n6.BWFJjKdjiMcv8KkJbm', 'gaston_editor', '<EMAIL>', '', '2024-10-07 15:10:27', '', 0, 'gaston_editor', 0) ;

#
# End of data contents of table `nd_users`
# --------------------------------------------------------

#
# Add constraints back in and apply any alter data queries.
#

