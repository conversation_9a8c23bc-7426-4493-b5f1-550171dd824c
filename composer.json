{"name": "lesanimals/grahou", "version": "4.0.0", "description": "<PERSON><PERSON><PERSON>", "homepage": "https://lesanimals.digital", "authors": [{"name": "Les Animals", "email": "<EMAIL>", "homepage": "https://github.com/lesanimals"}], "repositories": [{"type": "composer", "url": "https://wpackagist.org", "only": ["wpackagist-plugin/*"]}, {"type": "composer", "url": "https://composer.deliciousbrains.com/2631864A9B3BEF79B036345C6A87E763"}, {"type": "composer", "url": "https://connect.advancedcustomfields.com"}, {"type": "package", "package": {"name": "junaidbhura/acf-extended-pro", "version": "0.9.1", "type": "wordpress-plugin", "dist": {"type": "zip", "url": "https://www.acf-extended.com/"}, "require": {"junaidbhura/composer-wp-pro-plugins": "*"}}}, {"type": "package", "package": {"name": "junaidbhura/polylang-pro", "version": "3.6.6", "type": "wordpress-plugin", "dist": {"type": "zip", "url": "https://www.polylang.pro/"}, "require": {"junaidbhura/composer-wp-pro-plugins": "*"}}}], "require": {"php": ">=8.1", "composer/installers": "^2.2", "vlucas/phpdotenv": "^5.5", "oscarotero/env": "^2.1", "roots/bedrock-autoloader": "^1.0", "roots/bedrock-disallow-indexing": "^2.0", "roots/wordpress": "@stable", "roots/wp-config": "1.0.0", "roots/wp-password-bcrypt": "1.1.0", "rbdwllr/wordpress-salts-generator": "*", "giggsey/libphonenumber-for-php": "*", "wpackagist-plugin/acf-quickedit-fields": "*", "wpengine/advanced-custom-fields-pro": "*", "junaidbhura/acf-extended-pro": "*", "wpackagist-plugin/elasticpress": "*", "wpackagist-plugin/enable-media-replace": "*", "wpackagist-plugin/manage-privacy-options": "*", "wpackagist-plugin/media-library-organizer": "*", "wpackagist-plugin/password-protected": "*", "junaidbhura/polylang-pro": "*", "wpackagist-plugin/safe-svg": "*", "wpackagist-plugin/wp-seopress": "*", "wpackagist-plugin/simple-custom-post-order": "*", "wpackagist-plugin/smtp-mailer": "*", "wpackagist-plugin/spatie-ray": "*", "wpackagist-plugin/user-switching": "*", "wpackagist-plugin/admin-bar-user-switching": "*", "wpackagist-plugin/wordfence": "*", "deliciousbrains-plugin/wp-migrate-db-pro": "*", "wp-media/wp-rocket": "*"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "allow-plugins": {"composer/installers": true, "roots/wordpress-core-installer": true, "junaidbhura/composer-wp-pro-plugins": true}}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"installer-paths": {"web/app/mu-plugins/{$name}/": ["type:wordpress-muplugin"], "web/app/plugins/{$name}/": ["type:wordpress-plugin"]}, "wordpress-install-dir": "web/wp"}}